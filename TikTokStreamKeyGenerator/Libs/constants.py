application = {
    "aid": 1233,
    "app_name": "musical_ly",
    "version_code": "190103",
    "version_name": "19.1.3",
    "ab_version": "19.1.3",
    "build_number": "19.1.3",
    "update_version_code": 2021901030,
    "manifest_version_code": 2021901030,
    "app_version": "19.1.3",
    "version_code": 190103,
    "git_hash": "27690874",
    "release_build": "6e21592_20210415",
    "sig_hash": "3e084e9c0ae40e21f00baf8b2a111091",
    "sdk": "2",
    "sdk_version": "19",
}

locales = [
    {"by": "be-BY"},
    {"bg": "bg-BG"},
    {"es": "ca-ES"},
    {"cz": "cs-CZ"},
    {"dk": "da-DK"},
    {"de": "de-DE"},
    {"gr": "el-GR"},
    {"au": "en-AU"},
    {"us": "en-US"},
    {"fr": "fr-FR"},
    {"hr": "hr-HR"},
    {"it": "it-IT"},
    {"lt": "lt-LT"},
    {"pl": "pl-PL"},
    {"pt": "pt-BR"},
    {"ro": "ro-RO"},
    {"ru": "ru-RU"},
    {"sk": "sk-SK"},
    {"se": "sv-SE"},
    {"il": "iw-IL"},
    {"in": "hi-IN"},
]
devices = [
    {
        "brand": "samsung",
        "model": ["SM-A127F"],
        "resolution": "1467x720",
        "dpi": 300,
        "build": ["RP1A.200720.012"],
        "rom": ["A127FXXU3AUJ5"],
        "board": [
            "universal7884B",
            "mt6768",
            "mt6768",
            "atoll",
            "universal9825",
            "universal7904",
            "universal7904",
            "universal9611",
            "mt6765",
            "msm8953",
            "universal7870",
            "universal9610",
            "universal9610",
            "universal9611",
            "msm8937",
            "universal7870",
            "universal7870",
            "msm8953",
            "universal7570",
            "msm8953",
            "mt6765",
            "mt6739",
            "sm6150",
            "universal9810",
            "msm8998",
            "universal7904",
            "sdm660",
            "universal7885",
            "msm8953",
            "universal7885",
            "msm8937",
            "universal7884B",
            "atoll",
            "universal9820",
            "universal3830",
            "mt6739",
            "sdm845",
            "universal9611",
            "universal3830",
            "universal9820",
            "universal3830",
            "mt6768",
            "sm6150",
            "mt6768",
            "universal9810",
            "universal9611",
            "universal9610",
            "universal7904",
            "kona",
            "msm8937",
            "msm8953",
            "mt6765",
            "msm8937",
            "mt6765",
            "msm8937",
            "bengal",
            "universal9810",
            "msmnile",
            "universal7904",
            "universal2100_r",
        ],
        "core": [
            "exynos7884B",
            "k69v1_64_titan_marmot",
            "k68v1_64_titan",
            "atoll",
            "exynos9825",
            "exynos7904",
            "exynos7904",
            "exynos9611",
            "S96116RA1",
            "msm8953",
            "exynos7870",
            "exynos9610",
            "exynos9610",
            "exynos9611",
            "msm8937",
            "exynos7870",
            "exynos7870",
            "msm8953",
            "exynos7570",
            "QC_Reference_Phone",
            "k65v1_64_bsp_titan_rat",
            "k39tv1_bsp_1g_titan",
            "sm6150",
            "exynos9810",
            "msm8998",
            "exynos7904",
            "sdm660",
            "exynos7885",
            "QC_Reference_Phone",
            "exynos7885",
            "QC_Reference_Phone",
            "exynos7884B",
            "atoll",
            "exynos9820",
            "exynos850",
            "k39tv1_bsp_titan_hamster",
            "sdm845",
            "exynos9611",
            "exynos850",
            "exynos9820",
            "exynos850",
            "k68v1_64_titan",
            "sm6150",
            "k69v1_64_titan_buffalo",
            "exynos9810",
            "exynos9611",
            "exynos9610",
            "exynos7904",
            "kona",
            "QC_Reference_Phone",
            "QC_Reference_Phone",
            "hs03s",
            "QC_Reference_Phone",
            "ot8",
            "QC_Reference_Phone",
            "bengal",
            "exynos9810",
            "msmnile",
            "exynos7904",
            "exynos2100",
        ],
        "device": [
            {"device": "a10", "product": "a10ser"},
            {"device": "a32", "product": "a32ser"},
            {"device": "a31", "product": "a31ser"},
            {"device": "a72q", "product": "a72qnsxx"},
            {"device": "d1", "product": "d1eea"},
            {"device": "a30", "product": "a30ser"},
            {"device": "a30s", "product": "a30sser"},
            {"device": "a51", "product": "a51nsser"},
            {"device": "a10s", "product": "a10sxx"},
            {"device": "a6plte", "product": "a6plteser"},
            {"device": "j6lte", "product": "j6lteser"},
            {"device": "a50", "product": "a50xser"},
            {"device": "a50", "product": "a50ser"},
            {"device": "m31", "product": "m31nsser"},
            {"device": "j6primelte", "product": "j6primelteser"},
            {"device": "j6lte", "product": "j6ltexx"},
            {"device": "a6lte", "product": "a6lteser"},
            {"device": "a20s", "product": "a20sxx"},
            {"device": "j4lte", "product": "j4lteser"},
            {"device": "a02q", "product": "a02qnnser"},
            {"device": "a12", "product": "a12nsser"},
            {"device": "a01core", "product": "a01coreser"},
            {"device": "a71", "product": "a71naxx"},
            {"device": "star2lte", "product": "star2lteser"},
            {"device": "gts4llte", "product": "gts4llteser"},
            {"device": "a40", "product": "a40ser"},
            {"device": "a9y18qlte", "product": "a9y18qlteser"},
            {"device": "a7y18lte", "product": "a7y18ltejt"},
            {"device": "m11q", "product": "m11qnsser"},
            {"device": "a7y18lte", "product": "a7y18lteser"},
            {"device": "gto", "product": "gtoser"},
            {"device": "a20", "product": "a20ser"},
            {"device": "a52q", "product": "a52qnsser"},
            {"device": "beyond0", "product": "beyond0lteser"},
            {"device": "a12s", "product": "a12snsser"},
            {"device": "a02", "product": "a02cisser"},
            {"device": "star2qltesq", "product": "star2qltesq"},
            {"device": "m21", "product": "m21nsser"},
            {"device": "m12", "product": "m12nsser"},
            {"device": "beyond2", "product": "beyond2ltexx"},
            {"device": "a21s", "product": "a21snsser"},
            {"device": "a41", "product": "a41ser"},
            {"device": "a60q", "product": "a60qzh"},
            {"device": "a22", "product": "a22nsser"},
            {"device": "starlte", "product": "starlteser"},
            {"device": "m31s", "product": "m31snsser"},
            {"device": "a50", "product": "a50dd"},
            {"device": "a30", "product": "a30dd"},
            {"device": "x1q", "product": "x1quex"},
            {"device": "a01q", "product": "a01qser"},
            {"device": "a11q", "product": "a11qnsser"},
            {"device": "a03s", "product": "a03snnser"},
            {"device": "gtowifi", "product": "gtowifiser"},
            {"device": "gta7litewifi", "product": "gta7litewifiser"},
            {"device": "m01q", "product": "m01qser"},
            {"device": "gta4l", "product": "gta4lxx"},
            {"device": "crownlte", "product": "crownlteser"},
            {"device": "r5q", "product": "r5qnaxx"},
            {"device": "gta3xl", "product": "gta3xlxx"},
            {"device": "o1s", "product": "o1sxser"},
        ],
        "display_density": "mdpi",
        "os": 10,
    }
]
