import binascii
import gzip
import random
from Crypto.Cipher import AES 


class TTEncrypt:
    __content = []
    __content_raw = []
    CF = 0
    begining = [0x74, 0x63, 0x05, 0x10, 0, 0]
    dword_0 = [
        99,
        124,
        119,
        123,
        242,
        107,
        111,
        197,
        48,
        1,
        103,
        43,
        254,
        215,
        171,
        118,
        202,
        130,
        201,
        125,
        250,
        89,
        71,
        240,
        173,
        212,
        162,
        175,
        156,
        164,
        114,
        192,
        183,
        253,
        147,
        38,
        54,
        63,
        247,
        204,
        52,
        165,
        229,
        241,
        113,
        216,
        49,
        21,
        4,
        199,
        35,
        195,
        24,
        150,
        5,
        154,
        7,
        18,
        128,
        226,
        235,
        39,
        178,
        117,
        9,
        131,
        44,
        26,
        27,
        110,
        90,
        160,
        82,
        59,
        214,
        179,
        41,
        227,
        47,
        132,
        83,
        209,
        0,
        237,
        32,
        252,
        177,
        91,
        106,
        203,
        190,
        57,
        74,
        76,
        88,
        207,
        208,
        239,
        170,
        251,
        67,
        77,
        51,
        133,
        69,
        249,
        2,
        127,
        80,
        60,
        159,
        168,
        81,
        163,
        64,
        143,
        146,
        157,
        56,
        245,
        188,
        182,
        218,
        33,
        16,
        255,
        243,
        210,
        205,
        12,
        19,
        236,
        95,
        151,
        68,
        23,
        196,
        167,
        126,
        61,
        100,
        93,
        25,
        115,
        96,
        129,
        79,
        220,
        34,
        42,
        144,
        136,
        70,
        238,
        184,
        20,
        222,
        94,
        11,
        219,
        224,
        50,
        58,
        10,
        73,
        6,
        36,
        92,
        194,
        211,
        172,
        98,
        145,
        149,
        228,
        121,
        231,
        200,
        55,
        109,
        141,
        213,
        78,
        169,
        108,
        86,
        244,
        234,
        101,
        122,
        174,
        8,
        186,
        120,
        37,
        46,
        28,
        166,
        180,
        198,
        232,
        221,
        116,
        31,
        75,
        189,
        139,
        138,
        112,
        62,
        181,
        102,
        72,
        3,
        246,
        14,
        97,
        53,
        87,
        185,
        134,
        193,
        29,
        158,
        225,
        248,
        152,
        17,
        105,
        217,
        142,
        148,
        155,
        30,
        135,
        233,
        206,
        85,
        40,
        223,
        140,
        161,
        137,
        13,
        191,
        230,
        66,
        104,
        65,
        153,
        45,
        15,
        176,
        84,
        187,
        22,
    ]  # line:258
    dword_1 = [
        16777216,
        33554432,
        67108864,
        134217728,
        268435456,
        536870912,
        1073741824,
        2147483648,
        452984832,
        905969664,
    ]  # line:270
    dword_2 = [
        0,
        235474187,
        470948374,
        303765277,
        941896748,
        908933415,
        607530554,
        708780849,
        1883793496,
        2118214995,
        1817866830,
        1649639237,
        1215061108,
        1181045119,
        1417561698,
        1517767529,
        3767586992,
        4003061179,
        4236429990,
        4069246893,
        3635733660,
        3602770327,
        3299278474,
        3400528769,
        2430122216,
        2664543715,
        2362090238,
        2193862645,
        2835123396,
        2801107407,
        3035535058,
        3135740889,
        3678124923,
        3576870512,
        3341394285,
        3374361702,
        3810496343,
        3977675356,
        4279080257,
        4043610186,
        2876494627,
        2776292904,
        3076639029,
        3110650942,
        2472011535,
        2640243204,
        2403728665,
        2169303058,
        1001089995,
        899835584,
        666464733,
        699432150,
        59727847,
        226906860,
        530400753,
        294930682,
        1273168787,
        1172967064,
        1475418501,
        1509430414,
        1942435775,
        2110667444,
        1876241833,
        1641816226,
        2910219766,
        2743034109,
        2976151520,
        3211623147,
        2505202138,
        2606453969,
        2302690252,
        2269728455,
        3711829422,
        3543599269,
        3240894392,
        3475313331,
        3843699074,
        3943906441,
        4178062228,
        4144047775,
        1306967366,
        1139781709,
        1374988112,
        1610459739,
        1975683434,
        2076935265,
        1775276924,
        1742315127,
        1034867998,
        866637845,
        566021896,
        800440835,
        92987698,
        193195065,
        429456164,
        395441711,
        1984812685,
        2017778566,
        1784663195,
        1683407248,
        1315562145,
        1080094634,
        1383856311,
        1551037884,
        101039829,
        135050206,
        437757123,
        337553864,
        1042385657,
        807962610,
        573804783,
        742039012,
        2531067453,
        2564033334,
        2328828971,
        2227573024,
        2935566865,
        2700099354,
        3001755655,
        3168937228,
        3868552805,
        3902563182,
        4203181171,
        4102977912,
        3736164937,
        3501741890,
        3265478751,
        3433712980,
        1106041591,
        1340463100,
        1576976609,
        1408749034,
        2043211483,
        2009195472,
        1708848333,
        1809054150,
        832877231,
        1068351396,
        766945465,
        599762354,
        159417987,
        126454664,
        361929877,
        463180190,
        2709260871,
        2943682380,
        3178106961,
        3009879386,
        2572697195,
        2538681184,
        2236228733,
        2336434550,
        3509871135,
        3745345300,
        3441850377,
        3274667266,
        3910161971,
        3877198648,
        4110568485,
        4211818798,
        2597806476,
        2497604743,
        2261089178,
        2295101073,
        2733856160,
        2902087851,
        3202437046,
        2968011453,
        3936291284,
        3835036895,
        4136440770,
        4169408201,
        3535486456,
        3702665459,
        3467192302,
        3231722213,
        2051518780,
        1951317047,
        1716890410,
        1750902305,
        1113818384,
        1282050075,
        1584504582,
        1350078989,
        168810852,
        67556463,
        371049330,
        404016761,
        841739592,
        1008918595,
        775550814,
        540080725,
        3969562369,
        3801332234,
        4035489047,
        4269907996,
        3569255213,
        3669462566,
        3366754619,
        3332740144,
        2631065433,
        2463879762,
        2160117071,
        2395588676,
        2767645557,
        2868897406,
        3102011747,
        3069049960,
        202008497,
        33778362,
        270040487,
        504459436,
        875451293,
        975658646,
        675039627,
        641025152,
        2084704233,
        1917518562,
        1615861247,
        1851332852,
        1147550661,
        1248802510,
        1484005843,
        1451044056,
        933301370,
        967311729,
        733156972,
        632953703,
        260388950,
        25965917,
        328671808,
        496906059,
        1206477858,
        1239443753,
        1543208500,
        1441952575,
        2144161806,
        1908694277,
        1675577880,
        1842759443,
        3610369226,
        3644379585,
        3408119516,
        3307916247,
        4011190502,
        3776767469,
        4077384432,
        4245618683,
        2809771154,
        2842737049,
        3144396420,
        3043140495,
        2673705150,
        2438237621,
        2203032232,
        2370213795,
    ]  # line:528
    dword_3 = [
        0,
        185469197,
        370938394,
        487725847,
        741876788,
        657861945,
        975451694,
        824852259,
        1483753576,
        1400783205,
        1315723890,
        1164071807,
        1950903388,
        2135319889,
        1649704518,
        1767536459,
        2967507152,
        3152976349,
        2801566410,
        2918353863,
        2631447780,
        2547432937,
        2328143614,
        2177544179,
        3901806776,
        3818836405,
        4270639778,
        4118987695,
        3299409036,
        3483825537,
        3535072918,
        3652904859,
        2077965243,
        1893020342,
        1841768865,
        1724457132,
        1474502543,
        1559041666,
        1107234197,
        1257309336,
        598438867,
        681933534,
        901210569,
        1052338372,
        261314535,
        77422314,
        428819965,
        310463728,
        3409685355,
        3224740454,
        3710368113,
        3593056380,
        3875770207,
        3960309330,
        4045380933,
        4195456072,
        2471224067,
        2554718734,
        2237133081,
        2388260884,
        3212035895,
        3028143674,
        2842678573,
        2724322336,
        4138563181,
        4255350624,
        3769721975,
        3955191162,
        3667219033,
        3516619604,
        3431546947,
        3347532110,
        2933734917,
        2782082824,
        3099667487,
        3016697106,
        2196052529,
        2313884476,
        2499348523,
        2683765030,
        1179510461,
        1296297904,
        1347548327,
        1533017514,
        1786102409,
        1635502980,
        2087309459,
        2003294622,
        507358933,
        355706840,
        136428751,
        53458370,
        839224033,
        957055980,
        605657339,
        790073846,
        2373340630,
        2256028891,
        2607439820,
        2422494913,
        2706270690,
        2856345839,
        3075636216,
        3160175349,
        3573941694,
        3725069491,
        3273267108,
        3356761769,
        4181598602,
        4063242375,
        4011996048,
        3828103837,
        1033297158,
        915985419,
        730517276,
        545572369,
        296679730,
        446754879,
        129166120,
        213705253,
        1709610350,
        1860738147,
        1945798516,
        2029293177,
        1239331162,
        1120974935,
        1606591296,
        1422699085,
        4148292826,
        4233094615,
        3781033664,
        3931371469,
        3682191598,
        3497509347,
        3446004468,
        3328955385,
        2939266226,
        2755636671,
        3106780840,
        2988687269,
        2198438022,
        2282195339,
        2501218972,
        2652609425,
        1201765386,
        1286567175,
        1371368976,
        1521706781,
        1805211710,
        1620529459,
        2105887268,
        1988838185,
        533804130,
        350174575,
        164439672,
        46346101,
        870912086,
        954669403,
        636813900,
        788204353,
        2358957921,
        2274680428,
        2592523643,
        2441661558,
        2695033685,
        2880240216,
        3065962831,
        3182487618,
        3572145929,
        3756299780,
        3270937875,
        3388507166,
        4174560061,
        4091327024,
        4006521127,
        3854606378,
        1014646705,
        930369212,
        711349675,
        560487590,
        272786309,
        457992840,
        106852767,
        223377554,
        1678381017,
        1862534868,
        1914052035,
        2031621326,
        1211247597,
        1128014560,
        1580087799,
        1428173050,
        32283319,
        182621114,
        401639597,
        486441376,
        768917123,
        651868046,
        1003007129,
        818324884,
        1503449823,
        1385356242,
        1333838021,
        1150208456,
        1973745387,
        2125135846,
        1673061617,
        1756818940,
        2970356327,
        3120694122,
        2802849917,
        2887651696,
        2637442643,
        2520393566,
        2334669897,
        2149987652,
        3917234703,
        3799141122,
        4284502037,
        4100872472,
        3309594171,
        3460984630,
        3545789473,
        3629546796,
        2050466060,
        1899603969,
        1814803222,
        1730525723,
        1443857720,
        1560382517,
        1075025698,
        1260232239,
        575138148,
        692707433,
        878443390,
        1062597235,
        243256656,
        91341917,
        409198410,
        325965383,
        3403100636,
        3252238545,
        3704300486,
        3620022987,
        3874428392,
        3990953189,
        4042459122,
        4227665663,
        2460449204,
        2578018489,
        2226875310,
        2411029155,
        3198115200,
        3046200461,
        2827177882,
        2743944855,
    ]  # line:786
    dword_4 = [
        0,
        218828297,
        437656594,
        387781147,
        875313188,
        958871085,
        775562294,
        590424639,
        1750626376,
        1699970625,
        1917742170,
        2135253587,
        1551124588,
        1367295589,
        1180849278,
        1265195639,
        3501252752,
        3720081049,
        3399941250,
        3350065803,
        3835484340,
        3919042237,
        4270507174,
        4085369519,
        3102249176,
        3051593425,
        2734591178,
        2952102595,
        2361698556,
        2177869557,
        2530391278,
        2614737639,
        3145456443,
        3060847922,
        2708326185,
        2892417312,
        2404901663,
        2187128086,
        2504130317,
        2555048196,
        3542330227,
        3727205754,
        3375740769,
        3292445032,
        3876557655,
        3926170974,
        4246310725,
        4027744588,
        1808481195,
        1723872674,
        1910319033,
        2094410160,
        1608975247,
        1391201670,
        1173430173,
        1224348052,
        59984867,
        244860394,
        428169201,
        344873464,
        935293895,
        984907214,
        766078933,
        547512796,
        1844882806,
        1627235199,
        2011214180,
        2062270317,
        1507497298,
        1423022939,
        1137477952,
        1321699145,
        95345982,
        145085239,
        532201772,
        313773861,
        830661914,
        1015671571,
        731183368,
        648017665,
        3175501286,
        2957853679,
        2807058932,
        2858115069,
        2305455554,
        2220981195,
        2474404304,
        2658625497,
        3575528878,
        3625268135,
        3473416636,
        3254988725,
        3778151818,
        3963161475,
        4213447064,
        4130281361,
        3599595085,
        3683022916,
        3432737375,
        3247465558,
        3802222185,
        4020912224,
        4172763771,
        4122762354,
        3201631749,
        3017672716,
        2764249623,
        2848461854,
        2331590177,
        2280796200,
        2431590963,
        2648976442,
        104699613,
        188127444,
        472615631,
        287343814,
        840019705,
        1058709744,
        671593195,
        621591778,
        1852171925,
        1668212892,
        1953757831,
        2037970062,
        1514790577,
        1463996600,
        1080017571,
        1297403050,
        3673637356,
        3623636965,
        3235995134,
        3454686199,
        4007360968,
        3822090177,
        4107101658,
        4190530515,
        2997825956,
        3215212461,
        2830708150,
        2779915199,
        2256734592,
        2340947849,
        2627016082,
        2443058075,
        172466556,
        122466165,
        273792366,
        492483431,
        1047239000,
        861968209,
        612205898,
        695634755,
        1646252340,
        1863638845,
        2013908262,
        1963115311,
        1446242576,
        1530455833,
        1277555970,
        1093597963,
        1636604631,
        1820824798,
        2073724613,
        1989249228,
        1436590835,
        1487645946,
        1337376481,
        1119727848,
        164948639,
        81781910,
        331544205,
        516552836,
        1039717051,
        821288114,
        669961897,
        719700128,
        2973530695,
        3157750862,
        2871682645,
        2787207260,
        2232435299,
        2283490410,
        2667994737,
        2450346104,
        3647212047,
        3564045318,
        3279033885,
        3464042516,
        3980931627,
        3762502690,
        4150144569,
        4199882800,
        3070356634,
        3121275539,
        2904027272,
        2686254721,
        2200818878,
        2384911031,
        2570832044,
        2486224549,
        3747192018,
        3528626907,
        3310321856,
        3359936201,
        3950355702,
        3867060991,
        4049844452,
        4234721005,
        1739656202,
        1790575107,
        2108100632,
        1890328081,
        1402811438,
        1586903591,
        1233856572,
        1149249077,
        266959938,
        48394827,
        369057872,
        418672217,
        1002783846,
        919489135,
        567498868,
        752375421,
        209336225,
        24197544,
        376187827,
        459744698,
        945164165,
        895287692,
        574624663,
        793451934,
        1679968233,
        1764313568,
        2117360635,
        1933530610,
        1343127501,
        1560637892,
        1243112415,
        1192455638,
        3704280881,
        3519142200,
        3336358691,
        3419915562,
        3907448597,
        3857572124,
        4075877127,
        4294704398,
        3029510009,
        3113855344,
        2927934315,
        2744104290,
        2159976285,
        2377486676,
        2594734927,
        2544078150,
    ]  # line:1044
    dword_5 = [
        0,
        151849742,
        303699484,
        454499602,
        607398968,
        758720310,
        908999204,
        1059270954,
        1214797936,
        1097159550,
        1517440620,
        1400849762,
        1817998408,
        1699839814,
        2118541908,
        2001430874,
        2429595872,
        2581445614,
        2194319100,
        2345119218,
        3034881240,
        3186202582,
        2801699524,
        2951971274,
        3635996816,
        3518358430,
        3399679628,
        3283088770,
        4237083816,
        4118925222,
        4002861748,
        3885750714,
        1002142683,
        850817237,
        698445255,
        548169417,
        529487843,
        377642221,
        227885567,
        77089521,
        1943217067,
        2061379749,
        1640576439,
        1757691577,
        1474760595,
        1592394909,
        1174215055,
        1290801793,
        2875968315,
        2724642869,
        3111247143,
        2960971305,
        2405426947,
        2253581325,
        2638606623,
        2487810577,
        3808662347,
        3926825029,
        4044981591,
        4162096729,
        3342319475,
        3459953789,
        3576539503,
        3693126241,
        1986918061,
        2137062819,
        1685577905,
        1836772287,
        1381620373,
        1532285339,
        1078185097,
        1229899655,
        1040559837,
        923313619,
        740276417,
        621982671,
        439452389,
        322734571,
        137073913,
        19308535,
        3871163981,
        4021308739,
        4104605777,
        4255800159,
        3263785589,
        3414450555,
        3499326569,
        3651041127,
        2933202493,
        2815956275,
        3167684641,
        3049390895,
        2330014213,
        2213296395,
        2566595609,
        2448830231,
        1305906550,
        1155237496,
        1607244650,
        1455525988,
        1776460110,
        1626319424,
        2079897426,
        1928707164,
        96392454,
        213114376,
        396673818,
        514443284,
        562755902,
        679998000,
        865136418,
        983426092,
        3708173718,
        3557504664,
        3474729866,
        3323011204,
        4180808110,
        4030667424,
        3945269170,
        3794078908,
        2507040230,
        2623762152,
        2272556026,
        2390325492,
        2975484382,
        3092726480,
        2738905026,
        2857194700,
        3973773121,
        3856137295,
        4274053469,
        4157467219,
        3371096953,
        3252932727,
        3673476453,
        3556361835,
        2763173681,
        2915017791,
        3064510765,
        3215307299,
        2156299017,
        2307622919,
        2459735317,
        2610011675,
        2081048481,
        1963412655,
        1846563261,
        1729977011,
        1480485785,
        1362321559,
        1243905413,
        1126790795,
        878845905,
        1030690015,
        645401037,
        796197571,
        274084841,
        425408743,
        38544885,
        188821243,
        3613494426,
        3731654548,
        3313212038,
        3430322568,
        4082475170,
        4200115116,
        3780097726,
        3896688048,
        2668221674,
        2516901860,
        2366882550,
        2216610296,
        3141400786,
        2989552604,
        2837966542,
        2687165888,
        1202797690,
        1320957812,
        1437280870,
        1554391400,
        1669664834,
        1787304780,
        1906247262,
        2022837584,
        265905162,
        114585348,
        499347990,
        349075736,
        736970802,
        585122620,
        972512814,
        821712160,
        2595684844,
        2478443234,
        2293045232,
        2174754046,
        3196267988,
        3079546586,
        2895723464,
        2777952454,
        3537852828,
        3687994002,
        3234156416,
        3385345166,
        4142626212,
        4293295786,
        3841024952,
        3992742070,
        174567692,
        57326082,
        410887952,
        292596766,
        777231668,
        660510266,
        1011452712,
        893681702,
        1108339068,
        1258480242,
        1343618912,
        1494807662,
        1715193156,
        1865862730,
        1948373848,
        2100090966,
        2701949495,
        2818666809,
        3004591147,
        3122358053,
        2235061775,
        2352307457,
        2535604243,
        2653899549,
        3915653703,
        3764988233,
        4219352155,
        4067639125,
        3444575871,
        3294430577,
        3746175075,
        3594982253,
        836553431,
        953270745,
        600235211,
        718002117,
        367585007,
        484830689,
        133361907,
        251657213,
        2041877159,
        1891211689,
        1806599355,
        1654886325,
        1568718495,
        1418573201,
        1335535747,
        1184342925,
    ]  # line:1302
    dword_6 = [
        3328402341,
        4168907908,
        4000806809,
        4135287693,
        4294111757,
        3597364157,
        3731845041,
        2445657428,
        1613770832,
        33620227,
        3462883241,
        1445669757,
        3892248089,
        3050821474,
        1303096294,
        3967186586,
        2412431941,
        528646813,
        2311702848,
        4202528135,
        4026202645,
        2992200171,
        2387036105,
        4226871307,
        1101901292,
        3017069671,
        1604494077,
        1169141738,
        597466303,
        1403299063,
        3832705686,
        2613100635,
        1974974402,
        3791519004,
        1033081774,
        1277568618,
        1815492186,
        2118074177,
        4126668546,
        2211236943,
        1748251740,
        1369810420,
        3521504564,
        4193382664,
        3799085459,
        2883115123,
        1647391059,
        706024767,
        134480908,
        2512897874,
        1176707941,
        2646852446,
        806885416,
        932615841,
        168101135,
        798661301,
        235341577,
        605164086,
        461406363,
        3756188221,
        3454790438,
        1311188841,
        2142417613,
        3933566367,
        302582043,
        495158174,
        1479289972,
        874125870,
        907746093,
        3698224818,
        3025820398,
        1537253627,
        2756858614,
        1983593293,
        3084310113,
        2108928974,
        1378429307,
        3722699582,
        1580150641,
        327451799,
        2790478837,
        3117535592,
        0,
        3253595436,
        1075847264,
        3825007647,
        2041688520,
        3059440621,
        3563743934,
        2378943302,
        1740553945,
        1916352843,
        2487896798,
        2555137236,
        2958579944,
        2244988746,
        3151024235,
        3320835882,
        1336584933,
        3992714006,
        2252555205,
        2588757463,
        1714631509,
        293963156,
        2319795663,
        3925473552,
        67240454,
        4269768577,
        2689618160,
        2017213508,
        631218106,
        1269344483,
        2723238387,
        1571005438,
        2151694528,
        93294474,
        1066570413,
        563977660,
        1882732616,
        4059428100,
        1673313503,
        2008463041,
        2950355573,
        1109467491,
        537923632,
        3858759450,
        4260623118,
        3218264685,
        2177748300,
        403442708,
        638784309,
        3287084079,
        3193921505,
        899127202,
        2286175436,
        773265209,
        2479146071,
        1437050866,
        4236148354,
        2050833735,
        3362022572,
        3126681063,
        840505643,
        3866325909,
        3227541664,
        427917720,
        2655997905,
        2749160575,
        1143087718,
        1412049534,
        999329963,
        193497219,
        2353415882,
        3354324521,
        1807268051,
        672404540,
        2816401017,
        3160301282,
        369822493,
        2916866934,
        3688947771,
        1681011286,
        1949973070,
        336202270,
        2454276571,
        201721354,
        1210328172,
        3093060836,
        2680341085,
        3184776046,
        1135389935,
        3294782118,
        965841320,
        831886756,
        3554993207,
        4068047243,
        3588745010,
        2345191491,
        1849112409,
        3664604599,
        26054028,
        2983581028,
        2622377682,
        1235855840,
        3630984372,
        2891339514,
        4092916743,
        3488279077,
        3395642799,
        4101667470,
        1202630377,
        268961816,
        1874508501,
        4034427016,
        1243948399,
        1546530418,
        941366308,
        1470539505,
        1941222599,
        2546386513,
        3421038627,
        2715671932,
        3899946140,
        1042226977,
        2521517021,
        1639824860,
        227249030,
        260737669,
        3765465232,
        2084453954,
        1907733956,
        3429263018,
        2420656344,
        100860677,
        4160157185,
        470683154,
        3261161891,
        1781871967,
        2924959737,
        1773779408,
        394692241,
        2579611992,
        974986535,
        664706745,
        3655459128,
        3958962195,
        731420851,
        571543859,
        3530123707,
        2849626480,
        126783113,
        865375399,
        765172662,
        1008606754,
        361203602,
        3387549984,
        2278477385,
        2857719295,
        1344809080,
        2782912378,
        59542671,
        1503764984,
        160008576,
        437062935,
        1707065306,
        3622233649,
        2218934982,
        3496503480,
        2185314755,
        697932208,
        1512910199,
        504303377,
        2075177163,
        2824099068,
        1841019862,
        739644986,
    ]  # line:1560
    dword_7 = [
        2781242211,
        2230877308,
        2582542199,
        2381740923,
        234877682,
        3184946027,
        2984144751,
        1418839493,
        1348481072,
        50462977,
        2848876391,
        2102799147,
        434634494,
        1656084439,
        3863849899,
        2599188086,
        1167051466,
        2636087938,
        1082771913,
        2281340285,
        368048890,
        3954334041,
        3381544775,
        201060592,
        3963727277,
        1739838676,
        4250903202,
        3930435503,
        3206782108,
        4149453988,
        2531553906,
        1536934080,
        3262494647,
        484572669,
        2923271059,
        1783375398,
        1517041206,
        1098792767,
        49674231,
        1334037708,
        1550332980,
        4098991525,
        886171109,
        150598129,
        2481090929,
        1940642008,
        1398944049,
        1059722517,
        201851908,
        1385547719,
        1699095331,
        1587397571,
        674240536,
        2704774806,
        252314885,
        3039795866,
        151914247,
        908333586,
        2602270848,
        1038082786,
        651029483,
        1766729511,
        3447698098,
        2682942837,
        454166793,
        2652734339,
        1951935532,
        775166490,
        758520603,
        3000790638,
        4004797018,
        4217086112,
        4137964114,
        1299594043,
        1639438038,
        3464344499,
        2068982057,
        1054729187,
        1901997871,
        2534638724,
        4121318227,
        1757008337,
        0,
        750906861,
        1614815264,
        535035132,
        3363418545,
        3988151131,
        3201591914,
        1183697867,
        3647454910,
        1265776953,
        3734260298,
        3566750796,
        3903871064,
        1250283471,
        1807470800,
        717615087,
        3847203498,
        384695291,
        3313910595,
        3617213773,
        1432761139,
        2484176261,
        3481945413,
        283769337,
        100925954,
        2180939647,
        4037038160,
        1148730428,
        3123027871,
        3813386408,
        4087501137,
        4267549603,
        3229630528,
        2315620239,
        2906624658,
        3156319645,
        1215313976,
        82966005,
        3747855548,
        3245848246,
        1974459098,
        1665278241,
        807407632,
        451280895,
        251524083,
        1841287890,
        1283575245,
        337120268,
        891687699,
        801369324,
        3787349855,
        2721421207,
        3431482436,
        959321879,
        1469301956,
        4065699751,
        2197585534,
        1199193405,
        2898814052,
        3887750493,
        724703513,
        2514908019,
        2696962144,
        2551808385,
        3516813135,
        2141445340,
        1715741218,
        2119445034,
        2872807568,
        2198571144,
        3398190662,
        700968686,
        3547052216,
        1009259540,
        2041044702,
        3803995742,
        487983883,
        1991105499,
        1004265696,
        1449407026,
        1316239930,
        504629770,
        3683797321,
        168560134,
        1816667172,
        3837287516,
        1570751170,
        1857934291,
        4014189740,
        2797888098,
        2822345105,
        2754712981,
        936633572,
        2347923833,
        852879335,
        1133234376,
        1500395319,
        3084545389,
        2348912013,
        1689376213,
        3533459022,
        3762923945,
        3034082412,
        4205598294,
        133428468,
        634383082,
        2949277029,
        2398386810,
        3913789102,
        403703816,
        3580869306,
        2297460856,
        1867130149,
        1918643758,
        607656988,
        4049053350,
        3346248884,
        1368901318,
        600565992,
        2090982877,
        2632479860,
        557719327,
        3717614411,
        3697393085,
        2249034635,
        2232388234,
        2430627952,
        1115438654,
        3295786421,
        2865522278,
        3633334344,
        84280067,
        33027830,
        303828494,
        2747425121,
        1600795957,
        4188952407,
        3496589753,
        2434238086,
        1486471617,
        658119965,
        3106381470,
        953803233,
        334231800,
        3005978776,
        857870609,
        3151128937,
        1890179545,
        2298973838,
        2805175444,
        3056442267,
        574365214,
        2450884487,
        550103529,
        1233637070,
        4289353045,
        2018519080,
        2057691103,
        2399374476,
        4166623649,
        2148108681,
        387583245,
        3664101311,
        836232934,
        3330556482,
        3100665960,
        3280093505,
        2955516313,
        2002398509,
        287182607,
        3413881008,
        4238890068,
        3597515707,
        975967766,
    ]  # line:1818
    dword_8 = [
        1671808611,
        2089089148,
        2006576759,
        2072901243,
        4061003762,
        1807603307,
        1873927791,
        3310653893,
        810573872,
        16974337,
        1739181671,
        729634347,
        4263110654,
        3613570519,
        2883997099,
        1989864566,
        3393556426,
        2191335298,
        3376449993,
        2106063485,
        4195741690,
        1508618841,
        1204391495,
        4027317232,
        2917941677,
        3563566036,
        2734514082,
        2951366063,
        2629772188,
        2767672228,
        1922491506,
        3227229120,
        3082974647,
        4246528509,
        2477669779,
        644500518,
        911895606,
        1061256767,
        4144166391,
        3427763148,
        878471220,
        2784252325,
        3845444069,
        4043897329,
        1905517169,
        3631459288,
        827548209,
        356461077,
        67897348,
        3344078279,
        593839651,
        3277757891,
        405286936,
        2527147926,
        84871685,
        2595565466,
        118033927,
        305538066,
        2157648768,
        3795705826,
        3945188843,
        661212711,
        2999812018,
        1973414517,
        152769033,
        2208177539,
        745822252,
        439235610,
        455947803,
        1857215598,
        1525593178,
        2700827552,
        1391895634,
        994932283,
        3596728278,
        3016654259,
        695947817,
        3812548067,
        795958831,
        2224493444,
        1408607827,
        3513301457,
        0,
        3979133421,
        543178784,
        4229948412,
        2982705585,
        1542305371,
        1790891114,
        3410398667,
        3201918910,
        961245753,
        1256100938,
        1289001036,
        1491644504,
        3477767631,
        3496721360,
        4012557807,
        2867154858,
        4212583931,
        1137018435,
        1305975373,
        861234739,
        2241073541,
        1171229253,
        4178635257,
        33948674,
        2139225727,
        1357946960,
        1011120188,
        2679776671,
        2833468328,
        1374921297,
        2751356323,
        1086357568,
        2408187279,
        2460827538,
        2646352285,
        944271416,
        4110742005,
        3168756668,
        3066132406,
        3665145818,
        560153121,
        271589392,
        4279952895,
        4077846003,
        3530407890,
        3444343245,
        202643468,
        322250259,
        3962553324,
        1608629855,
        2543990167,
        1154254916,
        389623319,
        3294073796,
        2817676711,
        2122513534,
        1028094525,
        1689045092,
        1575467613,
        422261273,
        1939203699,
        1621147744,
        2174228865,
        1339137615,
        3699352540,
        577127458,
        712922154,
        2427141008,
        2290289544,
        1187679302,
        3995715566,
        3100863416,
        339486740,
        3732514782,
        1591917662,
        186455563,
        3681988059,
        3762019296,
        844522546,
        978220090,
        169743370,
        1239126601,
        101321734,
        611076132,
        1558493276,
        3260915650,
        3547250131,
        2901361580,
        1655096418,
        2443721105,
        2510565781,
        3828863972,
        2039214713,
        3878868455,
        3359869896,
        928607799,
        1840765549,
        2374762893,
        3580146133,
        1322425422,
        2850048425,
        1823791212,
        1459268694,
        4094161908,
        3928346602,
        1706019429,
        2056189050,
        2934523822,
        135794696,
        3134549946,
        2022240376,
        628050469,
        779246638,
        472135708,
        2800834470,
        3032970164,
        3327236038,
        3894660072,
        3715932637,
        1956440180,
        522272287,
        1272813131,
        3185336765,
        2340818315,
        2323976074,
        1888542832,
        1044544574,
        3049550261,
        1722469478,
        1222152264,
        50660867,
        4127324150,
        236067854,
        1638122081,
        895445557,
        1475980887,
        3117443513,
        2257655686,
        3243809217,
        489110045,
        2662934430,
        3778599393,
        4162055160,
        2561878936,
        288563729,
        1773916777,
        3648039385,
        2391345038,
        2493985684,
        2612407707,
        505560094,
        2274497927,
        3911240169,
        3460925390,
        1442818645,
        678973480,
        3749357023,
        2358182796,
        2717407649,
        2306869641,
        219617805,
        3218761151,
        3862026214,
        1120306242,
        1756942440,
        1103331905,
        2578459033,
        762796589,
        252780047,
        2966125488,
        1425844308,
        3151392187,
        372911126,
    ]  # line:2076
    dword_9 = [
        1667474886,
        2088535288,
        2004326894,
        2071694838,
        4075949567,
        1802223062,
        1869591006,
        3318043793,
        808472672,
        16843522,
        1734846926,
        724270422,
        4278065639,
        3621216949,
        2880169549,
        1987484396,
        3402253711,
        2189597983,
        3385409673,
        2105378810,
        4210693615,
        1499065266,
        1195886990,
        4042263547,
        2913856577,
        3570689971,
        2728590687,
        2947541573,
        2627518243,
        2762274643,
        1920112356,
        3233831835,
        3082273397,
        4261223649,
        2475929149,
        640051788,
        909531756,
        1061110142,
        4160160501,
        3435941763,
        875846760,
        2779116625,
        3857003729,
        4059105529,
        1903268834,
        3638064043,
        825316194,
        353713962,
        67374088,
        3351728789,
        589522246,
        3284360861,
        404236336,
        2526454071,
        84217610,
        2593830191,
        117901582,
        303183396,
        2155911963,
        3806477791,
        3958056653,
        656894286,
        2998062463,
        1970642922,
        151591698,
        2206440989,
        741110872,
        437923380,
        454765878,
        1852748508,
        1515908788,
        2694904667,
        1381168804,
        993742198,
        3604373943,
        3014905469,
        690584402,
        3823320797,
        791638366,
        2223281939,
        1398011302,
        3520161977,
        0,
        3991743681,
        538992704,
        4244381667,
        2981218425,
        1532751286,
        1785380564,
        3419096717,
        3200178535,
        960056178,
        1246420628,
        1280103576,
        1482221744,
        3486468741,
        3503319995,
        4025428677,
        2863326543,
        4227536621,
        1128514950,
        1296947098,
        859002214,
        2240123921,
        1162203018,
        4193849577,
        33687044,
        2139062782,
        1347481760,
        1010582648,
        2678045221,
        2829640523,
        1364325282,
        2745433693,
        1077985408,
        2408548869,
        2459086143,
        2644360225,
        943212656,
        4126475505,
        3166494563,
        3065430391,
        3671750063,
        555836226,
        269496352,
        4294908645,
        4092792573,
        3537006015,
        3452783745,
        202118168,
        320025894,
        3974901699,
        1600119230,
        2543297077,
        1145359496,
        387397934,
        3301201811,
        2812801621,
        2122220284,
        1027426170,
        1684319432,
        1566435258,
        421079858,
        1936954854,
        1616945344,
        2172753945,
        1330631070,
        3705438115,
        572679748,
        707427924,
        2425400123,
        2290647819,
        1179044492,
        4008585671,
        3099120491,
        336870440,
        3739122087,
        1583276732,
        185277718,
        3688593069,
        3772791771,
        842159716,
        976899700,
        168435220,
        1229577106,
        101059084,
        606366792,
        1549591736,
        3267517855,
        3553849021,
        2897014595,
        1650632388,
        2442242105,
        2509612081,
        3840161747,
        2038008818,
        3890688725,
        3368567691,
        926374254,
        1835907034,
        2374863873,
        3587531953,
        1313788572,
        2846482505,
        1819063512,
        1448540844,
        4109633523,
        3941213647,
        1701162954,
        2054852340,
        2930698567,
        134748176,
        3132806511,
        2021165296,
        623210314,
        774795868,
        471606328,
        2795958615,
        3031746419,
        3334885783,
        3907527627,
        3722280097,
        1953799400,
        522133822,
        1263263126,
        3183336545,
        2341176845,
        2324333839,
        1886425312,
        1044267644,
        3048588401,
        1718004428,
        1212733584,
        50529542,
        4143317495,
        235803164,
        1633788866,
        892690282,
        1465383342,
        3115962473,
        2256965911,
        3250673817,
        488449850,
        2661202215,
        3789633753,
        4177007595,
        2560144171,
        286339874,
        1768537042,
        3654906025,
        2391705863,
        2492770099,
        2610673197,
        505291324,
        2273808917,
        3924369609,
        3469625735,
        1431699370,
        673740880,
        3755965093,
        2358021891,
        2711746649,
        2307489801,
        218961690,
        3217021541,
        3873845719,
        1111672452,
        1751693520,
        1094828930,
        2576986153,
        757954394,
        252645662,
        2964376443,
        1414855848,
        3149649517,
        370555436,
    ]  # line:2334
    LIST_6B0 = [
        4089235720,
        1779033703,
        2227873595,
        3144134277,
        4271175723,
        1013904242,
        1595750129,
        2773480762,
        2917565137,
        1359893119,
        725511199,
        2600822924,
        4215389547,
        528734635,
        327033209,
        1541459225,
    ]  # line:2352
    ord_list = [
        77,
        212,
        194,
        230,
        184,
        49,
        98,
        9,
        14,
        82,
        179,
        199,
        166,
        115,
        59,
        164,
        28,
        178,
        70,
        43,
        130,
        154,
        181,
        138,
        25,
        107,
        57,
        219,
        87,
        23,
        117,
        36,
        244,
        155,
        175,
        127,
        8,
        232,
        214,
        141,
        38,
        167,
        46,
        55,
        193,
        169,
        90,
        47,
        31,
        5,
        165,
        24,
        146,
        174,
        242,
        148,
        151,
        50,
        182,
        42,
        56,
        170,
        221,
        88,
    ]  # line:2418
    rodata = [
        3609767458,
        1116352408,
        602891725,
        1899447441,
        3964484399,
        3049323471,
        2173295548,
        3921009573,
        4081628472,
        961987163,
        3053834265,
        1508970993,
        2937671579,
        2453635748,
        3664609560,
        2870763221,
        2734883394,
        3624381080,
        1164996542,
        310598401,
        1323610764,
        607225278,
        3590304994,
        1426881987,
        4068182383,
        1925078388,
        991336113,
        2162078206,
        633803317,
        2614888103,
        3479774868,
        3248222580,
        2666613458,
        3835390401,
        944711139,
        4022224774,
        2341262773,
        264347078,
        2007800933,
        604807628,
        1495990901,
        770255983,
        1856431235,
        1249150122,
        3175218132,
        1555081692,
        2198950837,
        1996064986,
        3999719339,
        2554220882,
        766784016,
        2821834349,
        2566594879,
        2952996808,
        3203337956,
        3210313671,
        1034457026,
        3336571891,
        2466948901,
        3584528711,
        3758326383,
        113926993,
        168717936,
        338241895,
        1188179964,
        666307205,
        1546045734,
        773529912,
        1522805485,
        1294757372,
        2643833823,
        1396182291,
        2343527390,
        1695183700,
        1014477480,
        1986661051,
        1206759142,
        2177026350,
        344077627,
        2456956037,
        1290863460,
        2730485921,
        3158454273,
        2820302411,
        3505952657,
        3259730800,
        106217008,
        3345764771,
        3606008344,
        3516065817,
        1432725776,
        3600352804,
        1467031594,
        4094571909,
        851169720,
        275423344,
        3100823752,
        430227734,
        1363258195,
        506948616,
        3750685593,
        659060556,
        3785050280,
        883997877,
        3318307427,
        958139571,
        3812723403,
        1322822218,
        2003034995,
        1537002063,
        3602036899,
        1747873779,
        1575990012,
        1955562222,
        1125592928,
        2024104815,
        2716904306,
        2227730452,
        442776044,
        2361852424,
        593698344,
        2428436474,
        3733110249,
        2756734187,
        2999351573,
        3204031479,
        3815920427,
        3329325298,
        3928383900,
        3391569614,
        566280711,
        3515267271,
        3454069534,
        3940187606,
        4000239992,
        4118630271,
        1914138554,
        116418474,
        2731055270,
        174292421,
        3203993006,
        289380356,
        320620315,
        460393269,
        587496836,
        685471733,
        1086792851,
        852142971,
        365543100,
        1017036298,
        2618297676,
        1126000580,
        3409855158,
        1288033470,
        4234509866,
        1501505948,
        987167468,
        1607167915,
        1246189591,
        1816402316,
    ]  # line:2580
    list_9C8 = []

    def encrypt(self, data):
        headers = [31, 139, 8, 0, 0, 0, 0, 0, 0, 0]
        data = gzip.compress(bytes(data.encode("latin-1")), compresslevel=9, mtime=0)
        data = list(data)
        self.setData(data)
        for i in range(len(headers)):
            self.__content[i] = headers[i]
        list_0B0 = self.calculate(self.list_9C8) + self.ord_list

        list_5D8 = self.calculate(list_0B0)
        list_378 = []
        list_740 = []
        for i in range(0x10):
            list_378.append(list_5D8[i])
        list_378Array = self.dump_list(list_378)
        for i in range(0x10, 0x20):
            list_740.append(list_5D8[i])

        list_8D8 = self.calculate(self.__content)
        list_AB0 = list_8D8 + self.__content
        list_AB0List = self.convertLongList(list_AB0)
        differ = 0x10 - len(list_AB0) % 0x10

        for i in range(differ):
            list_AB0List.append(differ)

        list_AB0 = list_AB0List

        list_55C = self.hex_CF8(list_378Array)
        final_list = self.hex_0A2(list_AB0, list_740, list_55C)
        final_list = (self.begining + self.list_9C8) + final_list
        final_list = self.changeLongArrayTobytes(final_list)

        return bytes(i % 256 for i in final_list).hex()

    def decrypt(self, data):
        data = bytearray.fromhex(data)
        data = list(data)
        self.setData(data)
        self.__content = self.__content_raw[38:]
        self.list_9C8 = self.__content_raw[6:38]
        self.__content = self.changeByteArrayToLong(self.__content)
        list_0B0 = self.calculate(self.list_9C8) + self.ord_list
        list_5D8 = self.calculate(list_0B0)

        list_378 = []
        list_740 = []
        for i in range(0x10):
            list_378.append(list_5D8[i])
        list_378Array = self.dump_list(list_378)
        for i in range(0x10, 0x20):
            list_740.append(list_5D8[i])

        key_longs = self.hex_list(list_378Array)
        decrypted = self.aes_decrypt(bytes(key_longs), bytes(self.__content))

        decryptedByteArray = ([0] * 16) + list(decrypted)
        toDecompress = decryptedByteArray[64:]
        result = gzip.decompress(bytes(toDecompress))
        return result.decode()

    def aes_decrypt(self, secretKey, encoded):
        initVector = encoded[0:16]
        data = encoded[16:]
        decryptor = AES.new(secretKey, AES.MODE_CBC, initVector)
        decoded = decryptor.decrypt(data)
        return decoded[: -decoded[-1]]

    def bytearray_decode(self, arrays):
        out = []
        for d in arrays:
            out.append(chr(d))
        return "".join(out)

    def changeLongArrayTobytes(self, array):
        result = []
        for i in range(len(array)):
            if array[i] > 127:
                result.append(array[i] - 256)
            else:
                result.append(array[i])
        return result

    def hex_0A2(self, content, list_740, list_55C):
        result = []
        l55cl = len(list_55C)
        lens = len(content)
        end = lens // 16
        for i in range(end):
            for j in range(16):
                list_740[j] = list_740[j] ^ content[16 * i + j]

            tmp_list = self.dump_list(list_740)
            R6 = tmp_list[3]
            LR = tmp_list[0]
            R8 = tmp_list[1]
            R12 = tmp_list[2]
            R5 = list_55C[0]
            R4 = list_55C[1]
            R1 = list_55C[2]
            R2 = list_55C[3]
            R11 = 0
            v_334 = 0
            R2 = R2 ^ R6
            v_33C = R2
            R1 = R1 ^ R12
            v_338 = R1
            R4 = R4 ^ R8
            R12 = R5 ^ LR
            for j in range(5):
                R3 = v_33C
                R9 = R4
                R0 = int(self.UBFX(R12, 0x10, 8))
                R1 = R3 >> 0x18
                R1 = self.dword_6[R1]
                R0 = self.dword_7[R0]
                R0 = R0 ^ R1
                R1 = int(self.UBFX(R4, 8, 8))
                R8 = v_338
                R1 = self.dword_8[R1]
                LR = list_55C[8 * j + 6]
                R0 = R0 ^ R1
                R1 = int(self.UTFX(R8))
                R1 = self.dword_9[R1]
                R0 = R0 ^ R1
                R1 = list_55C[8 * j + 4]
                v_334 = R1
                R1 = list_55C[8 * j + 5]
                v_330 = R1
                R1 = list_55C[8 * j + 7]
                R11 = R0 ^ R1
                R1 = int(self.UBFX(R3, 0x10, 8))
                R0 = R8 >> 24
                R0 = self.dword_6[R0]
                R1 = self.dword_7[R1]
                R0 = R0 ^ R1
                R1 = int(self.UBFX(R12, 8, 8))
                R1 = self.dword_8[R1]
                R0 = R0 ^ R1
                R1 = int(self.UTFX(R9))
                R1 = self.dword_9[R1]
                R0 = R0 ^ R1
                R1 = int(self.UBFX(R8, 0x10, 8))
                R6 = R0 ^ LR
                R0 = R9 >> 24
                R0 = self.dword_6[R0]
                R1 = self.dword_7[R1]
                R0 = R0 ^ R1
                R1 = int(self.UBFX(R3, 8, 8))
                R1 = self.dword_8[R1]
                R0 = R0 ^ R1
                R1 = int(self.UTFX(R12))
                R1 = self.dword_9[R1]
                R0 = R0 ^ R1
                R1 = v_330
                LR = R0 ^ R1
                R0 = int(self.UTFX(R3))
                R0 = self.dword_9[R0]
                R4 = R12 >> 24

                R1 = int(self.UBFX(R8, 8, 8))
                R4 = self.dword_6[R4]
                R5 = int(self.UBFX(R9, 16, 8))
                R1 = self.dword_8[R1]
                R5 = self.dword_7[R5]
                R5 = R5 ^ R4
                R1 = R1 ^ R5
                R0 = R0 ^ R1
                R1 = v_334
                R1 = R1 ^ R0
                R0 = R1 >> 0x18
                v_334 = R0
                if j == 4:
                    break
                else:
                    R4 = int(self.UBFX(R1, 16, 8))
                    R5 = R11 >> 24
                    R10 = R6
                    R5 = self.dword_6[R5]
                    R4 = self.dword_7[R4]
                    R5 = R5 ^ R4
                    R4 = int(self.UBFX(LR, 8, 8))
                    R4 = self.dword_8[R4]
                    R5 = R5 ^ R4
                    R4 = int(self.UTFX(R10))
                    R4 = self.dword_9[R4]
                    R5 = R5 ^ R4
                    R4 = list_55C[8 * j + 11]
                    R0 = R5 ^ R4
                    v_33C = R0
                    R4 = int(self.UBFX(R11, 16, 8))
                    R5 = R10 >> 24
                    R5 = self.dword_6[R5]
                    R4 = self.dword_7[R4]
                    R5 = R5 ^ R4
                    R4 = int(self.UBFX(R1, 8, 8))
                    R0 = list_55C[8 * j + 9]
                    R9 = list_55C[8 * j + 8]
                    R1 = int(self.UTFX(R1))
                    R4 = self.dword_8[R4]
                    R1 = self.dword_9[R1]
                    R5 = R5 ^ R4
                    R4 = int(self.UTFX(LR))
                    R4 = self.dword_9[R4]
                    R5 = R5 ^ R4
                    R4 = list_55C[8 * j + 10]
                    R4 = R4 ^ R5
                    v_338 = R4
                    R5 = int(self.UBFX(R10, 16, 8))
                    R4 = LR >> 24
                    R4 = self.dword_6[R4]
                    R5 = self.dword_7[R5]
                    R4 = R4 ^ R5
                    R5 = int(self.UBFX(R11, 8, 8))
                    R5 = self.dword_8[R5]
                    R4 = R4 ^ R5
                    R1 = R1 ^ R4
                    R4 = R1 ^ R0
                    R0 = v_334
                    R1 = int(self.UBFX(LR, 16, 8))
                    R5 = int(self.UBFX(R10, 8, 8))
                    R0 = self.dword_6[R0]
                    R1 = self.dword_7[R1]
                    R5 = self.dword_8[R5]
                    R0 = R0 ^ R1
                    R1 = int(self.UTFX(R11))
                    R1 = self.dword_9[R1]
                    R0 = R0 ^ R5
                    R0 = R0 ^ R1
                    R12 = R0 ^ R9
            R2 = R11 >> 24
            R3 = int(self.UBFX(R1, 16, 8))
            R10 = R6
            R0 = R10 >> 24
            R2 = self.dword_0[R2]

            R2 = int(self.parseLong(self.toHex(R2) + "000000", 10, 16))
            R9 = R10
            R3 = self.dword_0[R3]
            R3 = int(self.parseLong(self.toHex(R3) + "0000", 10, 16))
            R0 = self.dword_0[R0]
            R0 = int(self.parseLong(self.toHex(R0) + "000000", 10, 16))
            R2 = R2 ^ R3
            v_350 = R2
            R2 = int(self.UBFX(R11, 0x10, 8))
            R2 = self.dword_0[R2]
            R2 = int(self.parseLong(self.toHex(R2) + "0000", 10, 16))
            R0 = R0 ^ R2
            R2 = int(self.UBFX(R1, 8, 8))
            R1 = int(self.UTFX(R1))
            R2 = self.dword_0[R2]
            R2 = int(self.parseLong(self.toHex(R2) + "00", 10, 16))
            R1 = self.dword_0[R1]
            R0 = R0 ^ R2
            R2 = int(self.UTFX(LR))
            R2 = self.dword_0[R2]
            R12 = R0 ^ R2
            R0 = list_55C[l55cl - 2]
            R10 = list_55C[l55cl - 3]
            R12 = R12 ^ R0
            R2 = list_55C[l55cl - 1]
            R0 = LR >> 24
            v_34C = R2
            R2 = int(self.UBFX(R9, 0x10, 8))
            R0 = self.dword_0[R0]
            R0 = int(self.parseLong(self.toHex(R0) + "000000", 10, 16))
            R2 = self.dword_0[R2]
            R2 = int(self.parseLong(self.toHex(R2) + "0000", 10, 16))
            R0 = R0 ^ R2
            R2 = int(self.UBFX(R11, 8, 8))
            R2 = self.dword_0[R2]
            R2 = int(self.parseLong(self.toHex(R2) + "00", 10, 16))
            R0 = R0 ^ R2
            R0 = R0 ^ R1
            R1 = R0 ^ R10
            R0 = v_334
            R2 = int(self.UBFX(LR, 0x10, 8))
            R0 = self.dword_0[R0]
            R0 = int(self.parseLong(self.toHex(R0) + "000000", 10, 16))
            R2 = self.dword_0[R2]
            R2 = int(self.parseLong(self.toHex(R2) + "0000", 10, 16))
            R0 = R0 ^ R2
            R2 = int(self.UBFX(R9, 8, 8))
            R2 = self.dword_0[R2]
            R2 = int(self.parseLong(self.toHex(R2) + "00", 10, 16))
            R0 = R0 ^ R2
            R2 = int(self.UTFX(R11))
            R2 = self.dword_0[R2]
            R0 = R0 ^ R2
            R2 = int(self.UTFX(R9))
            R2 = self.dword_0[R2]
            R3 = int(self.UBFX(LR, 8, 8))
            R3 = self.dword_0[R3]
            R3 = int(self.parseLong(self.toHex(R3) + "00", 10, 16))
            R5 = v_350
            R6 = list_55C[l55cl - 4]
            R3 = R3 ^ R5
            R2 = R2 ^ R3
            R3 = v_34C
            R0 = R0 ^ R6
            R2 = R2 ^ R3
            list_740 = self.hex_list([R0, R1, R12, R2])
            result = result + list_740

        return result  # WORKED

    def calculate(self, content):
        hex_6A8 = 0
        tmp_list = []
        length = len(content)
        list_6B0 = self.LIST_6B0.copy()

        for item in content:
            tmp_list.append(item)

        divisible = length % 0x80
        tmp = 0x80 - divisible

        if tmp > 0x11:
            tmp_list.append(0x80)
            for i in range(tmp - 0x11):
                tmp_list.append(0)

            for j in range(16):
                tmp_list.append(0)
        else:
            tmp_list.append(128)

            for i in range(128 - 16 + tmp + 1):
                tmp_list.append(0)

            for j in range(16):
                tmp_list.append(0)

        tmp_list_size = len(tmp_list)
        d = tmp_list_size // 0x80
        for i in range(tmp_list_size // 0x80):
            if (tmp_list_size // 128 - 1) == i:
                ending = self.handle_ending(hex_6A8, divisible)
                for j in range(8):
                    index = tmp_list_size - j - 1
                    tmp_list[index] = ending[7 - j]

            param_list = []
            for j in range(32):
                tmpss = ""
                for k in range(4):
                    tmp_string = self.toHex(tmp_list[0x80 * i + 4 * j + k])
                    if len(tmp_string) < 2:
                        tmp_string = "0" + tmp_string

                    tmpss = tmpss + tmp_string

                param_list.append(int(self.parseLong(tmpss, 10, 16)))

            list_3B8 = self.hex_27E(param_list)

            list_6B0 = self.hex_30A(list_6B0, list_3B8)

            hex_6A8 += 0x400

        list_8D8 = self.hex_C52(list_6B0)
        return list_8D8

    def convertLongList(self, content):
        if len(content) == 0:
            return []
        result = []
        for i in content:
            result.append(i)
        return result

    def dump_list(self, content):
        size = len(content)
        ssize = size // 4
        result = []
        for index in range(ssize):
            tmp_string = ""
            for j in range(4):
                tmp = self.toHex(content[4 * index + j])
                if len(tmp) < 2:
                    tmp = "0" + tmp

                tmp_string = tmp_string + tmp
            i = int(self.parseLong(tmp_string, 10, 16))
            result.append(int(i))
        return result

    def hex_CF8(self, param_list):
        list_388 = []
        list_378 = param_list
        for i in range(0xA):
            R3 = list_378[0]
            R8 = list_378[1]
            R9 = list_378[2]
            R5 = list_378[3]
            R6 = int(self.UBFX(R5, 8, 8))
            R6 = self.dword_0[R6]
            R6 = int(self.parseLong(self.toHex(R6) + "0000", 10, 16))
            R4 = int(self.UBFX(R5, 0x10, 8))
            R11 = self.dword_1[i]
            R4 = self.dword_0[R4]
            R4 = int(self.parseLong(self.toHex(R4) + "000000", 10, 16))
            R3 = R3 ^ R4
            R4 = int(self.UTFX(R5))
            R3 = R3 ^ R6
            R4 = self.dword_0[R4]
            R4 = int(self.parseLong(self.toHex(R4) + "00", 10, 16))
            R3 = R3 ^ R4
            R4 = R5 >> 24
            R4 = self.dword_0[R4]
            R3 = R3 ^ R4
            R3 = R3 ^ R11
            R2 = R8 ^ R3
            R4 = R9 ^ R2
            R5 = R5 ^ R4
            list_378 = [R3, R2, R4, R5]
            list_388 = list_388 + list_378
        l388l = len(list_388)
        list_478 = []
        for i in range(0x9):
            R5 = list_388[l388l - 8 - 4 * i]
            R4 = int(self.UBFX(R5, 0x10, 8))
            R6 = R5 >> 0x18
            R6 = self.dword_2[R6]
            R4 = self.dword_3[R4]
            R6 = R6 ^ R4
            R4 = int(self.UBFX(R5, 8, 8))
            R5 = int(self.UTFX(R5))
            R4 = self.dword_4[R4]
            R5 = self.dword_5[R5]
            R6 = R6 ^ R4
            R6 = R6 ^ R5
            list_478.append(R6)
            R6 = list_388[l388l - 7 - 4 * i]
            R1 = int(self.UBFX(R6, 0x10, 8))
            R4 = R6 >> 0x18
            R4 = self.dword_2[R4]
            R1 = self.dword_3[R1]
            R1 = R1 ^ R4
            R4 = int(self.UBFX(R6, 8, 8))
            R4 = self.dword_4[R4]
            R1 = R1 ^ R4
            R4 = int(self.UTFX(R6))
            R4 = self.dword_5[R4]
            R1 = R1 ^ R4
            list_478.append(R1)
            R1 = list_388[l388l - 6 - 4 * i]
            R6 = int(self.UBFX(R1, 0x10, 8))
            R4 = R1 >> 0x18
            R4 = self.dword_2[R4]
            R6 = self.dword_3[R6]
            R4 = R4 ^ R6
            R6 = int(self.UBFX(R1, 8, 8))
            R1 = int(self.UTFX(R1))
            R6 = self.dword_4[R6]
            R1 = self.dword_5[R1]
            R4 = R4 ^ R6
            R1 = R1 ^ R4
            list_478.append(R1)
            R0 = list_388[l388l - 5 - 4 * i]
            R1 = int(self.UTFX(R0))
            R4 = int(self.UBFX(R0, 8, 8))
            R6 = R0 >> 0x18
            R0 = int(self.UBFX(R0, 0x10, 8))
            R6 = self.dword_2[R6]
            R0 = self.dword_3[R0]
            R4 = self.dword_4[R4]
            R1 = self.dword_5[R1]
            R0 = R0 ^ R6
            R0 = R0 ^ R4
            R0 = R0 ^ R1
            list_478.append(R0)
        list_468 = param_list + list_388
        return list_468

    def handle_ending(self, num, r0):
        s = self.toHex(num)
        r1 = None
        r2 = None
        if len(s) <= 8:
            r1 = num
            r2 = 0
        else:
            num_str = self.toHex(num)
            length = len(num)
            r1 = self.parseLong(num_str[: length - 8], 10, 16)
            r2 = self.parseLong(num_str[2 : length - 8], 10, 16)

        r1 = self.ADDS(r1, r0 << 3)
        r2 = self.ADC(r2, r0 >> 29)
        a = self.hex_list([r2, r1])
        return self.hex_list([r2, r1])

    def UTFX(self, num):
        tmp_string = self.toBinaryString(num)
        start = len(tmp_string) - 8
        return self.parseLong(tmp_string[start:], 10, 2)

    def hex_27E(self, param_list):
        r6 = param_list[0]
        r8 = param_list[1]
        for i in range(0x40):
            r0 = param_list[2 * i + 0x1C]
            r5 = param_list[2 * i + 0x1D]
            r4 = self.LSRS(r0, 0x13)
            r3 = self.LSRS(r0, 0x1D)
            lr = r4 | self.check(r5) << 13
            r4 = self.LSLS(r0, 3)
            r4 = r4 | self.check(r5) >> 29
            r3 = r3 | self.check(r5) << 3
            r4 = r4 ^ self.check(r0) >> 6
            lr = lr ^ r4
            r4 = self.LSRS(r5, 6)
            r4 = r4 | self.check(r0) << 26
            r9 = r3 ^ r4
            r4 = self.LSRS(r5, 0x13)
            r0 = r4 | self.check(r0) << 13
            r10 = param_list[2 * i + 0x12]
            r3 = param_list[2 * i + 0x13]
            r5 = param_list[2 * i + 0x2]
            r4 = param_list[2 * i + 0x3]
            r0 = r0 ^ r9
            r3 = self.ADDS(r3, r8)
            r6 = self.ADC(r6, r10)
            r8 = self.ADDS(r3, r0)
            lr = self.ADC(lr, r6)
            r6 = self.LSRS(r4, 7)
            r3 = self.LSRS(r4, 8)
            r6 = r6 | self.check(r5) << 25
            r3 = r3 | self.check(r5) << 24
            r3 = int(self.EORS(r3, r6))
            r6 = self.LSRS(r5, 1)
            r0 = int(self.RRX(r4))
            r0 = int(self.EORS(r0, r3))
            r3 = r6 | self.check(r4) << 31
            r6 = self.LSRS(r5, 8)
            r0 = int(self.ADDS(r0, r8))
            r6 = r6 | self.check(r4) << 24
            r8 = r4
            r6 = r6 ^ self.check(r5) >> 7
            r3 = r3 ^ r6
            r6 = r5

            r3 = self.ADC(r3, lr)
            param_list = param_list + [r3, r0]

        return param_list  # WORKED

    def hex_30A(self, param_list, list_3B8):
        v_3A0 = param_list[7]
        v_3A4 = param_list[6]
        v_374 = param_list[5]
        v_378 = param_list[4]
        LR = param_list[0]
        R12 = param_list[1]
        v_39C = param_list[2]
        v_398 = param_list[3]
        v_3AC = param_list[11]
        v_3A8 = param_list[10]
        R9 = param_list[12]
        R10 = param_list[13]
        R5 = param_list[9]
        R8 = param_list[8]
        R4 = param_list[15]
        R6 = param_list[14]
        for index in range(10):
            v_384 = R5
            R3 = self.rodata[0x10 * index]
            R1 = self.rodata[0x10 * index + 2]
            R2 = self.rodata[0x10 * index + 1]
            R3 = self.ADDS(R3, R6)

            R6 = self.check(R8) >> 14

            v_390 = R1
            R6 = R6 | self.check(R5) << 18

            R1 = self.rodata[0x10 * index + 3]
            R0 = self.rodata[0x10 * index + 4]
            v_36C = R0
            R0 = self.ADC(R2, R4)
            R2 = self.LSRS(R5, 0x12)
            R4 = self.LSRS(R5, 0xE)
            R2 = R2 | self.check(R8) << 14
            R4 = R4 | self.check(R8) << 18
            R2 = self.EORS(R2, R4)
            R4 = self.LSLS(R5, 0x17)
            R4 = R4 | self.check(R8) >> 9
            v_38C = R1
            R2 = self.EORS(R2, R4)
            R4 = self.check(R8) >> 18
            R4 = R4 | self.check(R5) << 14
            R6 = self.EORS(R6, R4)
            R4 = self.LSRS(R5, 9)
            R4 = R4 | self.check(R8) << 23
            v_354 = R8
            R6 = self.EORS(R6, R4)
            R3 = self.ADDS(R3, R6)
            R0 = self.ADCS(R0, R2)
            R2 = list_3B8[0x10 * index + 1]
            R2 = self.ADDS(R2, R3)
            R3 = list_3B8[0x10 * index + 3]
            R6 = list_3B8[0x10 * index]
            v_358 = R10
            R6 = self.ADCS(R6, R0)
            R0 = v_3AC
            v_360 = R3
            R0 = R0 ^ R10
            R3 = list_3B8[0x10 * index + 2]
            R0 = self.ANDS(R0, R5)
            R1 = list_3B8[0x10 * index + 5]
            R4 = R0 ^ R10
            R0 = v_3A8
            v_364 = R1
            R0 = R0 ^ R9
            R1 = v_374
            R0 = R0 & R8
            R8 = v_39C
            R0 = R0 ^ R9
            v_35C = R3
            R10 = self.ADDS(R2, R0)
            R0 = v_398
            R11 = self.ADC(R6, R4)
            R3 = v_378
            R2 = R0 | R12
            R6 = R0 & R12
            R2 = self.ANDS(R2, R1)
            R1 = R0
            R2 = self.ORRS(R2, R6)
            R6 = R8 | LR
            R6 = self.ANDS(R6, R3)
            R3 = R8 & LR
            R3 = self.ORRS(R3, R6)
            R6 = self.check(R12) << 30
            R0 = self.check(R12) >> 28
            R6 = R6 | self.check(LR) >> 2
            R0 = R0 | self.check(LR) << 4
            R4 = self.check(LR) >> 28
            R0 = self.EORS(R0, R6)
            R6 = self.check(R12) << 25
            R6 = R6 | self.check(LR) >> 7
            R4 = R4 | self.check(R12) << 4
            R0 = self.EORS(R0, R6)
            R6 = self.check(R12) >> 2
            R6 = R6 | self.check(LR) << 30
            R3 = self.ADDS(R3, R10)
            R6 = R6 ^ R4
            R4 = self.check(R12) >> 7
            R4 = R4 | self.check(LR) << 25
            R2 = self.ADC(R2, R11)
            R6 = self.EORS(R6, R4)
            v_37C = R12
            R5 = self.ADDS(R3, R6)
            R6 = self.ADC(R2, R0)
            R0 = R6 | R12
            R2 = R6 & R12
            R0 = self.ANDS(R0, R1)
            R3 = self.LSRS(R6, 0x1C)
            R0 = self.ORRS(R0, R2)
            R2 = self.LSLS(R6, 0x1E)
            R2 = R2 | self.check(R5) >> 2
            R3 = R3 | self.check(R5) << 4
            R2 = self.EORS(R2, R3)
            R3 = self.LSLS(R6, 0x19)
            R3 = R3 | self.check(R5) >> 7
            R4 = self.LSRS(R5, 0x1C)
            R3 = self.EORS(R3, R2)
            R2 = self.LSRS(R6, 2)
            R2 = R2 | self.check(R5) << 30
            R4 = R4 | self.check(R6) << 4
            R2 = self.EORS(R2, R4)
            R4 = self.LSRS(R6, 7)
            R4 = R4 | self.check(R5) << 25
            R12 = R6
            R2 = self.EORS(R2, R4)
            R4 = R5 | LR
            R4 = R4 & R8
            R6 = R5 & LR
            R4 = self.ORRS(R4, R6)
            v_388 = R5
            R5 = self.ADDS(R2, R4)
            R0 = self.ADCS(R0, R3)
            v_398 = R1
            R4 = R9
            v_350 = R0
            R0 = v_3A4
            R1 = v_3A0
            v_380 = LR
            LR = self.ADDS(R0, R10)
            R9 = self.ADC(R1, R11)
            R0 = v_3AC
            R6 = self.check(LR) >> 14
            R1 = v_384
            R3 = self.check(R9) >> 18
            R2 = self.check(R9) >> 14
            R3 = R3 | self.check(LR) << 14
            R2 = R2 | self.check(LR) << 18
            R2 = self.EORS(R2, R3)
            R3 = self.check(R9) << 23
            R3 = R3 | self.check(LR) >> 9
            R6 = R6 | self.check(R9) << 18
            R2 = self.EORS(R2, R3)
            R3 = self.check(LR) >> 18
            R3 = R3 | self.check(R9) << 14
            v_39C = R8
            R3 = self.EORS(R3, R6)
            R6 = self.check(R9) >> 9
            R6 = R6 | self.check(LR) << 23
            R8 = v_354
            R3 = self.EORS(R3, R6)
            R6 = R0 ^ R1
            R6 = R6 & R9
            v_370 = R12
            R6 = self.EORS(R6, R0)
            R0 = v_3A8
            R1 = R0 ^ R8
            R1 = R1 & LR
            R1 = self.EORS(R1, R0)
            R0 = v_358
            R1 = self.ADDS(R1, R4)
            R6 = self.ADCS(R6, R0)
            R0 = v_390
            R1 = self.ADDS(R1, R0)
            R0 = v_38C
            R6 = self.ADCS(R6, R0)
            R0 = v_360
            R1 = self.ADDS(R1, R0)
            R0 = v_35C
            R6 = self.ADCS(R6, R0)
            R1 = self.ADDS(R1, R3)
            R3 = self.ADC(R6, R2)
            R2 = v_350
            R0 = self.ADDS(R5, R1)
            R5 = v_37C
            R4 = self.ADC(R2, R3)
            v_390 = R4
            R2 = R4 | R12
            R6 = R4 & R12
            R2 = self.ANDS(R2, R5)
            R5 = self.LSRS(R4, 0x1C)
            R10 = R2 | R6
            R2 = self.LSLS(R4, 0x1E)
            R2 = R2 | self.check(R0) >> 2
            R5 = R5 | self.check(R0) << 4
            R2 = self.EORS(R2, R5)
            R5 = self.LSLS(R4, 0x19)
            R5 = R5 | self.check(R0) >> 7
            R6 = self.LSRS(R0, 0x1C)
            R12 = R2 ^ R5
            R2 = self.LSRS(R4, 2)
            R2 = R2 | self.check(R0) << 30
            R6 = R6 | self.check(R4) << 4
            R2 = self.EORS(R2, R6)
            R6 = self.LSRS(R4, 7)
            R4 = v_388
            R6 = R6 | self.check(R0) << 25
            R5 = v_380
            R2 = self.EORS(R2, R6)
            R6 = R0 | R4
            R4 = self.ANDS(R4, R0)
            R6 = self.ANDS(R6, R5)
            v_38C = R0
            R4 = self.ORRS(R4, R6)
            R6 = LR ^ R8
            R0 = self.ADDS(R2, R4)
            v_3A4 = R0
            R0 = self.ADC(R12, R10)
            v_3A0 = R0
            R0 = v_378
            R10 = self.ADDS(R1, R0)
            R0 = v_374
            R6 = R6 & R10
            R1 = self.ADC(R3, R0)
            R5 = self.check(R10) >> 14
            R0 = v_384
            R6 = R6 ^ R8
            R3 = self.LSRS(R1, 0x12)
            R4 = self.LSRS(R1, 0xE)
            R3 = R3 | self.check(R10) << 14
            R4 = R4 | self.check(R10) << 18
            R3 = self.EORS(R3, R4)
            R4 = self.LSLS(R1, 0x17)
            R4 = R4 | self.check(R10) >> 9
            R5 = R5 | self.check(R1) << 18
            R11 = R3 ^ R4
            R3 = self.check(R10) >> 18
            R3 = R3 | self.check(R1) << 14
            v_378 = R1
            R3 = self.EORS(R3, R5)
            R5 = self.LSRS(R1, 9)
            R5 = R5 | self.check(R10) << 23
            R3 = self.EORS(R3, R5)
            R5 = R9 ^ R0
            R5 = self.ANDS(R5, R1)
            R1 = v_3A8
            R5 = self.EORS(R5, R0)
            R0 = v_36C
            R4 = self.ADDS(R0, R1)
            R2 = self.rodata[0x10 * index + 5]
            R0 = v_3AC
            R2 = self.ADCS(R2, R0)
            R0 = v_364
            R4 = self.ADDS(R4, R0)
            R12 = list_3B8[0x10 * index + 4]
            R0 = v_3A4
            R2 = self.ADC(R2, R12)
            R6 = self.ADDS(R6, R4)
            R2 = self.ADCS(R2, R5)
            R3 = self.ADDS(R3, R6)
            R11 = self.ADC(R11, R2)
            R1 = self.ADDS(R0, R3)
            R0 = v_3A0
            R6 = v_390
            R4 = self.check(R1) >> 28
            R0 = self.ADC(R0, R11)
            R5 = v_370
            R2 = R0 | R6
            R6 = self.ANDS(R6, R0)
            R2 = self.ANDS(R2, R5)
            R5 = self.LSRS(R0, 0x1C)
            R12 = R2 | R6
            R6 = self.LSLS(R0, 0x1E)
            R6 = R6 | self.check(R1) >> 2
            R5 = R5 | self.check(R1) << 4
            R6 = self.EORS(R6, R5)
            R5 = self.LSLS(R0, 0x19)
            R5 = R5 | self.check(R1) >> 7
            R4 = R4 | self.check(R0) << 4
            R6 = self.EORS(R6, R5)
            R5 = self.LSRS(R0, 2)
            R5 = R5 | self.check(R1) << 30
            v_3AC = R0
            R5 = self.EORS(R5, R4)
            R4 = self.LSRS(R0, 7)
            R0 = v_38C
            R4 = R4 | self.check(R1) << 25
            R2 = v_388
            R5 = self.EORS(R5, R4)
            R4 = R1 | R0
            v_3A8 = R1
            R4 = self.ANDS(R4, R2)
            R2 = R1 & R0
            R2 = self.ORRS(R2, R4)
            R0 = self.ADDS(R5, R2)
            v_3A4 = R0
            R0 = self.ADC(R6, R12)
            v_3A0 = R0
            R0 = v_39C
            R2 = v_398
            R0 = self.ADDS(R0, R3)
            v_39C = R0
            R11 = self.ADC(R11, R2)
            R4 = self.LSRS(R0, 0xE)
            R3 = self.check(R11) >> 18
            R6 = self.check(R11) >> 14
            R3 = R3 | self.check(R0) << 14
            R6 = R6 | self.check(R0) << 18
            R3 = self.EORS(R3, R6)
            R6 = self.check(R11) << 23
            R6 = R6 | self.check(R0) >> 9
            R4 = R4 | self.check(R11) << 18
            R1 = self.EORS(R3, R6)
            R6 = self.LSRS(R0, 0x12)
            R6 = R6 | self.check(R11) << 14
            R3 = R10 ^ LR
            R6 = self.EORS(R6, R4)
            R4 = self.check(R11) >> 9
            R3 = self.ANDS(R3, R0)
            R4 = R4 | self.check(R0) << 23
            R5 = R6 ^ R4
            v_398 = R1
            R3 = R3 ^ LR
            R1 = v_378
            R6 = self.rodata[0x10 * index + 6]
            R12 = self.rodata[0x10 * index + 7]
            R4 = R1 ^ R9
            R0 = v_384
            R6 = self.ADDS(R6, R8)
            R4 = R4 & R11
            R12 = self.ADC(R12, R0)
            R4 = R4 ^ R9
            R8 = list_3B8[0x10 * index + 7]
            R2 = list_3B8[0x10 * index + 6]
            R6 = self.ADDS(R6, R8)
            R0 = v_398
            R2 = self.ADC(R2, R12)
            R3 = self.ADDS(R3, R6)
            R2 = self.ADCS(R2, R4)
            R6 = self.ADDS(R3, R5)
            R12 = self.ADC(R2, R0)
            R0 = v_3A4
            R4 = v_390
            R1 = self.ADDS(R0, R6)
            R0 = v_3A0
            v_384 = R1
            R5 = self.ADC(R0, R12)
            R0 = v_3AC
            R8 = self.check(R1) >> 28
            R2 = R5 | R0
            R3 = R8 | self.check(R5) << 4
            R2 = self.ANDS(R2, R4)
            R4 = R5 & R0
            R0 = R2 | R4
            R4 = self.LSLS(R5, 0x1E)
            R2 = self.LSRS(R5, 0x1C)
            R4 = R4 | self.check(R1) >> 2
            R2 = R2 | self.check(R1) << 4
            v_3A0 = R0
            R2 = self.EORS(R2, R4)
            R4 = self.LSLS(R5, 0x19)
            R4 = R4 | self.check(R1) >> 7
            R0 = v_3A8
            R2 = self.EORS(R2, R4)
            R4 = self.LSRS(R5, 2)
            R4 = R4 | self.check(R1) << 30
            R8 = R5
            R3 = self.EORS(R3, R4)
            R4 = self.LSRS(R5, 7)
            R4 = R4 | self.check(R1) << 25
            R5 = v_38C
            R3 = self.EORS(R3, R4)
            R4 = R1 | R0
            R4 = self.ANDS(R4, R5)
            R5 = R1 & R0
            R4 = self.ORRS(R4, R5)
            v_36C = R8
            R0 = self.ADDS(R3, R4)
            v_3A4 = R0
            R0 = v_3A0
            R0 = self.ADCS(R0, R2)
            v_3A0 = R0
            R0 = v_380
            R2 = v_37C
            R0 = self.ADDS(R0, R6)
            R5 = self.ADC(R12, R2)
            v_37C = R5
            R4 = self.LSRS(R0, 0xE)
            v_380 = R0
            R2 = self.LSRS(R5, 0x12)
            R3 = self.LSRS(R5, 0xE)
            R2 = R2 | self.check(R0) << 14
            R3 = R3 | self.check(R0) << 18
            R2 = self.EORS(R2, R3)
            R3 = self.LSLS(R5, 0x17)
            R3 = R3 | self.check(R0) >> 9
            R4 = R4 | self.check(R5) << 18
            R1 = R2 ^ R3
            R3 = self.LSRS(R0, 0x12)
            R3 = R3 | self.check(R5) << 14
            v_398 = R1
            R3 = self.EORS(R3, R4)
            R4 = self.LSRS(R5, 9)
            R1 = v_378
            R4 = R4 | self.check(R0) << 23
            R12 = R3 ^ R4
            R3 = list_3B8[0x10 * index + 9]
            R4 = R11 ^ R1
            R4 = self.ANDS(R4, R5)
            R4 = self.EORS(R4, R1)
            R1 = v_39C
            R5 = R1 ^ R10
            R5 = self.ANDS(R5, R0)
            R5 = R5 ^ R10
            R2 = self.rodata[0x10 * index + 8]
            R0 = self.ADDS(R2, LR)
            R2 = self.rodata[0x10 * index + 9]
            R2 = self.ADC(R2, R9)
            R0 = self.ADDS(R0, R3)
            R3 = list_3B8[0x10 * index + 8]
            R2 = self.ADCS(R2, R3)
            R0 = self.ADDS(R0, R5)
            R2 = self.ADCS(R2, R4)
            R1 = self.ADDS(R0, R12)
            R0 = v_398
            R3 = v_3AC
            R4 = self.ADC(R2, R0)
            R0 = v_3A4
            R6 = self.ADDS(R0, R1)
            R0 = v_3A0
            v_3A4 = R6
            R0 = self.ADCS(R0, R4)
            v_3A0 = R0
            R2 = R0 | R8
            R2 = self.ANDS(R2, R3)
            R3 = R0 & R8
            LR = R2 | R3
            R8 = R6
            R3 = self.LSLS(R0, 0x1E)
            R5 = self.LSRS(R0, 0x1C)
            R3 = R3 | self.check(R8) >> 2
            R5 = R5 | self.check(R8) << 4
            R3 = self.EORS(R3, R5)
            R5 = self.LSLS(R0, 0x19)
            R5 = R5 | self.check(R8) >> 7
            R2 = self.check(R8) >> 28
            R12 = R3 ^ R5
            R5 = self.LSRS(R0, 2)
            R5 = R5 | self.check(R8) << 30
            R2 = R2 | self.check(R0) << 4
            R2 = self.EORS(R2, R5)
            R5 = self.LSRS(R0, 7)
            R3 = v_384
            R5 = R5 | self.check(R8) << 25
            R6 = v_3A8
            R2 = self.EORS(R2, R5)
            R5 = R8 | R3
            R5 = self.ANDS(R5, R6)
            R6 = R8 & R3
            R5 = self.ORRS(R5, R6)
            R0 = self.ADDS(R2, R5)
            v_398 = R0
            R2 = v_388
            R12 = self.ADC(R12, LR)
            R0 = v_370
            R3 = self.ADDS(R1, R2)
            R1 = v_380
            R8 = self.ADC(R4, R0)
            R0 = R3
            R2 = self.check(R8) >> 18
            R3 = self.check(R8) >> 14
            R2 = R2 | self.check(R0) << 14
            R3 = R3 | self.check(R0) << 18
            R2 = self.EORS(R2, R3)
            R3 = self.check(R8) << 23
            R3 = R3 | self.check(R0) >> 9
            R4 = self.LSRS(R0, 0xE)
            LR = R2 ^ R3
            R3 = self.LSRS(R0, 0x12)
            R3 = R3 | self.check(R8) << 14
            R4 = R4 | self.check(R8) << 18
            R3 = self.EORS(R3, R4)
            R4 = self.check(R8) >> 9
            R4 = R4 | self.check(R0) << 23
            R2 = R0
            R0 = v_37C
            R3 = self.EORS(R3, R4)
            v_388 = R2
            R4 = R0 ^ R11
            R0 = v_39C
            R4 = R4 & R8
            R5 = R1 ^ R0
            R4 = R4 ^ R11
            R5 = self.ANDS(R5, R2)
            R5 = self.EORS(R5, R0)
            R6 = self.rodata[0x10 * index + 10]
            R1 = self.ADDS(R6, R10)
            R6 = self.rodata[0x10 * index + 11]
            R0 = v_378
            R6 = self.ADCS(R6, R0)
            R2 = list_3B8[0x10 * index + 11]
            R1 = self.ADDS(R1, R2)
            R2 = list_3B8[0x10 * index + 10]
            R0 = v_398
            R2 = self.ADCS(R2, R6)
            R1 = self.ADDS(R1, R5)
            R2 = self.ADCS(R2, R4)
            R1 = self.ADDS(R1, R3)
            R4 = self.ADC(R2, LR)
            R6 = v_3A0
            R0 = self.ADDS(R0, R1)
            R9 = self.ADC(R12, R4)
            R3 = v_36C
            R2 = R9 | R6
            R5 = self.check(R9) >> 28
            v_374 = R9
            R2 = self.ANDS(R2, R3)
            R3 = R9 & R6
            R10 = R2 | R3
            R3 = self.check(R9) << 30
            R3 = R3 | self.check(R0) >> 2
            R5 = R5 | self.check(R0) << 4
            R3 = self.EORS(R3, R5)
            R5 = self.check(R9) << 25
            R5 = R5 | self.check(R0) >> 7
            R6 = self.LSRS(R0, 0x1C)
            R12 = R3 ^ R5
            R5 = self.check(R9) >> 2
            R5 = R5 | self.check(R0) << 30
            R6 = R6 | self.check(R9) << 4
            R5 = self.EORS(R5, R6)
            R6 = self.check(R9) >> 7
            R3 = v_3A4
            R6 = R6 | self.check(R0) << 25
            R2 = v_384
            R5 = self.EORS(R5, R6)
            R6 = R0 | R3
            R6 = self.ANDS(R6, R2)
            R2 = R0 & R3
            R2 = R2 | R6
            R2 = self.ADDS(R2, R5)
            v_398 = R2
            R2 = self.ADC(R12, R10)
            v_378 = R2
            R2 = v_38C
            R12 = self.ADDS(R1, R2)
            R1 = v_390
            LR = self.ADC(R4, R1)
            R4 = self.check(R12) >> 14
            R1 = self.check(LR) >> 18
            R2 = self.check(LR) >> 14
            R1 = R1 | self.check(R12) << 14
            R2 = R2 | self.check(R12) << 18
            R1 = self.EORS(R1, R2)
            R2 = self.check(LR) << 23
            R2 = R2 | self.check(R12) >> 9
            R4 = R4 | self.check(LR) << 18
            R1 = self.EORS(R1, R2)
            R2 = self.check(R12) >> 18
            R2 = R2 | self.check(LR) << 14
            v_390 = R1
            R2 = self.EORS(R2, R4)
            R4 = self.check(LR) >> 9
            R1 = v_37C
            R4 = R4 | self.check(R12) << 23
            R10 = R2 ^ R4
            R2 = v_388
            R4 = R8 ^ R1
            R4 = R4 & LR
            R4 = self.EORS(R4, R1)
            R1 = v_380
            R5 = R2 ^ R1
            R2 = v_39C
            R5 = R5 & R12
            R5 = self.EORS(R5, R1)
            R6 = self.rodata[0x10 * index + 12]
            R3 = self.rodata[0x10 * index + 13]
            R6 = self.ADDS(R6, R2)
            R3 = self.ADC(R3, R11)
            R1 = list_3B8[0x10 * index + 13]
            R1 = self.ADDS(R1, R6)
            R6 = list_3B8[0x10 * index + 12]
            R3 = self.ADCS(R3, R6)
            R1 = self.ADDS(R1, R5)
            R3 = self.ADCS(R3, R4)
            R5 = self.ADDS(R1, R10)
            R1 = v_390
            R2 = self.ADC(R3, R1)
            R1 = v_398
            R3 = v_3A0
            R10 = self.ADDS(R1, R5)
            R1 = v_378
            v_378 = R0
            R11 = self.ADC(R1, R2)
            R6 = self.check(R10) >> 28
            R1 = R11 | R9
            v_398 = R11
            R1 = self.ANDS(R1, R3)
            R3 = R11 & R9
            R9 = R1 | R3
            R3 = self.check(R11) << 30
            R4 = self.check(R11) >> 28
            R3 = R3 | self.check(R10) >> 2
            R4 = R4 | self.check(R10) << 4
            R6 = R6 | self.check(R11) << 4
            R3 = self.EORS(R3, R4)
            R4 = self.check(R11) << 25
            R4 = R4 | self.check(R10) >> 7
            R1 = v_3A4
            R3 = self.EORS(R3, R4)
            R4 = self.check(R11) >> 2
            R4 = R4 | self.check(R10) << 30
            v_39C = R10
            R4 = self.EORS(R4, R6)
            R6 = self.check(R11) >> 7
            R6 = R6 | self.check(R10) << 25
            R4 = self.EORS(R4, R6)
            R6 = R10 | R0
            R6 = self.ANDS(R6, R1)
            R1 = R10 & R0
            R1 = self.ORRS(R1, R6)
            R10 = LR
            R0 = self.ADDS(R4, R1)
            v_390 = R0
            R0 = self.ADC(R3, R9)
            v_38C = R0
            R0 = v_3A8
            R9 = R12
            R4 = self.ADDS(R5, R0)
            R0 = v_3AC
            v_3A8 = R4
            R0 = self.ADCS(R0, R2)
            R3 = self.LSRS(R4, 0xE)
            v_3AC = R0
            R1 = self.LSRS(R0, 0x12)
            R2 = self.LSRS(R0, 0xE)
            R1 = R1 | self.check(R4) << 14
            R2 = R2 | self.check(R4) << 18
            R1 = self.EORS(R1, R2)
            R2 = self.LSLS(R0, 0x17)
            R2 = R2 | self.check(R4) >> 9
            R3 = R3 | self.check(R0) << 18
            R11 = R1 ^ R2
            R2 = self.LSRS(R4, 0x12)
            R2 = R2 | self.check(R0) << 14
            R2 = self.EORS(R2, R3)
            R3 = self.LSRS(R0, 9)
            R3 = R3 | self.check(R4) << 23
            R2 = self.EORS(R2, R3)
            R3 = LR ^ R8
            R3 = self.ANDS(R3, R0)
            R0 = v_388
            LR = R3 ^ R8
            R5 = R12 ^ R0
            R5 = self.ANDS(R5, R4)
            R3 = R0
            R5 = self.EORS(R5, R0)
            R4 = self.rodata[0x10 * index + 14]
            R6 = self.rodata[0x10 * index + 15]
            R0 = v_380
            R4 = self.ADDS(R4, R0)
            R0 = v_37C
            R6 = self.ADCS(R6, R0)
            R0 = list_3B8[0x10 * index + 14]
            R1 = list_3B8[0x10 * index + 15]
            R1 = self.ADDS(R1, R4)
            R0 = self.ADCS(R0, R6)
            R1 = self.ADDS(R1, R5)
            R0 = self.ADC(R0, LR)
            R1 = self.ADDS(R1, R2)
            R2 = v_390
            R0 = self.ADC(R0, R11)
            R4 = R8
            LR = self.ADDS(R2, R1)
            R2 = v_38C
            R6 = R3
            R12 = self.ADC(R2, R0)
            R2 = v_384
            R8 = self.ADDS(R1, R2)
            R2 = v_36C
            R5 = self.ADC(R0, R2)

        list_638 = [
            self.check(LR),
            self.check(R12),
            self.check(v_39C),
            self.check(v_398),
            self.check(v_378),
            self.check(v_374),
            self.check(v_3A4),
            self.check(v_3A0),
            self.check(R8),
            self.check(R5),
            self.check(v_3A8),
            self.check(v_3AC),
            self.check(R9),
            self.check(R10),
            self.check(R6),
            self.check(R4),
        ]

        for i in range(8):
            R0 = param_list[2 * i]
            R1 = param_list[2 * i + 1]
            R0 = self.ADDS(R0, list_638[2 * i])
            R1 = self.ADCS(R1, list_638[2 * i + 1])
            param_list[2 * i] = R0
            param_list[2 * i + 1] = R1
        return param_list

    def hex_C52(self, list_6B0):
        list_8D8 = []
        for i in range(8):
            tmp = self.hex_list([list_6B0[2 * i + 1], list_6B0[2 * i]])
            list_8D8 = list_8D8 + tmp
        return list_8D8

    def toHex(self, num):
        return format(int(num), "x")

    def check(self, tmp):
        ss = ""
        if tmp < 0:
            ss = self.toHex(4294967296 + int(tmp))
        else:
            ss = self.toHex(tmp)
        if len(ss) > 8:
            size = len(ss)
            start = size - 8
            ss = ss[start:]
            tmp = int(self.parseLong(ss, 10, 16))
        return tmp  # 3035769959

    def ADDS(self, a, b):
        c = self.check(a) + self.check(b)
        if len(self.toHex(c)) > 8:
            self.CF = 1
        else:
            self.CF = 0
        result = self.check(c)
        return result

    def ANDS(self, a, b):
        return self.check(a & b)

    def EORS(self, a, b):
        return self.check(a ^ b)

    def ADC(self, a, b):

        c = self.check(a) + self.check(b)
        d = self.check(c + self.CF)
        return d

    def ADCS(self, a, b):
        c = self.check(a) + self.check(b)
        d = self.check(c + self.CF)
        if len(self.toHex(c)) > 8:
            self.CF = 1
        else:
            self.CF = 0
        return d

    def LSLS(self, num, k):
        result = self.bin_type(num)
        self.CF = result[k - 1]
        return self.check(self.check(num) << k)

    def LSRS(self, num, k):
        result = self.bin_type(num)
        self.CF = result[len(result) - k]
        return self.check(self.check(num) >> k)

    def ORRS(self, a, b):
        return self.check(a | b)

    def RRX(self, num):
        result = self.bin_type(num)
        lenght = len(result)
        s = str(self.CF) + result[: lenght - 1 - 0]
        return self.parseLong(s, 10, 2)

    def bin_type(self, num):
        result = ""
        num = self.check(num)
        lst = self.toBinaryString(num)
        for i in range(32):
            if i < len(lst):
                result += str(lst[i])
            else:
                result = "0" + result
        return result

    def UBFX(self, num, lsb, width):
        tmp_string = self.toBinaryString(num)
        while len(tmp_string) < 32:
            tmp_string = "0" + tmp_string
        lens = len(tmp_string)
        start = lens - lsb - width
        end = start - lsb
        a = int(self.parseLong(tmp_string[start : end - start], 10, 2))

        return int(self.parseLong(tmp_string[start : end - start], 10, 2))

    def UFTX(self, num):
        tmp_string = self.toBinaryString(num)
        start = len(tmp_string) - 8
        return self.parseLong(tmp_string[start:], 10, 2)

    def toBinaryString(self, num):
        return "{0:b}".format(num)

    def setData(self, data):
        self.__content_raw = data
        self.__content = data
        self.list_9C8 = self.hex_9C8()

    def hex_9C8(self):
        result = []
        for i in range(32):
            result.append(self.chooice(0, 0x100))
        return result

    def chooice(self, start, end):
        return int(random.uniform(0, 1) * (end + 1 - start) + start)

    def s2b(self, data):
        arr = []
        for i in range(len(data)):
            arr.append(data[i])
        return arr

    def hex_list(self, content):
        result = []
        for value in content:
            tmp = self.toHex(value)
            while len(tmp) < 8:
                tmp = "0" + tmp
            for i in range(4):
                start = 2 * i
                end = 2 * i + 2
                ss = tmp[start:end]
                result.append(int(self.parseLong(ss, 10, 16)))
        return result

    def parseLong(self, num, to_base=10, from_base=10):
        if isinstance(num, str):
            n = int(num, from_base)
        else:
            n = int(num)
        alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        if n < to_base:
            return alphabet[n]
        else:
            return self.parseLong(n // to_base, to_base) + alphabet[n % to_base]

    def byteArray2str(self, b):
        return binascii.hexlify(bytes(b)).decode()

    def changeByteArrayToLong(self, bytes):
        result = []
        for byte in bytes:
            if byte < 0:
                result.append(byte + 256)
            else:
                result.append(byte)
        return result
