import express from 'express';
import { createServer } from 'http';
import { createServer as createHttpsServer } from 'https';
import { Server } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// Încercăm să creăm server HTTPS dacă avem certificate, altfel HTTP
let server;
let protocol = 'http';
let port = process.env.PORT || 3000;

try {
    // Verificăm dacă există certificate SSL
    if (fs.existsSync('./ssl/cert.pem') && fs.existsSync('./ssl/key.pem')) {
        const httpsOptions = {
            key: fs.readFileSync('./ssl/key.pem'),
            cert: fs.readFileSync('./ssl/cert.pem')
        };
        server = createHttpsServer(httpsOptions, app);
        protocol = 'https';
        port = process.env.HTTPS_PORT || 3443;
        console.log('🔒 Using HTTPS server with SSL certificates');
    } else {
        server = createServer(app);
        console.log('⚠️  Using HTTP server (no SSL certificates found)');
        console.log('💡 For production, consider adding SSL certificates in ./ssl/ folder');
    }
} catch (error) {
    console.log('⚠️  SSL certificates not found, using HTTP server');
    server = createServer(app);
}

const io = new Server(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// Middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            connectSrc: ["'self'", "wss:", "ws:", "https:", "http:"],
            imgSrc: ["'self'", "data:", "https:"],
            mediaSrc: ["'self'", "https:", "http:"]
        }
    },
    crossOriginEmbedderPolicy: false,
    hsts: protocol === 'https' ? {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    } : false
}));

app.use(cors({
    origin: "*",
    methods: ["GET", "POST", "PUT", "DELETE"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true
}));

app.use(express.json());
app.use(express.static('.'));

// Servește fișierele statice
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Socket.IO pentru comunicare în timp real
io.on('connection', (socket) => {
    console.log('Client connected:', socket.id);
    
    socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
    });
    
    // Event-uri pentru casino
    socket.on('game-result', (data) => {
        // Broadcast rezultatul jocului la toți clienții conectați
        io.emit('game-update', data);
    });
    
    socket.on('leaderboard-update', (data) => {
        // Broadcast actualizarea leaderboard-ului
        io.emit('leaderboard-changed', data);
    });
    
    socket.on('chat-message', (data) => {
        // Broadcast mesajele de chat
        io.emit('new-chat-message', data);
    });
});

server.listen(port, () => {
    console.log(`🎰 TikTok Live Casino server running on port ${port}`);
    console.log(`📱 Open ${protocol}://localhost:${port} to access the casino`);
    console.log(`🔗 Connect your TikTok username to start the live casino!`);

    if (protocol === 'http') {
        console.log('');
        console.log('⚠️  IMPORTANT: Running on HTTP');
        console.log('🔒 For TikTok Live integration, you may need HTTPS');
        console.log('💡 To enable HTTPS, add SSL certificates to ./ssl/ folder:');
        console.log('   - ./ssl/cert.pem (certificate)');
        console.log('   - ./ssl/key.pem (private key)');
        console.log('');
    }
});
