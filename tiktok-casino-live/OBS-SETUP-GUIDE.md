# 🎬 G<PERSON>d Configurare OBS Studio pentru TikTok Live Casino

## 🚀 Pornire Rapidă

### Pasul 1: Pornește totul automat
```bash
./start-tiktok-live.sh
```

### Pasul 2: Configurează OBS Studio

#### A. <PERSON><PERSON>ri Stream pentru TikTok
1. **Deschide OBS Studio** (se pornește automat)
2. **File** → **Settings** → **Stream**
3. **Service**: Custom
4. **Server**: `rtmp://live.tiktok.com/live/`
5. **Stream Key**: [Vezi secțiunea Stream Key mai jos]

#### B. Setări Video pentru TikTok (Format Vertical)
1. **Settings** → **Video**
2. **Base (Canvas) Resolution**: 1080x1920
3. **Output (Scaled) Resolution**: 1080x1920
4. **Common FPS Values**: 30

#### C. Set<PERSON>ri Output
1. **Settings** → **Output**
2. **Output Mode**: Simple
3. **Video Bitrate**: 3000 Kbps
4. **Encoder**: x264
5. **Audio Bitrate**: 128

### Pasul 3: Adaugă Casino App ca Browser Source

#### A. Creează Scene
1. **Sources** → **+** → **Browser**
2. **Name**: "TikTok Casino"
3. **Create New**

#### B. Configurează Browser Source
- **URL**: `https://localhost:3443`
- **Width**: 1080
- **Height**: 1920
- **FPS**: 30
- **✅ Shutdown source when not visible**
- **✅ Refresh browser when scene becomes active**

#### C. Poziționare
1. **Fit to Screen** sau **Transform** → **Fit to Screen**
2. Verifică că aplicația casino ocupă tot ecranul

## 🔑 Obținere Stream Key pentru TikTok

### Metoda 1: TikTok Stream Key Generator
```bash
cd TikTokStreamKeyGenerator
source venv/bin/activate
python TikTokStreamKeyGenerator.py
```

### Metoda 2: TikTok Live Studio
1. Deschide TikTok Live Studio pe telefon/computer
2. Începe un live
3. Copiază stream key-ul generat

### Metoda 3: TikTok Creator Tools
1. Mergi la TikTok Creator Tools
2. Live → Go Live
3. Copiază RTMP URL și Stream Key

## 🎯 Configurare Avansată

### Setări Audio
- **Sample Rate**: 48000 Hz
- **Channels**: Stereo
- **Desktop Audio**: Activat (pentru sunetele casino-ului)

### Setări Hotkeys
- **Start Streaming**: F1
- **Stop Streaming**: F2
- **Start Recording**: F3

### Filtre Recomandate
1. **Color Correction** pentru brightness/contrast
2. **Noise Suppression** pentru audio
3. **Compressor** pentru audio uniform

## 🔧 Troubleshooting

### Problema: OBS nu pornește
```bash
# Încearcă cu environment curat
env -i PATH=/usr/bin:/bin:/usr/local/bin DISPLAY=$DISPLAY HOME=$HOME USER=$USER obs
```

### Problema: Casino app nu se încarcă în OBS
1. Verifică că serverul rulează: `curl -k https://localhost:3443`
2. Acceptă certificatul SSL în browser mai întâi
3. Restart Browser Source în OBS

### Problema: Stream nu se conectează la TikTok
1. Verifică stream key-ul
2. Verifică că TikTok Live este activ
3. Încearcă să reduci bitrate-ul la 2000 Kbps

## 📱 Format TikTok Optim

### Rezoluție Recomandată
- **1080x1920** (9:16 aspect ratio)
- **30 FPS**
- **3000 Kbps video bitrate**
- **128 Kbps audio bitrate**

### Layout Casino pentru TikTok
- Casino app ocupă tot ecranul vertical
- Chat și leaderboard vizibile
- Butoane de joc mari și clare
- Text lizibil pe telefon

## 🎮 Testare Live

### Pre-Live Checklist
- [ ] Casino server rulează la https://localhost:3443
- [ ] OBS Studio configurat cu setările corecte
- [ ] Browser Source adăugat și funcțional
- [ ] Stream key configurat
- [ ] Audio și video preview OK
- [ ] TikTok Live pregătit

### Test Stream
1. **Start Streaming** în OBS
2. Verifică pe TikTok că stream-ul apare
3. Testează interacțiunea casino (tap, gifts, etc.)
4. Verifică că leaderboard se actualizează

## 🎯 Tips pentru Success

### Engagement
- Anunță jocurile disponibile
- Explică cum să câștigi credite
- Interacționează cu chat-ul
- Arată leaderboard-ul regulat

### Performance
- Monitorizează CPU usage în OBS
- Verifică dropped frames
- Ajustează bitrate dacă e necesar
- Folosește wired internet connection

### Content
- Variază jocurile (ruletă, slots, blackjack)
- Creează momente de suspense
- Celebrează câștigătorii
- Încurajează participarea
