// TikTok Live Casino - Conectare la TikTok Live Studio existent
import { WebcastPushConnection } from 'tiktok-live-connector';

class TikTokLiveCasino {
    constructor() {
        this.connection = null;
        this.isConnected = false;
        this.players = new Map();
        this.currentCredits = 0;
        this.currentGame = null;
        this.leaderboard = [];
        
        this.initializeUI();
        this.setupEventListeners();
    }

    initializeUI() {
        this.elements = {
            connectionPanel: document.getElementById('connection-panel'),
            casinoGames: document.getElementById('casino-games'),
            liveChat: document.getElementById('live-chat'),
            usernameInput: document.getElementById('username-input'),
            connectBtn: document.getElementById('connect-btn'),
            connectionStatus: document.getElementById('connection-status'),
            liveIndicator: document.getElementById('live-indicator'),
            viewerCount: document.getElementById('viewer-count'),
            creditsDisplay: document.getElementById('credits-display'),
            gameArea: document.getElementById('game-area'),
            chatMessages: document.getElementById('chat-messages'),
            leaderboardList: document.getElementById('leaderboard-list')
        };
    }

    setupEventListeners() {
        this.elements.connectBtn.addEventListener('click', () => this.connectToTikTok());
        
        // Game buttons
        document.querySelectorAll('.game-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const game = e.target.closest('.game-btn').dataset.game;
                this.startGame(game);
            });
        });

        // Enter key pentru conectare
        this.elements.usernameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.connectToTikTok();
            }
        });
    }

    async connectToTikTok() {
        const username = this.elements.usernameInput.value.trim();
        
        if (!username) {
            this.updateConnectionStatus('Te rog introdu username-ul TikTok!', 'error');
            return;
        }

        this.elements.connectBtn.disabled = true;
        this.elements.connectBtn.textContent = 'Se conectează...';
        this.updateConnectionStatus('Se conectează la TikTok Live...', 'connecting');

        try {
            // Creează conexiunea la TikTok Live cu configurare HTTPS
            this.connection = new WebcastPushConnection(username, {
                processInitialData: true,
                enableExtendedGiftInfo: true,
                enableWebsocketUpgrade: true,
                requestPollingIntervalMs: 1000,
                sessionId: undefined,
                clientParams: {},
                requestHeaders: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Referer': 'https://www.tiktok.com/',
                    'Origin': 'https://www.tiktok.com'
                },
                websocketHeaders: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                },
                requestOptions: {
                    timeout: 10000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    }
                },
                websocketOptions: {
                    timeout: 10000
                }
            });

            // Event listeners pentru TikTok Live
            this.connection.on('connected', (state) => {
                this.onConnected(state);
            });

            this.connection.on('disconnected', () => {
                this.onDisconnected();
            });

            this.connection.on('streamEnd', () => {
                this.onStreamEnd();
            });

            this.connection.on('roomUser', (data) => {
                this.onViewerJoin(data);
            });

            this.connection.on('member', (data) => {
                this.onViewerJoin(data);
            });

            this.connection.on('chat', (data) => {
                this.onChatMessage(data);
            });

            this.connection.on('gift', (data) => {
                this.onGiftReceived(data);
            });

            this.connection.on('like', (data) => {
                this.onLikeReceived(data);
            });

            this.connection.on('share', (data) => {
                this.onShareReceived(data);
            });

            this.connection.on('follow', (data) => {
                this.onFollowReceived(data);
            });

            this.connection.on('error', (err) => {
                console.error('TikTok connection error:', err);
                this.updateConnectionStatus(`Eroare: ${err.message}`, 'error');
                this.elements.connectBtn.disabled = false;
                this.elements.connectBtn.textContent = 'Conectează Live Casino';
            });

            // Conectează la live
            await this.connection.connect();

        } catch (error) {
            console.error('Failed to connect:', error);
            this.updateConnectionStatus(`Nu s-a putut conecta: ${error.message}`, 'error');
            this.elements.connectBtn.disabled = false;
            this.elements.connectBtn.textContent = 'Conectează Live Casino';
        }
    }

    onConnected(state) {
        console.log('Connected to TikTok Live!', state);
        this.isConnected = true;
        
        this.updateConnectionStatus('✅ Conectat la TikTok Live!', 'success');
        this.elements.liveIndicator.textContent = 'LIVE';
        this.elements.liveIndicator.classList.add('online');
        
        // Ascunde panoul de conectare și arată casino-ul
        this.elements.connectionPanel.style.display = 'none';
        this.elements.casinoGames.style.display = 'grid';
        this.elements.liveChat.style.display = 'block';
        
        // Actualizează numărul de vizualizatori
        if (state.roomInfo && state.roomInfo.stats) {
            this.updateViewerCount(state.roomInfo.stats.viewerCount || 0);
        }

        this.addChatMessage('🎰', 'Casino Live este acum activ! Vizitatorii pot câștiga credite prin gifts, tap-uri și share-uri!');
    }

    onDisconnected() {
        console.log('Disconnected from TikTok Live');
        this.isConnected = false;
        this.updateConnectionStatus('Deconectat de la TikTok Live', 'error');
        this.elements.liveIndicator.textContent = 'OFFLINE';
        this.elements.liveIndicator.classList.remove('online');
    }

    onStreamEnd() {
        console.log('TikTok Live stream ended');
        this.updateConnectionStatus('Live-ul s-a încheiat', 'info');
        this.addChatMessage('📺', 'Live-ul s-a încheiat. Mulțumim pentru participare!');
    }

    onViewerJoin(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        
        if (!this.players.has(username)) {
            // Credite de start pentru jucători noi
            this.players.set(username, {
                username: username,
                credits: 100, // Credite de start
                totalGifts: 0,
                totalTaps: 0,
                totalShares: 0,
                joinTime: Date.now()
            });
            
            this.addChatMessage('👋', `${username} s-a alăturat casino-ului! +100 credite de start!`);
            this.updateLeaderboard();
        }
        
        this.updateViewerCount(this.players.size);
    }

    onChatMessage(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        const message = data.comment || '';
        
        this.addChatMessage(username, message);
        
        // Verifică comenzi de joc în chat
        this.processGameCommands(username, message);
    }

    onGiftReceived(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        const giftName = data.giftName || 'Gift';
        const giftCount = data.repeatCount || 1;
        const giftValue = data.diamondCount || 1;
        
        // Calculează credite pe baza gift-ului
        const creditsEarned = giftValue * giftCount * 10; // 1 diamond = 10 credite
        
        this.addCreditsToPlayer(username, creditsEarned, 'gift');
        this.addChatMessage('🎁', `${username} a trimis ${giftName} x${giftCount}! +${creditsEarned} credite!`);
        
        console.log(`Gift received: ${giftName} x${giftCount} from ${username} (+${creditsEarned} credits)`);
    }

    onLikeReceived(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        
        if (!this.players.has(username)) {
            this.onViewerJoin(data);
        }
        
        const player = this.players.get(username);
        player.totalTaps += 1;
        
        // 3000 tap-uri = 50 credite
        if (player.totalTaps % 3000 === 0) {
            this.addCreditsToPlayer(username, 50, 'taps');
            this.addChatMessage('👆', `${username} a atins 3000 tap-uri! +50 credite!`);
        }
    }

    onShareReceived(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        
        if (!this.players.has(username)) {
            this.onViewerJoin(data);
        }
        
        const player = this.players.get(username);
        player.totalShares += 1;
        
        // 300 share-uri = 30 credite
        if (player.totalShares % 300 === 0) {
            this.addCreditsToPlayer(username, 30, 'shares');
            this.addChatMessage('📤', `${username} a atins 300 share-uri! +30 credite!`);
        }
    }

    onFollowReceived(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        this.addCreditsToPlayer(username, 200, 'follow'); // Bonus pentru follow
        this.addChatMessage('❤️', `${username} a dat follow! +200 credite bonus!`);
    }

    addCreditsToPlayer(username, credits, source) {
        if (!this.players.has(username)) {
            this.players.set(username, {
                username: username,
                credits: 0,
                totalGifts: 0,
                totalTaps: 0,
                totalShares: 0,
                joinTime: Date.now()
            });
        }
        
        const player = this.players.get(username);
        player.credits += credits;
        
        if (source === 'gift') player.totalGifts += credits;
        
        this.updateLeaderboard();
        
        // Actualizează afișajul de credite pentru jucătorul curent (dacă este cazul)
        this.updateCreditsDisplay();
    }

    updateViewerCount(count) {
        this.elements.viewerCount.textContent = `${count} players`;
    }

    updateCreditsDisplay() {
        // Pentru demo, arată creditele totale ale tuturor jucătorilor
        const totalCredits = Array.from(this.players.values()).reduce((sum, player) => sum + player.credits, 0);
        this.elements.creditsDisplay.textContent = `${totalCredits} credite totale`;
    }

    updateConnectionStatus(message, type) {
        this.elements.connectionStatus.textContent = message;
        this.elements.connectionStatus.className = `connection-status ${type}`;
    }

    addChatMessage(username, message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';
        messageElement.innerHTML = `<strong>${username}:</strong> ${message}`;
        
        this.elements.chatMessages.appendChild(messageElement);
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    updateLeaderboard() {
        const sortedPlayers = Array.from(this.players.values())
            .sort((a, b) => b.credits - a.credits)
            .slice(0, 5);
        
        this.elements.leaderboardList.innerHTML = '';
        
        sortedPlayers.forEach((player, index) => {
            const item = document.createElement('div');
            item.className = 'leaderboard-item';
            item.innerHTML = `
                <span class="rank">${index + 1}.</span>
                <span class="player">${player.username}</span>
                <span class="credits">${player.credits}</span>
            `;
            this.elements.leaderboardList.appendChild(item);
        });
    }

    processGameCommands(username, message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('ruletă') || lowerMessage.includes('roulette')) {
            this.playGameForUser(username, 'roulette');
        } else if (lowerMessage.includes('slots') || lowerMessage.includes('păcănele')) {
            this.playGameForUser(username, 'slots');
        } else if (lowerMessage.includes('blackjack') || lowerMessage.includes('21')) {
            this.playGameForUser(username, 'blackjack');
        } else if (lowerMessage.includes('zaruri') || lowerMessage.includes('dice')) {
            this.playGameForUser(username, 'dice');
        }
    }

    playGameForUser(username, gameType) {
        if (!this.players.has(username)) {
            this.addChatMessage('🎰', `${username}, trebuie să te alături live-ului pentru a juca!`);
            return;
        }
        
        const player = this.players.get(username);
        const gameCosts = {
            roulette: 10,
            slots: 5,
            blackjack: 15,
            dice: 8
        };
        
        const cost = gameCosts[gameType];
        
        if (player.credits < cost) {
            this.addChatMessage('🎰', `${username}, ai nevoie de ${cost} credite pentru ${gameType}! Ai doar ${player.credits}.`);
            return;
        }
        
        // Deduce creditele
        player.credits -= cost;
        
        // Simulează jocul cu house advantage
        const result = this.simulateGame(gameType, cost);
        
        if (result.win) {
            player.credits += result.payout;
            this.addChatMessage('🎉', `${username} a câștigat ${result.payout} credite la ${gameType}! ${result.message}`);
        } else {
            this.addChatMessage('😔', `${username} a pierdut la ${gameType}. ${result.message}`);
        }
        
        this.updateLeaderboard();
        this.updateCreditsDisplay();
    }

    simulateGame(gameType, bet) {
        // House advantage: 15% pentru casino
        const winChance = 0.45; // 45% șanse de câștig pentru jucător
        const isWin = Math.random() < winChance;
        
        if (isWin) {
            const multipliers = {
                roulette: 2.5,
                slots: 3.0,
                blackjack: 2.0,
                dice: 2.2
            };
            
            const payout = Math.floor(bet * multipliers[gameType]);
            
            return {
                win: true,
                payout: payout,
                message: this.getWinMessage(gameType)
            };
        } else {
            return {
                win: false,
                payout: 0,
                message: this.getLoseMessage(gameType)
            };
        }
    }

    getWinMessage(gameType) {
        const messages = {
            roulette: 'Bila a căzut pe numărul tău!',
            slots: 'Trei simboluri identice!',
            blackjack: '21! Blackjack perfect!',
            dice: 'Zarurile au fost norocoase!'
        };
        return messages[gameType] || 'Câștig!';
    }

    getLoseMessage(gameType) {
        const messages = {
            roulette: 'Bila a căzut pe alt număr.',
            slots: 'Simbolurile nu s-au aliniat.',
            blackjack: 'Dealer-ul a câștigat.',
            dice: 'Zarurile nu au fost norocoase.'
        };
        return messages[gameType] || 'Încearcă din nou!';
    }

    startGame(gameType) {
        this.currentGame = gameType;
        
        const gameDescriptions = {
            roulette: '🎯 Ruletă - Alege un număr și vezi dacă bila cade pe el!',
            slots: '🎰 Slots - Trei simboluri identice pentru câștig!',
            blackjack: '🃏 Blackjack - Încearcă să ajungi la 21!',
            dice: '🎲 Zaruri - Ghicește suma zarurilor!'
        };
        
        this.elements.gameArea.innerHTML = `
            <div class="active-game">
                <h3>${gameDescriptions[gameType]}</h3>
                <p>Jucătorii pot scrie în chat numele jocului pentru a juca!</p>
                <p>Exemplu: "ruletă", "slots", "blackjack", "zaruri"</p>
            </div>
        `;
        
        this.addChatMessage('🎮', `Jocul ${gameType} este acum activ! Scrieți numele jocului în chat pentru a juca!`);
    }
}

// Inițializează aplicația când pagina se încarcă
document.addEventListener('DOMContentLoaded', () => {
    window.casino = new TikTokLiveCasino();
    console.log('TikTok Live Casino initialized!');
});
