// TikTok Live Casino - Conectare la TikTok Live Studio existent
import { WebcastPushConnection } from 'tiktok-live-connector';

class TikTokLiveCasino {
    constructor() {
        console.log('🏗️ Constructing TikTokLiveCasino...');

        this.connection = null;
        this.isConnected = false;
        this.players = new Map();
        this.credits = 0; // Renamed from currentCredits
        this.currentGame = null;
        this.leaderboard = [];

        try {
            this.initializeUI();
            this.setupEventListeners();
            console.log('✅ Casino constructor completed successfully!');
        } catch (error) {
            console.error('❌ Error in constructor:', error);
        }
    }

    initializeUI() {
        this.elements = {
            connectionPanel: document.getElementById('connection-panel'),
            casinoGames: document.getElementById('casino-games'),
            liveChat: document.getElementById('live-chat'),
            usernameInput: document.getElementById('username-input'),
            connectBtn: document.getElementById('connect-btn'),
            connectionStatus: document.getElementById('connection-status'),
            liveIndicator: document.getElementById('live-indicator'),
            viewerCount: document.getElementById('viewer-count'),
            creditsDisplay: document.getElementById('credits-display'),
            gameArea: document.getElementById('game-area'),
            chatMessages: document.getElementById('chat-messages'),
            leaderboardList: document.getElementById('leaderboard-list')
        };
    }

    setupEventListeners() {
        console.log('🔧 Setting up event listeners...');

        // Connect button
        if (this.elements.connectBtn) {
            this.elements.connectBtn.addEventListener('click', () => {
                console.log('🔗 Connect button clicked');
                this.connectToTikTok();
            });
            console.log('✅ Connect button listener added');
        } else {
            console.error('❌ Connect button not found!');
        }

        // Test button pentru a testa casino-ul fără TikTok
        const testBtn = document.getElementById('test-btn');
        if (testBtn) {
            testBtn.addEventListener('click', () => {
                console.log('🧪 Test button clicked');
                this.enableTestMode();
            });
            console.log('✅ Test button listener added');
        } else {
            console.error('❌ Test button not found!');
        }

        // Game buttons
        document.querySelectorAll('.game-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const game = e.target.closest('.game-btn').dataset.game;
                console.log('🎮 Game button clicked:', game);
                this.startGame(game);
            });
        });
        console.log('✅ Game buttons listeners added');

        // Enter key pentru conectare
        if (this.elements.usernameInput) {
            this.elements.usernameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    console.log('⌨️ Enter key pressed');
                    this.connectToTikTok();
                }
            });
            console.log('✅ Username input listener added');
        } else {
            console.error('❌ Username input not found!');
        }

        console.log('🎉 All event listeners set up!');
    }

    enableTestMode() {
        console.log('🧪 Enabling test mode...');

        try {
            // Verifică elementele
            console.log('Elements check:', this.elements);

            // Simulează conexiunea reușită
            this.updateConnectionStatus('✅ Mod Test Activat!', 'success');

            if (this.elements.liveIndicator) {
                this.elements.liveIndicator.textContent = 'TEST';
                this.elements.liveIndicator.classList.add('online');
            }

            // Ascunde panoul de conectare și arată casino-ul
            if (this.elements.connectionPanel) {
                this.elements.connectionPanel.style.display = 'none';
                console.log('✅ Connection panel hidden');
            }

            if (this.elements.casinoGames) {
                this.elements.casinoGames.style.display = 'grid';
                console.log('✅ Casino games shown');
            }

            if (this.elements.liveChat) {
                this.elements.liveChat.style.display = 'block';
                console.log('✅ Live chat shown');
            }

            // Setează credite de test
            this.credits = 1000;
            this.updateCreditsDisplay();
            console.log('✅ Credits set to 1000');

            // Adaugă mesaj în chat
            this.addChatMessage('🤖 Sistem', 'Mod test activat! Ai 1000 credite pentru a testa jocurile.', 'system');

            // Simulează câțiva jucători în leaderboard
            this.leaderboard = [
                { username: 'TestPlayer1', credits: 2500 },
                { username: 'TestPlayer2', credits: 1800 },
                { username: 'TestPlayer3', credits: 1200 }
            ];
            this.updateLeaderboard();

            console.log('🎉 Test mode activated successfully!');

        } catch (error) {
            console.error('❌ Error in enableTestMode:', error);
        }
    }

    async connectToTikTok() {
        const username = this.elements.usernameInput.value.trim();
        
        if (!username) {
            this.updateConnectionStatus('Te rog introdu username-ul TikTok!', 'error');
            return;
        }

        this.elements.connectBtn.disabled = true;
        this.elements.connectBtn.textContent = 'Se conectează...';
        this.updateConnectionStatus('Se conectează la TikTok Live...', 'connecting');

        try {
            // Creează conexiunea la TikTok Live cu configurare HTTPS
            this.connection = new WebcastPushConnection(username, {
                processInitialData: true,
                enableExtendedGiftInfo: true,
                enableWebsocketUpgrade: true,
                requestPollingIntervalMs: 1000,
                sessionId: undefined,
                clientParams: {},
                requestHeaders: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Referer': 'https://www.tiktok.com/',
                    'Origin': 'https://www.tiktok.com'
                },
                websocketHeaders: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                },
                requestOptions: {
                    timeout: 10000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                    }
                },
                websocketOptions: {
                    timeout: 10000
                }
            });

            // Event listeners pentru TikTok Live
            this.connection.on('connected', (state) => {
                this.onConnected(state);
            });

            this.connection.on('disconnected', () => {
                this.onDisconnected();
            });

            this.connection.on('streamEnd', () => {
                this.onStreamEnd();
            });

            this.connection.on('roomUser', (data) => {
                this.onViewerJoin(data);
            });

            this.connection.on('member', (data) => {
                this.onViewerJoin(data);
            });

            this.connection.on('chat', (data) => {
                this.onChatMessage(data);
            });

            this.connection.on('gift', (data) => {
                this.onGiftReceived(data);
            });

            this.connection.on('like', (data) => {
                this.onLikeReceived(data);
            });

            this.connection.on('share', (data) => {
                this.onShareReceived(data);
            });

            this.connection.on('follow', (data) => {
                this.onFollowReceived(data);
            });

            this.connection.on('error', (err) => {
                console.error('TikTok connection error:', err);
                this.updateConnectionStatus(`Eroare: ${err.message}`, 'error');
                this.elements.connectBtn.disabled = false;
                this.elements.connectBtn.textContent = 'Conectează Live Casino';
            });

            // Conectează la live
            await this.connection.connect();

        } catch (error) {
            console.error('Failed to connect:', error);
            this.updateConnectionStatus(`Nu s-a putut conecta: ${error.message}`, 'error');
            this.elements.connectBtn.disabled = false;
            this.elements.connectBtn.textContent = 'Conectează Live Casino';
        }
    }

    onConnected(state) {
        console.log('Connected to TikTok Live!', state);
        this.isConnected = true;
        
        this.updateConnectionStatus('✅ Conectat la TikTok Live!', 'success');
        this.elements.liveIndicator.textContent = 'LIVE';
        this.elements.liveIndicator.classList.add('online');
        
        // Ascunde panoul de conectare și arată casino-ul
        this.elements.connectionPanel.style.display = 'none';
        this.elements.casinoGames.style.display = 'grid';
        this.elements.liveChat.style.display = 'block';
        
        // Actualizează numărul de vizualizatori
        if (state.roomInfo && state.roomInfo.stats) {
            this.updateViewerCount(state.roomInfo.stats.viewerCount || 0);
        }

        this.addChatMessage('🎰', 'Casino Live este acum activ! Vizitatorii pot câștiga credite prin gifts, tap-uri și share-uri!');
    }

    onDisconnected() {
        console.log('Disconnected from TikTok Live');
        this.isConnected = false;
        this.updateConnectionStatus('Deconectat de la TikTok Live', 'error');
        this.elements.liveIndicator.textContent = 'OFFLINE';
        this.elements.liveIndicator.classList.remove('online');
    }

    onStreamEnd() {
        console.log('TikTok Live stream ended');
        this.updateConnectionStatus('Live-ul s-a încheiat', 'info');
        this.addChatMessage('📺', 'Live-ul s-a încheiat. Mulțumim pentru participare!');
    }

    onViewerJoin(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        
        if (!this.players.has(username)) {
            // Credite de start pentru jucători noi
            this.players.set(username, {
                username: username,
                credits: 100, // Credite de start
                totalGifts: 0,
                totalTaps: 0,
                totalShares: 0,
                joinTime: Date.now()
            });
            
            this.addChatMessage('👋', `${username} s-a alăturat casino-ului! +100 credite de start!`);
            this.updateLeaderboard();
        }
        
        this.updateViewerCount(this.players.size);
    }

    onChatMessage(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        const message = data.comment || '';
        
        this.addChatMessage(username, message);
        
        // Verifică comenzi de joc în chat
        this.processGameCommands(username, message);
    }

    onGiftReceived(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        const giftName = data.giftName || 'Gift';
        const giftCount = data.repeatCount || 1;
        const giftValue = data.diamondCount || 1;
        
        // Calculează credite pe baza gift-ului
        const creditsEarned = giftValue * giftCount * 10; // 1 diamond = 10 credite
        
        this.addCreditsToPlayer(username, creditsEarned, 'gift');
        this.addChatMessage('🎁', `${username} a trimis ${giftName} x${giftCount}! +${creditsEarned} credite!`);
        
        console.log(`Gift received: ${giftName} x${giftCount} from ${username} (+${creditsEarned} credits)`);
    }

    onLikeReceived(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        
        if (!this.players.has(username)) {
            this.onViewerJoin(data);
        }
        
        const player = this.players.get(username);
        player.totalTaps += 1;
        
        // 3000 tap-uri = 50 credite
        if (player.totalTaps % 3000 === 0) {
            this.addCreditsToPlayer(username, 50, 'taps');
            this.addChatMessage('👆', `${username} a atins 3000 tap-uri! +50 credite!`);
        }
    }

    onShareReceived(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        
        if (!this.players.has(username)) {
            this.onViewerJoin(data);
        }
        
        const player = this.players.get(username);
        player.totalShares += 1;
        
        // 300 share-uri = 30 credite
        if (player.totalShares % 300 === 0) {
            this.addCreditsToPlayer(username, 30, 'shares');
            this.addChatMessage('📤', `${username} a atins 300 share-uri! +30 credite!`);
        }
    }

    onFollowReceived(data) {
        const username = data.uniqueId || data.nickname || 'Anonim';
        this.addCreditsToPlayer(username, 200, 'follow'); // Bonus pentru follow
        this.addChatMessage('❤️', `${username} a dat follow! +200 credite bonus!`);
    }

    addCreditsToPlayer(username, credits, source) {
        if (!this.players.has(username)) {
            this.players.set(username, {
                username: username,
                credits: 0,
                totalGifts: 0,
                totalTaps: 0,
                totalShares: 0,
                joinTime: Date.now()
            });
        }
        
        const player = this.players.get(username);
        player.credits += credits;
        
        if (source === 'gift') player.totalGifts += credits;
        
        this.updateLeaderboard();
        
        // Actualizează afișajul de credite pentru jucătorul curent (dacă este cazul)
        this.updateCreditsDisplay();
    }

    updateViewerCount(count) {
        this.elements.viewerCount.textContent = `${count} players`;
    }

    updateCreditsDisplay() {
        if (this.elements.creditsDisplay) {
            // Arată creditele personale ale jucătorului curent
            this.elements.creditsDisplay.textContent = `${this.credits} credite`;
            console.log(`💰 Credits updated: ${this.credits}`);
        } else {
            console.error('❌ Credits display element not found!');
        }
    }

    updateConnectionStatus(message, type) {
        this.elements.connectionStatus.textContent = message;
        this.elements.connectionStatus.className = `connection-status ${type}`;
    }

    addChatMessage(username, message, type = 'normal') {
        if (this.elements.chatMessages) {
            const messageElement = document.createElement('div');
            messageElement.className = `chat-message ${type}`;
            messageElement.innerHTML = `<strong>${username}:</strong> ${message}`;

            this.elements.chatMessages.appendChild(messageElement);
            this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;

            console.log(`💬 Chat message added: ${username}: ${message}`);
        } else {
            console.error('❌ Chat messages element not found!');
            console.log(`💬 Would have added: ${username}: ${message}`);
        }
    }

    updateLeaderboard() {
        const sortedPlayers = Array.from(this.players.values())
            .sort((a, b) => b.credits - a.credits)
            .slice(0, 5);
        
        this.elements.leaderboardList.innerHTML = '';
        
        sortedPlayers.forEach((player, index) => {
            const item = document.createElement('div');
            item.className = 'leaderboard-item';
            item.innerHTML = `
                <span class="rank">${index + 1}.</span>
                <span class="player">${player.username}</span>
                <span class="credits">${player.credits}</span>
            `;
            this.elements.leaderboardList.appendChild(item);
        });
    }

    processGameCommands(username, message) {
        const lowerMessage = message.toLowerCase();
        
        if (lowerMessage.includes('ruletă') || lowerMessage.includes('roulette')) {
            this.playGameForUser(username, 'roulette');
        } else if (lowerMessage.includes('slots') || lowerMessage.includes('păcănele')) {
            this.playGameForUser(username, 'slots');
        } else if (lowerMessage.includes('blackjack') || lowerMessage.includes('21')) {
            this.playGameForUser(username, 'blackjack');
        } else if (lowerMessage.includes('zaruri') || lowerMessage.includes('dice')) {
            this.playGameForUser(username, 'dice');
        }
    }

    playGameForUser(username, gameType) {
        if (!this.players.has(username)) {
            this.addChatMessage('🎰', `${username}, trebuie să te alături live-ului pentru a juca!`);
            return;
        }
        
        const player = this.players.get(username);
        const gameCosts = {
            roulette: 10,
            slots: 5,
            blackjack: 15,
            dice: 8
        };
        
        const cost = gameCosts[gameType];
        
        if (player.credits < cost) {
            this.addChatMessage('🎰', `${username}, ai nevoie de ${cost} credite pentru ${gameType}! Ai doar ${player.credits}.`);
            return;
        }
        
        // Deduce creditele
        player.credits -= cost;
        
        // Simulează jocul cu house advantage
        const result = this.simulateGame(gameType, cost);
        
        if (result.win) {
            player.credits += result.payout;
            this.addChatMessage('🎉', `${username} a câștigat ${result.payout} credite la ${gameType}! ${result.message}`);
        } else {
            this.addChatMessage('😔', `${username} a pierdut la ${gameType}. ${result.message}`);
        }
        
        this.updateLeaderboard();
        this.updateCreditsDisplay();
    }

    simulateGame(gameType, bet) {
        // House advantage: 15% pentru casino
        const winChance = 0.45; // 45% șanse de câștig pentru jucător
        const isWin = Math.random() < winChance;
        
        if (isWin) {
            const multipliers = {
                roulette: 2.5,
                slots: 3.0,
                blackjack: 2.0,
                dice: 2.2
            };
            
            const payout = Math.floor(bet * multipliers[gameType]);
            
            return {
                win: true,
                payout: payout,
                message: this.getWinMessage(gameType)
            };
        } else {
            return {
                win: false,
                payout: 0,
                message: this.getLoseMessage(gameType)
            };
        }
    }

    getWinMessage(gameType) {
        const messages = {
            roulette: 'Bila a căzut pe numărul tău!',
            slots: 'Trei simboluri identice!',
            blackjack: '21! Blackjack perfect!',
            dice: 'Zarurile au fost norocoase!'
        };
        return messages[gameType] || 'Câștig!';
    }

    getLoseMessage(gameType) {
        const messages = {
            roulette: 'Bila a căzut pe alt număr.',
            slots: 'Simbolurile nu s-au aliniat.',
            blackjack: 'Dealer-ul a câștigat.',
            dice: 'Zarurile nu au fost norocoase.'
        };
        return messages[gameType] || 'Încearcă din nou!';
    }

    startGame(gameType) {
        this.currentGame = gameType;

        // Verifică dacă jucătorul are suficiente credite
        const gameCosts = {
            roulette: 10,
            slots: 5,
            blackjack: 15,
            dice: 8
        };

        const cost = gameCosts[gameType];
        if (this.credits < cost) {
            this.addChatMessage('🚫 Sistem', `Nu ai suficiente credite! Ai nevoie de ${cost} credite pentru ${gameType}.`, 'error');
            return;
        }

        // Scade creditele
        this.credits -= cost;
        this.updateCreditsDisplay();

        // Creează interfața jocului
        switch(gameType) {
            case 'roulette':
                this.createRouletteGame();
                break;
            case 'slots':
                this.createSlotsGame();
                break;
            case 'blackjack':
                this.createBlackjackGame();
                break;
            case 'dice':
                this.createDiceGame();
                break;
        }

        this.addChatMessage('🎮 Sistem', `Jocul ${gameType} a început! Costul: ${cost} credite.`, 'game');
    }

    createRouletteGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🎯 RULETĂ CASINO</div>
                <div class="game-board">
                    <div class="roulette-wheel">
                        <div class="roulette-ball"></div>
                    </div>
                    <div class="betting-interface">
                        <h4>Alege numărul tău norocos (0-36):</h4>
                        <div class="number-grid">
                            ${Array.from({length: 37}, (_, i) => {
                                const isRed = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36].includes(i);
                                const colorClass = i === 0 ? '' : (isRed ? 'red' : 'black');
                                return `<button class="number-btn ${colorClass}" data-number="${i}">${i}</button>`;
                            }).join('')}
                        </div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="spin-roulette">🎲 ÎNVÂRTE RULETA</button>
                    </div>
                    <div id="roulette-result"></div>
                </div>
            </div>
        `;

        this.setupRouletteControls();
    }

    setupRouletteControls() {
        let selectedNumber = null;

        // Number selection
        document.querySelectorAll('.number-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // Remove previous selection
                document.querySelectorAll('.number-btn').forEach(b => b.classList.remove('selected'));

                // Select new number
                e.target.classList.add('selected');
                selectedNumber = parseInt(e.target.dataset.number);

                // Enable spin button
                document.getElementById('spin-roulette').disabled = false;
            });
        });

        // Spin button
        document.getElementById('spin-roulette').addEventListener('click', () => {
            if (selectedNumber === null) {
                this.addChatMessage('🚫 Sistem', 'Alege un număr mai întâi!', 'error');
                return;
            }

            this.spinRoulette(selectedNumber);
        });
    }

    spinRoulette(selectedNumber) {
        const spinBtn = document.getElementById('spin-roulette');
        spinBtn.disabled = true;
        spinBtn.textContent = '🎲 SE ÎNVÂRTE...';

        // Animate wheel
        const wheel = document.querySelector('.roulette-wheel');
        wheel.style.animation = 'spin 4s ease-out';

        setTimeout(() => {
            const winningNumber = Math.floor(Math.random() * 37);
            const isWin = winningNumber === selectedNumber;

            const resultDiv = document.getElementById('roulette-result');

            if (isWin) {
                const winAmount = 250; // 25x bet
                this.credits += winAmount;
                this.updateCreditsDisplay();

                resultDiv.innerHTML = `
                    <div class="game-result win">
                        🎉 CÂȘTIG! 🎉<br>
                        Numărul câștigător: ${winningNumber}<br>
                        Ai câștigat ${winAmount} credite!
                    </div>
                `;

                this.addChatMessage('🎯 Ruletă', `🎉 JACKPOT! Numărul ${winningNumber} a câștigat ${winAmount} credite!`, 'win');
            } else {
                resultDiv.innerHTML = `
                    <div class="game-result lose">
                        😔 Ai pierdut!<br>
                        Numărul câștigător: ${winningNumber}<br>
                        Numărul tău: ${selectedNumber}
                    </div>
                `;

                this.addChatMessage('🎯 Ruletă', `Numărul câștigător a fost ${winningNumber}. Încearcă din nou!`, 'lose');
            }

            // Reset for next game
            setTimeout(() => {
                wheel.style.animation = '';
                spinBtn.disabled = false;
                spinBtn.textContent = '🎲 ÎNVÂRTE RULETA';
                document.querySelectorAll('.number-btn').forEach(b => b.classList.remove('selected'));
            }, 3000);

        }, 4000);
    }

    createSlotsGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🎰 SLOTS CASINO</div>
                <div class="game-board">
                    <div class="slots-machine">
                        <div class="slot-reel" id="reel1">🍒</div>
                        <div class="slot-reel" id="reel2">🍋</div>
                        <div class="slot-reel" id="reel3">🍊</div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="spin-slots">🎰 SPIN!</button>
                    </div>
                    <div id="slots-result"></div>
                    <div class="betting-interface">
                        <h4>💰 Combinații câștigătoare:</h4>
                        <p>🍒🍒🍒 = 150 credite | 🍋🍋🍋 = 100 credite | 🍊🍊🍊 = 75 credite</p>
                        <p>🔔🔔🔔 = 200 credite | 💎💎💎 = 500 credite | 🎰🎰🎰 = 1000 credite</p>
                    </div>
                </div>
            </div>
        `;

        this.setupSlotsControls();
    }

    setupSlotsControls() {
        document.getElementById('spin-slots').addEventListener('click', () => {
            this.spinSlots();
        });
    }

    spinSlots() {
        const spinBtn = document.getElementById('spin-slots');
        spinBtn.disabled = true;
        spinBtn.textContent = '🎰 SPINNING...';

        const symbols = ['🍒', '🍋', '🍊', '🔔', '💎', '🎰'];
        const reels = [document.getElementById('reel1'), document.getElementById('reel2'), document.getElementById('reel3')];

        // Animate reels
        reels.forEach((reel, index) => {
            reel.classList.add('spinning');

            // Change symbols during spin
            const spinInterval = setInterval(() => {
                reel.textContent = symbols[Math.floor(Math.random() * symbols.length)];
            }, 100);

            // Stop spinning after delay
            setTimeout(() => {
                clearInterval(spinInterval);
                reel.classList.remove('spinning');

                // Final symbol
                const finalSymbol = symbols[Math.floor(Math.random() * symbols.length)];
                reel.textContent = finalSymbol;

                // Check for win after all reels stop
                if (index === 2) {
                    setTimeout(() => this.checkSlotsWin(), 500);
                }
            }, 2000 + (index * 500));
        });
    }

    checkSlotsWin() {
        const reel1 = document.getElementById('reel1').textContent;
        const reel2 = document.getElementById('reel2').textContent;
        const reel3 = document.getElementById('reel3').textContent;

        const resultDiv = document.getElementById('slots-result');
        const spinBtn = document.getElementById('spin-slots');

        let winAmount = 0;

        if (reel1 === reel2 && reel2 === reel3) {
            // Three of a kind
            const payouts = {
                '🍒': 150,
                '🍋': 100,
                '🍊': 75,
                '🔔': 200,
                '💎': 500,
                '🎰': 1000
            };

            winAmount = payouts[reel1] || 50;

            this.credits += winAmount;
            this.updateCreditsDisplay();

            resultDiv.innerHTML = `
                <div class="game-result win">
                    🎉 JACKPOT! 🎉<br>
                    ${reel1}${reel2}${reel3}<br>
                    Ai câștigat ${winAmount} credite!
                </div>
            `;

            this.addChatMessage('🎰 Slots', `🎉 JACKPOT! ${reel1}${reel2}${reel3} = ${winAmount} credite!`, 'win');
        } else {
            resultDiv.innerHTML = `
                <div class="game-result lose">
                    😔 Încearcă din nou!<br>
                    ${reel1}${reel2}${reel3}<br>
                    Nu ai câștigat de această dată.
                </div>
            `;

            this.addChatMessage('🎰 Slots', `${reel1}${reel2}${reel3} - Încearcă din nou!`, 'lose');
        }

        // Reset for next game
        setTimeout(() => {
            spinBtn.disabled = false;
            spinBtn.textContent = '🎰 SPIN!';
        }, 3000);
    }

    createDiceGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🎲 ZARURI CASINO</div>
                <div class="game-board">
                    <div class="dice-container">
                        <div class="dice" id="dice1">🎲</div>
                        <div class="dice" id="dice2">🎲</div>
                    </div>
                    <div class="betting-interface">
                        <h4>Ghicește suma zarurilor (2-12):</h4>
                        <div class="bet-amount">
                            <label>Suma ta:</label>
                            <input type="number" class="bet-input" id="dice-guess" min="2" max="12" placeholder="2-12">
                        </div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="roll-dice">🎲 ARUNCĂ ZARURILE</button>
                    </div>
                    <div id="dice-result"></div>
                </div>
            </div>
        `;

        this.setupDiceControls();
    }

    setupDiceControls() {
        document.getElementById('roll-dice').addEventListener('click', () => {
            const guess = parseInt(document.getElementById('dice-guess').value);

            if (!guess || guess < 2 || guess > 12) {
                this.addChatMessage('🚫 Sistem', 'Introdu o sumă validă între 2 și 12!', 'error');
                return;
            }

            this.rollDice(guess);
        });
    }

    rollDice(guess) {
        const rollBtn = document.getElementById('roll-dice');
        rollBtn.disabled = true;
        rollBtn.textContent = '🎲 SE ARUNCĂ...';

        const dice1 = document.getElementById('dice1');
        const dice2 = document.getElementById('dice2');

        // Animate dice
        dice1.classList.add('rolling');
        dice2.classList.add('rolling');

        setTimeout(() => {
            const roll1 = Math.floor(Math.random() * 6) + 1;
            const roll2 = Math.floor(Math.random() * 6) + 1;
            const total = roll1 + roll2;

            dice1.textContent = roll1;
            dice2.textContent = roll2;
            dice1.classList.remove('rolling');
            dice2.classList.remove('rolling');

            const resultDiv = document.getElementById('dice-result');

            if (total === guess) {
                const winAmount = 176; // 22x bet
                this.credits += winAmount;
                this.updateCreditsDisplay();

                resultDiv.innerHTML = `
                    <div class="game-result win">
                        🎉 PERFECT! 🎉<br>
                        Zarurile: ${roll1} + ${roll2} = ${total}<br>
                        Ghicirea ta: ${guess}<br>
                        Ai câștigat ${winAmount} credite!
                    </div>
                `;

                this.addChatMessage('🎲 Zaruri', `🎉 PERFECT! ${roll1}+${roll2}=${total} = ${winAmount} credite!`, 'win');
            } else {
                resultDiv.innerHTML = `
                    <div class="game-result lose">
                        😔 Aproape!<br>
                        Zarurile: ${roll1} + ${roll2} = ${total}<br>
                        Ghicirea ta: ${guess}<br>
                        Încearcă din nou!
                    </div>
                `;

                this.addChatMessage('🎲 Zaruri', `Zarurile au dat ${total}, tu ai ghicit ${guess}. Încearcă din nou!`, 'lose');
            }

            // Reset for next game
            setTimeout(() => {
                rollBtn.disabled = false;
                rollBtn.textContent = '🎲 ARUNCĂ ZARURILE';
                document.getElementById('dice-guess').value = '';
            }, 3000);

        }, 2000);
    }

    createBlackjackGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🃏 BLACKJACK CASINO</div>
                <div class="game-board">
                    <div class="blackjack-table">
                        <div class="dealer-area">
                            <h4>🎩 Dealer:</h4>
                            <div class="card-area" id="dealer-cards"></div>
                            <div id="dealer-score">Scor: ?</div>
                        </div>
                        <div class="player-area">
                            <h4>👤 Tu:</h4>
                            <div class="card-area" id="player-cards"></div>
                            <div id="player-score">Scor: 0</div>
                        </div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="hit-btn">🃏 HIT</button>
                        <button class="control-btn" id="stand-btn">✋ STAND</button>
                        <button class="control-btn" id="new-blackjack">🆕 JOC NOU</button>
                    </div>
                    <div id="blackjack-result"></div>
                </div>
            </div>
        `;

        this.setupBlackjackControls();
        this.startNewBlackjackGame();
    }

    setupBlackjackControls() {
        document.getElementById('hit-btn').addEventListener('click', () => this.blackjackHit());
        document.getElementById('stand-btn').addEventListener('click', () => this.blackjackStand());
        document.getElementById('new-blackjack').addEventListener('click', () => this.startNewBlackjackGame());
    }

    startNewBlackjackGame() {
        this.deck = this.createDeck();
        this.playerCards = [];
        this.dealerCards = [];
        this.gameOver = false;

        // Deal initial cards
        this.playerCards.push(this.drawCard());
        this.dealerCards.push(this.drawCard());
        this.playerCards.push(this.drawCard());
        this.dealerCards.push(this.drawCard());

        this.updateBlackjackDisplay();

        // Enable game buttons
        document.getElementById('hit-btn').disabled = false;
        document.getElementById('stand-btn').disabled = false;

        // Clear result
        document.getElementById('blackjack-result').innerHTML = '';
    }

    createDeck() {
        const suits = ['♠', '♥', '♦', '♣'];
        const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        const deck = [];

        for (let suit of suits) {
            for (let rank of ranks) {
                deck.push({ suit, rank });
            }
        }

        // Shuffle deck
        for (let i = deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [deck[i], deck[j]] = [deck[j], deck[i]];
        }

        return deck;
    }

    drawCard() {
        return this.deck.pop();
    }

    getCardValue(card) {
        if (card.rank === 'A') return 11;
        if (['J', 'Q', 'K'].includes(card.rank)) return 10;
        return parseInt(card.rank);
    }

    calculateScore(cards) {
        let score = 0;
        let aces = 0;

        for (let card of cards) {
            if (card.rank === 'A') {
                aces++;
                score += 11;
            } else if (['J', 'Q', 'K'].includes(card.rank)) {
                score += 10;
            } else {
                score += parseInt(card.rank);
            }
        }

        // Adjust for aces
        while (score > 21 && aces > 0) {
            score -= 10;
            aces--;
        }

        return score;
    }

    updateBlackjackDisplay() {
        const playerCardsDiv = document.getElementById('player-cards');
        const dealerCardsDiv = document.getElementById('dealer-cards');

        // Player cards
        playerCardsDiv.innerHTML = this.playerCards.map(card =>
            `<div class="playing-card ${['♥', '♦'].includes(card.suit) ? 'red' : 'black'}">
                ${card.rank}<br>${card.suit}
            </div>`
        ).join('');

        // Dealer cards (hide second card if game not over)
        dealerCardsDiv.innerHTML = this.dealerCards.map((card, index) => {
            if (index === 1 && !this.gameOver) {
                return `<div class="playing-card">🂠</div>`;
            }
            return `<div class="playing-card ${['♥', '♦'].includes(card.suit) ? 'red' : 'black'}">
                ${card.rank}<br>${card.suit}
            </div>`;
        }).join('');

        // Scores
        const playerScore = this.calculateScore(this.playerCards);
        document.getElementById('player-score').textContent = `Scor: ${playerScore}`;

        if (this.gameOver) {
            const dealerScore = this.calculateScore(this.dealerCards);
            document.getElementById('dealer-score').textContent = `Scor: ${dealerScore}`;
        } else {
            document.getElementById('dealer-score').textContent = `Scor: ${this.getCardValue(this.dealerCards[0])} + ?`;
        }

        // Check for bust
        if (playerScore > 21) {
            this.endBlackjackGame('bust');
        } else if (playerScore === 21) {
            this.blackjackStand();
        }
    }

    blackjackHit() {
        if (this.gameOver) return;

        this.playerCards.push(this.drawCard());
        this.updateBlackjackDisplay();
    }

    blackjackStand() {
        if (this.gameOver) return;

        this.gameOver = true;

        // Dealer plays
        while (this.calculateScore(this.dealerCards) < 17) {
            this.dealerCards.push(this.drawCard());
        }

        this.updateBlackjackDisplay();

        const playerScore = this.calculateScore(this.playerCards);
        const dealerScore = this.calculateScore(this.dealerCards);

        if (dealerScore > 21) {
            this.endBlackjackGame('dealer-bust');
        } else if (playerScore > dealerScore) {
            this.endBlackjackGame('win');
        } else if (playerScore === dealerScore) {
            this.endBlackjackGame('tie');
        } else {
            this.endBlackjackGame('lose');
        }
    }

    endBlackjackGame(result) {
        this.gameOver = true;

        // Disable game buttons
        document.getElementById('hit-btn').disabled = true;
        document.getElementById('stand-btn').disabled = true;

        const resultDiv = document.getElementById('blackjack-result');
        const playerScore = this.calculateScore(this.playerCards);
        const dealerScore = this.calculateScore(this.dealerCards);

        switch(result) {
            case 'win':
            case 'dealer-bust':
                const winAmount = 30; // 2x bet
                this.credits += winAmount;
                this.updateCreditsDisplay();

                resultDiv.innerHTML = `
                    <div class="game-result win">
                        🎉 AI CÂȘTIGAT! 🎉<br>
                        Tu: ${playerScore} | Dealer: ${dealerScore}<br>
                        Ai câștigat ${winAmount} credite!
                    </div>
                `;

                this.addChatMessage('🃏 Blackjack', `🎉 CÂȘTIG! ${playerScore} vs ${dealerScore} = ${winAmount} credite!`, 'win');
                break;

            case 'bust':
            case 'lose':
                resultDiv.innerHTML = `
                    <div class="game-result lose">
                        😔 AI PIERDUT!<br>
                        Tu: ${playerScore} | Dealer: ${dealerScore}<br>
                        ${result === 'bust' ? 'Ai depășit 21!' : 'Dealer-ul a câștigat!'}
                    </div>
                `;

                this.addChatMessage('🃏 Blackjack', `${playerScore} vs ${dealerScore} - ${result === 'bust' ? 'Bust!' : 'Dealer câștigă!'}`, 'lose');
                break;

            case 'tie':
                // Return bet
                this.credits += 15;
                this.updateCreditsDisplay();

                resultDiv.innerHTML = `
                    <div class="game-result" style="background: #ffa502; color: #fff;">
                        🤝 EGALITATE!<br>
                        Tu: ${playerScore} | Dealer: ${dealerScore}<br>
                        Credite returnate!
                    </div>
                `;

                this.addChatMessage('🃏 Blackjack', `Egalitate ${playerScore}-${dealerScore}! Credite returnate.`, 'tie');
                break;
        }
    }
}

// Inițializează aplicația când pagina se încarcă
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing casino...');

    try {
        window.casino = new TikTokLiveCasino();
        console.log('TikTok Live Casino initialized successfully!');

        // Debug: Verifică dacă butoanele există
        const connectBtn = document.getElementById('connect-btn');
        const testBtn = document.getElementById('test-btn');

        console.log('Connect button:', connectBtn);
        console.log('Test button:', testBtn);

        if (!connectBtn) {
            console.error('Connect button not found!');
        }
        if (!testBtn) {
            console.error('Test button not found!');
        }

    } catch (error) {
        console.error('Error initializing casino:', error);
    }
});
