<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Casino Simple</title>
    <style>
        body {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-btn {
            padding: 15px 30px;
            background: linear-gradient(145deg, #ffd700, #ffed4e);
            border: none;
            border-radius: 15px;
            color: #000;
            font-weight: bold;
            font-size: 1.1em;
            cursor: pointer;
            margin: 10px;
        }
        .test-btn:hover {
            transform: scale(1.05);
        }
        .credits {
            font-size: 2em;
            color: #ffd700;
            margin: 20px 0;
        }
        .game-area {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            min-height: 200px;
        }
    </style>
</head>
<body>
    <h1>🎰 Test Casino Simple</h1>
    
    <div class="credits" id="credits">Credite: 0</div>
    
    <button class="test-btn" onclick="activateTest()">🧪 Activează Test Mode</button>
    <button class="test-btn" onclick="playRoulette()">🎯 Joacă Ruletă</button>
    <button class="test-btn" onclick="playSlots()">🎰 Joacă Slots</button>
    
    <div class="game-area" id="game-area">
        Apasă un buton pentru a testa!
    </div>

    <script>
        let credits = 0;
        
        function updateCredits() {
            document.getElementById('credits').textContent = `Credite: ${credits}`;
        }
        
        function activateTest() {
            console.log('🧪 Test mode activated!');
            credits = 1000;
            updateCredits();
            document.getElementById('game-area').innerHTML = `
                <h3>✅ Test Mode Activat!</h3>
                <p>Ai primit 1000 credite de test!</p>
                <p>Acum poți juca orice joc.</p>
            `;
        }
        
        function playRoulette() {
            if (credits < 10) {
                alert('Nu ai suficiente credite! Activează test mode mai întâi.');
                return;
            }
            
            credits -= 10;
            updateCredits();
            
            const number = Math.floor(Math.random() * 37);
            const playerGuess = Math.floor(Math.random() * 37);
            
            document.getElementById('game-area').innerHTML = `
                <h3>🎯 Ruletă</h3>
                <div style="font-size: 3em; margin: 20px 0;">🎲</div>
                <p>Numărul tău: ${playerGuess}</p>
                <p>Numărul câștigător: ${number}</p>
                ${number === playerGuess ? 
                    `<p style="color: #2ed573; font-size: 1.5em;">🎉 AI CÂȘTIGAT 250 CREDITE!</p>` : 
                    `<p style="color: #ff4757; font-size: 1.5em;">😔 Ai pierdut! Încearcă din nou!</p>`
                }
            `;
            
            if (number === playerGuess) {
                credits += 250;
                updateCredits();
            }
        }
        
        function playSlots() {
            if (credits < 5) {
                alert('Nu ai suficiente credite! Activează test mode mai întâi.');
                return;
            }
            
            credits -= 5;
            updateCredits();
            
            const symbols = ['🍒', '🍋', '🍊', '🔔', '💎', '🎰'];
            const reel1 = symbols[Math.floor(Math.random() * symbols.length)];
            const reel2 = symbols[Math.floor(Math.random() * symbols.length)];
            const reel3 = symbols[Math.floor(Math.random() * symbols.length)];
            
            const isWin = reel1 === reel2 && reel2 === reel3;
            const winAmount = isWin ? 150 : 0;
            
            document.getElementById('game-area').innerHTML = `
                <h3>🎰 Slots</h3>
                <div style="font-size: 4em; margin: 20px 0;">${reel1} ${reel2} ${reel3}</div>
                ${isWin ? 
                    `<p style="color: #2ed573; font-size: 1.5em;">🎉 JACKPOT! AI CÂȘTIGAT ${winAmount} CREDITE!</p>` : 
                    `<p style="color: #ff4757; font-size: 1.5em;">😔 Încearcă din nou!</p>`
                }
            `;
            
            if (isWin) {
                credits += winAmount;
                updateCredits();
            }
        }
        
        console.log('🎮 Test casino loaded successfully!');
    </script>
</body>
</html>
