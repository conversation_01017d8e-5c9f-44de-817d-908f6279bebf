* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
}

.casino-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.casino-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.casino-title {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.casino-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 10px;
    font-weight: 600;
}

.live-indicator {
    background: #ff4757 !important;
    animation: pulse 2s infinite;
}

.live-indicator.online {
    background: #2ed573 !important;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Connection Panel */
.connection-panel {
    background: rgba(0, 0, 0, 0.4);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 20px;
}

.connection-panel h2 {
    margin-bottom: 20px;
    color: #FFD700;
}

.connection-form {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 15px;
}

#username-input {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    min-width: 250px;
}

.connect-btn {
    padding: 12px 25px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s;
}

.connect-btn:hover {
    transform: translateY(-2px);
}

.connection-status {
    color: #feca57;
    font-weight: 500;
}

/* Casino Games */
.casino-games {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
    color: #fff;
}

.credit-system, .game-selection, .leaderboard {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 15px;
}

.credit-system h3, .game-selection h3, .leaderboard h3 {
    margin-bottom: 15px;
    color: #FFD700;
}

.credit-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.credit-rule {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
    font-weight: 500;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

@media (max-width: 1200px) {
    .games-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 900px) {
    .games-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.game-btn {
    background: linear-gradient(45deg, #3742fa, #2f3542);
    border: none;
    padding: 20px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.game-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.game-cost {
    font-size: 0.8rem;
    color: #feca57;
}

/* Game Area */
.game-area {
    grid-column: 1 / -1;
    background: rgba(0, 0, 0, 0.4);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-placeholder {
    font-size: 1.2rem;
    color: #a4b0be;
}

/* Leaderboard */
.leaderboard-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.leaderboard-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
}

.rank {
    font-weight: 700;
    color: #FFD700;
}

/* Live Chat */
.live-chat {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 15px;
    height: 300px;
}

.live-chat h3 {
    margin-bottom: 15px;
    color: #FFD700;
}

.chat-messages {
    height: 250px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 10px;
}

/* Responsive */
@media (max-width: 768px) {
    .casino-games {
        grid-template-columns: 1fr;
    }

    .games-grid {
        grid-template-columns: 1fr;
    }

    .connection-form {
        flex-direction: column;
        align-items: center;
    }

    #username-input {
        min-width: 200px;
    }
}

/* Professional Casino Game Interfaces */
.game-interface {
    background: linear-gradient(145deg, #1e1e2e, #2d2d44);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 2px solid #ffd700;
    position: relative;
    overflow: hidden;
}

.game-interface::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ffd700, #ffed4e, #ffd700);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.game-title {
    text-align: center;
    font-size: 2.5em;
    margin-bottom: 20px;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    font-weight: bold;
}

.game-board {
    background: #0a0a1a;
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #333;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
}

/* Roulette Styles */
.roulette-wheel {
    width: 350px;
    height: 350px;
    border-radius: 50%;
    margin: 20px auto;
    position: relative;
    border: 8px solid #ffd700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
    background: #2d2d44;
    display: flex;
    align-items: center;
    justify-content: center;
}

.roulette-wheel.spinning {
    animation: spin 4s ease-out;
}

.roulette-numbers {
    width: 320px;
    height: 320px;
    border-radius: 50%;
    position: relative;
    background: conic-gradient(
        #ff0000 0deg 9.73deg,
        #000000 9.73deg 19.46deg,
        #ff0000 19.46deg 29.19deg,
        #000000 29.19deg 38.92deg,
        #ff0000 38.92deg 48.65deg,
        #000000 48.65deg 58.38deg,
        #ff0000 58.38deg 68.11deg,
        #000000 68.11deg 77.84deg,
        #ff0000 77.84deg 87.57deg,
        #000000 87.57deg 97.30deg,
        #ff0000 97.30deg 107.03deg,
        #000000 107.03deg 116.76deg,
        #ff0000 116.76deg 126.49deg,
        #000000 126.49deg 136.22deg,
        #ff0000 136.22deg 145.95deg,
        #000000 145.95deg 155.68deg,
        #ff0000 155.68deg 165.41deg,
        #000000 165.41deg 175.14deg,
        #ff0000 175.14deg 184.87deg,
        #000000 184.87deg 194.60deg,
        #ff0000 194.60deg 204.33deg,
        #000000 204.33deg 214.06deg,
        #ff0000 214.06deg 223.79deg,
        #000000 223.79deg 233.52deg,
        #ff0000 233.52deg 243.25deg,
        #000000 243.25deg 252.98deg,
        #ff0000 252.98deg 262.71deg,
        #000000 262.71deg 272.44deg,
        #ff0000 272.44deg 282.17deg,
        #000000 282.17deg 291.90deg,
        #ff0000 291.90deg 301.63deg,
        #000000 301.63deg 311.36deg,
        #ff0000 311.36deg 321.09deg,
        #000000 321.09deg 330.82deg,
        #ff0000 330.82deg 340.55deg,
        #000000 340.55deg 350.28deg,
        #00ff00 350.28deg 360deg
    );
}

.roulette-number {
    position: absolute;
    width: 25px;
    height: 25px;
    color: white;
    font-weight: bold;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.roulette-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: #ffd700;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #000;
    z-index: 10;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(1440deg); }
}

@keyframes ballSpin {
    0% { transform: rotate(0deg) translateX(120px) rotate(0deg); }
    100% { transform: rotate(1800deg) translateX(120px) rotate(-1800deg); }
}

.number-btn.green {
    border-color: #00ff00;
    background: linear-gradient(145deg, #00ff00, #00cc00);
    color: #000;
}

/* Winning Number Display */
.winning-number-display {
    background: linear-gradient(145deg, #1e1e2e, #2d2d44);
    border-radius: 20px;
    padding: 20px;
    margin: 20px 0;
    border: 3px solid #ffd700;
    text-align: center;
    animation: winningPulse 0.5s ease-out;
}

@keyframes winningPulse {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

.winning-number-container h3 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 1.5em;
}

.winning-number {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5em;
    font-weight: bold;
    margin: 15px auto;
    border: 4px solid #ffd700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
    animation: numberGlow 2s infinite alternate;
}

@keyframes numberGlow {
    0% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.5); }
    100% { box-shadow: 0 0 50px rgba(255, 215, 0, 0.8); }
}

.winning-number.red {
    background: #ff4757;
    color: #fff;
}

.winning-number.black {
    background: #2f3542;
    color: #fff;
}

.winning-number.green {
    background: #00ff00;
    color: #000;
}

.roulette-ball {
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.number-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 5px;
    margin: 20px 0;
}

.number-btn {
    padding: 15px;
    background: linear-gradient(145deg, #2d2d44, #1e1e2e);
    border: 2px solid #ffd700;
    border-radius: 10px;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
}

.number-btn:hover {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #000;
    transform: scale(1.05);
}

.number-btn.red {
    border-color: #ff4757;
    background: linear-gradient(145deg, #ff4757, #ff3742);
}

.number-btn.black {
    border-color: #2f3542;
    background: linear-gradient(145deg, #2f3542, #1e1e2e);
}

.number-btn.selected {
    background: linear-gradient(145deg, #ffd700, #ffed4e) !important;
    color: #000 !important;
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
}

/* Slots Styles */
.slots-machine {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 30px 0;
}

.slot-reel {
    width: 100px;
    height: 120px;
    background: linear-gradient(145deg, #1e1e2e, #2d2d44);
    border: 3px solid #ffd700;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3em;
    position: relative;
    overflow: hidden;
}

.slot-reel.spinning {
    animation: slotSpin 2s ease-out;
}

@keyframes slotSpin {
    0% { transform: translateY(0); }
    50% { transform: translateY(-200px); }
    100% { transform: translateY(0); }
}

/* Blackjack Styles */
.blackjack-table {
    background: #0d5f0d;
    border-radius: 20px;
    padding: 30px;
    margin: 20px 0;
    border: 3px solid #ffd700;
    position: relative;
}

.card-area {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    min-height: 120px;
    align-items: center;
}

.playing-card {
    width: 80px;
    height: 120px;
    background: #fff;
    border-radius: 10px;
    border: 2px solid #333;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    font-weight: bold;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: cardDeal 0.5s ease-out;
}

@keyframes cardDeal {
    from { transform: translateY(-100px) rotate(180deg); opacity: 0; }
    to { transform: translateY(0) rotate(0deg); opacity: 1; }
}

.playing-card.red {
    color: #ff4757;
}

.playing-card.black {
    color: #2f3542;
}

/* Dice Styles */
.dice-container {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin: 30px 0;
}

.dice {
    width: 80px;
    height: 80px;
    background: #fff;
    border-radius: 15px;
    border: 3px solid #333;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5em;
    font-weight: bold;
    color: #333;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    position: relative;
}

.dice.rolling {
    animation: diceRoll 2s ease-out;
}

@keyframes diceRoll {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(90deg); }
    50% { transform: rotate(180deg); }
    75% { transform: rotate(270deg); }
}

/* Game Controls */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.control-btn {
    padding: 15px 30px;
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    border: none;
    border-radius: 15px;
    color: #000;
    font-weight: bold;
    font-size: 1.1em;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 6px 12px rgba(255, 215, 0, 0.3);
}

.control-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(255, 215, 0, 0.5);
}

.control-btn:active {
    transform: translateY(0);
}

.control-btn.disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.5;
}

/* Game Results */
.game-result {
    text-align: center;
    padding: 20px;
    margin: 20px 0;
    border-radius: 15px;
    font-size: 1.3em;
    font-weight: bold;
    animation: resultPulse 0.5s ease-out;
}

@keyframes resultPulse {
    0% { transform: scale(0.8); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
}

.game-result.win {
    background: linear-gradient(145deg, #2ed573, #1dd1a1);
    color: #fff;
    border: 2px solid #ffd700;
}

.game-result.lose {
    background: linear-gradient(145deg, #ff4757, #ff3742);
    color: #fff;
    border: 2px solid #333;
}

/* Betting Interface */
.betting-interface {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #ffd700;
}

.bet-amount {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 15px 0;
}

.bet-input {
    padding: 10px 15px;
    border: 2px solid #ffd700;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1.1em;
    text-align: center;
    width: 120px;
}

.bet-buttons {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 15px 0;
}

.bet-btn {
    padding: 8px 15px;
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid #ffd700;
    border-radius: 8px;
    color: #ffd700;
    cursor: pointer;
    transition: all 0.3s;
}

.bet-btn:hover {
    background: rgba(255, 215, 0, 0.4);
}

/* Poker Styles */
.poker-table {
    background: #0d5f0d;
    border-radius: 20px;
    padding: 30px;
    margin: 20px 0;
    border: 3px solid #ffd700;
}

.poker-card {
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
}

.poker-card:hover {
    transform: translateY(-10px);
}

.poker-card.selected {
    transform: translateY(-20px);
    box-shadow: 0 0 20px #ffd700;
}

.hold-indicator {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: #ffd700;
    color: #000;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: bold;
}

/* Bingo Styles */
.bingo-card {
    background: #0d5f0d;
    border-radius: 20px;
    padding: 20px;
    margin: 20px 0;
    border: 3px solid #ffd700;
}

.bingo-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 5px;
    max-width: 300px;
    margin: 0 auto;
}

.bingo-cell {
    width: 50px;
    height: 50px;
    background: #2d2d44;
    border: 2px solid #ffd700;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s;
}

.bingo-cell:hover {
    background: #ffd700;
    color: #000;
}

.bingo-cell.marked {
    background: #2ed573;
    color: #fff;
    transform: scale(0.9);
}

.bingo-cell.free-space {
    background: #ff6b6b;
    color: #fff;
}

.bingo-numbers {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 15px;
    padding: 15px;
    margin: 15px 0;
}

/* Wheel of Fortune Enhanced Styles */
.wheel-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 30px 0;
    position: relative;
}

.fortune-wheel {
    width: 400px;
    height: 400px;
    position: relative;
    border-radius: 50%;
    border: 8px solid #ffd700;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
    background: #1a1a2e;
}

.wheel-svg {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.wheel-segment-path {
    cursor: pointer;
    transition: all 0.3s ease;
}

.wheel-segment-path:hover {
    stroke-width: 3;
    filter: brightness(1.1);
}

.wheel-text {
    pointer-events: none;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.wheel-pointer {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 2.5em;
    color: #ffd700;
    z-index: 20;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    animation: pointerPulse 2s infinite;
}

@keyframes pointerPulse {
    0%, 100% { transform: translateX(-50%) scale(1); }
    50% { transform: translateX(-50%) scale(1.1); }
}

.wheel-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 4px solid #fff;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    z-index: 15;
}

.center-logo {
    font-size: 1.5em;
    margin-bottom: -5px;
}

.center-text {
    font-size: 0.8em;
    font-weight: bold;
    color: #000;
}

/* Wheel Legend Enhanced */
.wheel-legend {
    background: linear-gradient(145deg, #1e1e2e, #2d2d44);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    border: 2px solid #ffd700;
}

.legend-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 15px;
}

.legend-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    padding: 12px;
    border-radius: 10px;
    border-left: 4px solid transparent;
}

.legend-common {
    color: #3742fa;
    font-weight: bold;
    border-left-color: #3742fa;
}

.legend-rare {
    color: #ffa502;
    font-weight: bold;
    border-left-color: #ffa502;
}

.legend-epic {
    color: #ff9ff3;
    font-weight: bold;
    border-left-color: #ff9ff3;
}

.legend-legendary {
    color: #ffd700;
    font-weight: bold;
    border-left-color: #ffd700;
    animation: legendaryGlow 2s infinite alternate;
}

@keyframes legendaryGlow {
    0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.3); }
    100% { box-shadow: 0 0 15px rgba(255, 215, 0, 0.6); }
}

.wheel-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 215, 0, 0.3);
}

.stat {
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 8px;
    font-size: 0.9em;
}

.stat span {
    color: #ffd700;
    font-weight: bold;
    display: block;
    font-size: 1.1em;
    margin-top: 5px;
}

.wheel-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2em;
    z-index: 10;
    background: #ffd700;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #fff;
}

/* Crash Game Enhanced Styles */
.crash-display {
    background: linear-gradient(135deg, #0f0f23, #1a1a2e);
    border-radius: 20px;
    padding: 30px;
    margin: 20px 0;
    border: 3px solid #ffd700;
    position: relative;
    min-height: 400px;
}

.crash-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 10px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.stat-item span:first-child {
    color: #ccc;
    font-size: 0.9em;
}

.stat-item span:last-child {
    color: #ffd700;
    font-weight: bold;
    font-size: 1.2em;
}

.crash-graph {
    position: relative;
    height: 200px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 15px;
    margin: 20px 0;
    overflow: hidden;
}

#crash-multiplier {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 3em;
    color: #ffd700;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    z-index: 10;
}

.rocket {
    position: absolute;
    bottom: 50px;
    left: 20px;
    font-size: 2.5em;
    z-index: 5;
    transition: all 0.1s linear;
}

.crash-line {
    position: absolute;
    bottom: 50px;
    left: 20px;
    height: 2px;
    background: #00ff00;
    transition: all 0.1s linear;
    z-index: 3;
}

.crash-history {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

#crash-history-list {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.history-item {
    padding: 5px 10px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9em;
}

.history-item.good {
    background: #2ed573;
    color: #fff;
}

.history-item.ok {
    background: #ffa502;
    color: #fff;
}

.history-item.bad {
    background: #ff4757;
    color: #fff;
}

.crash-info {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
}

.crash-info h4 {
    color: #ffd700;
    margin-bottom: 10px;
}

.crash-info p {
    margin: 5px 0;
    font-size: 0.9em;
}

.risk-meter {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
}

.risk-bar {
    width: 100%;
    height: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.risk-level {
    height: 100%;
    width: 0%;
    background: #00ff00;
    transition: all 0.3s;
    border-radius: 10px;
}

.risk-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8em;
    color: #ccc;
}

.cash-out-btn {
    animation: none;
}

.cash-out-btn.pulsing {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Points System Styles */
.points-system {
    background: linear-gradient(145deg, #1e1e2e, #2d2d44);
    border-radius: 20px;
    padding: 20px;
    margin: 15px 0;
    border: 3px solid #ffd700;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.points-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.points-display, .credits-display, .status-display {
    background: rgba(0, 0, 0, 0.3);
    padding: 12px;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversion-info {
    text-align: center;
    margin-top: 10px;
    padding: 8px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 8px;
}

/* Win Effects */
.firework, .confetti, .sparkle, .coin {
    position: fixed;
    font-size: 2em;
    pointer-events: none;
    z-index: 9999;
    animation: fallDown 2s ease-out forwards;
}

@keyframes fallDown {
    0% { transform: translateY(-100px) rotate(0deg); opacity: 1; }
    100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
}

.win-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, #1e1e2e, #2d2d44);
    border: 3px solid #ffd700;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    z-index: 10000;
    animation: winPulse 0.5s ease-out;
    box-shadow: 0 0 50px rgba(255, 215, 0, 0.8);
}

.win-message.jackpot {
    border-color: #ff6b6b;
    box-shadow: 0 0 50px rgba(255, 107, 107, 0.8);
    animation: jackpotPulse 1s ease-out infinite;
}

@keyframes winPulse {
    0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
    100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

@keyframes jackpotPulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.05); }
}

.win-title {
    font-size: 2.5em;
    color: #ffd700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.win-subtitle {
    font-size: 1.3em;
    color: #fff;
    margin-bottom: 10px;
}

@keyframes soundPulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.7; }
    100% { transform: scale(1); opacity: 0; }
}

/* Quiz Styles */
.quiz-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

.quiz-container {
    background: linear-gradient(145deg, #1e1e2e, #2d2d44);
    border: 3px solid #ffd700;
    border-radius: 20px;
    padding: 30px;
    max-width: 600px;
    width: 90%;
    text-align: center;
    box-shadow: 0 0 50px rgba(255, 215, 0, 0.5);
}

.quiz-header h2 {
    color: #ffd700;
    margin-bottom: 15px;
    font-size: 1.8em;
}

.quiz-question {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 15px;
    margin: 20px 0;
}

.quiz-question h3 {
    color: #fff;
    font-size: 1.3em;
    line-height: 1.4;
}

.quiz-answers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin: 20px 0;
}

.quiz-answer-btn {
    padding: 15px;
    background: linear-gradient(145deg, #3742fa, #2f3542);
    border: none;
    border-radius: 10px;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 1em;
}

.quiz-answer-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(55, 66, 250, 0.4);
}

.quiz-answer-btn:disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.quiz-timer {
    margin-top: 20px;
}

.timer-bar {
    width: 100%;
    height: 10px;
    background: #2ed573;
    border-radius: 5px;
    transition: all 0.3s;
    margin-bottom: 10px;
}

.timer-text {
    color: #fff;
    font-weight: bold;
}

.quiz-result {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, #1e1e2e, #2d2d44);
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    z-index: 10001;
    animation: resultPop 0.5s ease-out;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.8);
}

.quiz-result.correct {
    border: 3px solid #2ed573;
    box-shadow: 0 0 50px rgba(46, 213, 115, 0.5);
}

.quiz-result.incorrect {
    border: 3px solid #ff4757;
    box-shadow: 0 0 50px rgba(255, 71, 87, 0.5);
}

@keyframes resultPop {
    0% { transform: translate(-50%, -50%) scale(0.5); opacity: 0; }
    100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
}

.result-icon {
    font-size: 4em;
    margin-bottom: 15px;
}

.result-title {
    font-size: 1.8em;
    font-weight: bold;
    margin-bottom: 10px;
    color: #fff;
}

.result-subtitle {
    font-size: 1.2em;
    color: #ccc;
}
