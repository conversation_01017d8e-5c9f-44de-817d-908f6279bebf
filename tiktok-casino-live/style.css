* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
}

.casino-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.casino-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.casino-title {
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.casino-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 10px;
    font-weight: 600;
}

.live-indicator {
    background: #ff4757 !important;
    animation: pulse 2s infinite;
}

.live-indicator.online {
    background: #2ed573 !important;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Connection Panel */
.connection-panel {
    background: rgba(0, 0, 0, 0.4);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 20px;
}

.connection-panel h2 {
    margin-bottom: 20px;
    color: #FFD700;
}

.connection-form {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 15px;
}

#username-input {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    min-width: 250px;
}

.connect-btn {
    padding: 12px 25px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s;
}

.connect-btn:hover {
    transform: translateY(-2px);
}

.connection-status {
    color: #feca57;
    font-weight: 500;
}

/* Casino Games */
.casino-games {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.credit-system, .game-selection, .leaderboard {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 15px;
}

.credit-system h3, .game-selection h3, .leaderboard h3 {
    margin-bottom: 15px;
    color: #FFD700;
}

.credit-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.credit-rule {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
    font-weight: 500;
}

.games-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.game-btn {
    background: linear-gradient(45deg, #3742fa, #2f3542);
    border: none;
    padding: 20px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.game-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.game-cost {
    font-size: 0.8rem;
    color: #feca57;
}

/* Game Area */
.game-area {
    grid-column: 1 / -1;
    background: rgba(0, 0, 0, 0.4);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-placeholder {
    font-size: 1.2rem;
    color: #a4b0be;
}

/* Leaderboard */
.leaderboard-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.leaderboard-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 8px;
}

.rank {
    font-weight: 700;
    color: #FFD700;
}

/* Live Chat */
.live-chat {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 15px;
    height: 300px;
}

.live-chat h3 {
    margin-bottom: 15px;
    color: #FFD700;
}

.chat-messages {
    height: 250px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 10px;
}

/* Responsive */
@media (max-width: 768px) {
    .casino-games {
        grid-template-columns: 1fr;
    }

    .games-grid {
        grid-template-columns: 1fr;
    }

    .connection-form {
        flex-direction: column;
        align-items: center;
    }

    #username-input {
        min-width: 200px;
    }
}
