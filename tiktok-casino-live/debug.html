<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Casino</title>
    <style>
        body {
            background: #1a1a2e;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .debug-btn {
            padding: 15px 30px;
            background: #ffd700;
            border: none;
            border-radius: 10px;
            color: #000;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            font-size: 1.1em;
        }
        .debug-btn:hover {
            background: #ffed4e;
        }
        .status {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Debug Casino</h1>
    
    <div class="status" id="status">
        Status: Inițializare...
    </div>
    
    <button class="debug-btn" onclick="testElements()">🔍 Test Elemente</button>
    <button class="debug-btn" onclick="testCredits()">💰 Test Credite</button>
    <button class="debug-btn" onclick="testChat()">💬 Test Chat</button>
    <button class="debug-btn" onclick="testGame()">🎮 Test Joc</button>
    
    <div class="status" id="results">
        Rezultate vor apărea aici...
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
            console.log(message);
        }
        
        function updateResults(message) {
            document.getElementById('results').innerHTML += message + '<br>';
            console.log(message);
        }
        
        function testElements() {
            updateResults('🔍 Testez elementele...');
            
            const elements = [
                'connection-panel',
                'casino-games', 
                'live-chat',
                'username-input',
                'connect-btn',
                'test-btn',
                'connection-status',
                'live-indicator',
                'viewer-count',
                'credits-display',
                'game-area',
                'chat-messages',
                'leaderboard-list'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    updateResults(`✅ ${id}: GĂSIT`);
                } else {
                    updateResults(`❌ ${id}: NU EXISTĂ`);
                }
            });
        }
        
        function testCredits() {
            updateResults('💰 Testez sistemul de credite...');
            
            const creditsDisplay = document.getElementById('credits-display');
            if (creditsDisplay) {
                creditsDisplay.textContent = '1000 credite';
                updateResults('✅ Credite actualizate la 1000');
            } else {
                updateResults('❌ Element credits-display nu există');
            }
        }
        
        function testChat() {
            updateResults('💬 Testez chat-ul...');
            
            const chatMessages = document.getElementById('chat-messages');
            if (chatMessages) {
                const messageElement = document.createElement('div');
                messageElement.className = 'chat-message';
                messageElement.innerHTML = '<strong>🤖 Test:</strong> Mesaj de test!';
                chatMessages.appendChild(messageElement);
                updateResults('✅ Mesaj adăugat în chat');
            } else {
                updateResults('❌ Element chat-messages nu există');
            }
        }
        
        function testGame() {
            updateResults('🎮 Testez zona de joc...');
            
            const gameArea = document.getElementById('game-area');
            if (gameArea) {
                gameArea.innerHTML = `
                    <div style="background: #2d2d44; padding: 20px; border-radius: 15px; border: 2px solid #ffd700;">
                        <h3 style="color: #ffd700;">🎯 TEST RULETĂ</h3>
                        <div style="width: 100px; height: 100px; background: #ff0000; border-radius: 50%; margin: 20px auto;"></div>
                        <button style="padding: 10px 20px; background: #ffd700; border: none; border-radius: 10px; color: #000; font-weight: bold;">SPIN TEST</button>
                    </div>
                `;
                updateResults('✅ Interfață de joc creată');
            } else {
                updateResults('❌ Element game-area nu există');
            }
        }
        
        // Test inițial
        updateStatus('🔧 Debug Casino încărcat. Apasă butoanele pentru teste.');
        
        // Verifică dacă există window.casino
        if (window.casino) {
            updateResults('✅ window.casino există');
        } else {
            updateResults('❌ window.casino NU există');
        }
    </script>
</body>
</html>
