#!/bin/bash

echo "🔒 Generez certificate SSL pentru TikTok Live Casino..."

# Creează directorul ssl dacă nu există
mkdir -p ssl

# Generează certificate SSL self-signed
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes \
    -subj "/C=RO/ST=Romania/L=Bucharest/O=TikTok Casino/OU=Development/CN=localhost" \
    -addext "subjectAltName=DNS:localhost,DNS:*.localhost,IP:127.0.0.1,IP:::1"

echo "✅ Certificate SSL generate cu succes!"
echo "📁 Locație: ./ssl/"
echo "🔑 Cheie privată: ./ssl/key.pem"
echo "📜 Certificat: ./ssl/cert.pem"
echo ""
echo "⚠️  IMPORTANT: Acestea sunt certificate self-signed pentru dezvoltare."
echo "🌐 Browser-ul va afișa un avertisment de securitate - este normal."
echo "🔒 Pentru producție, folosește certificate SSL valide de la o autoritate de certificare."
echo ""
echo "🚀 Acum poți porni serverul cu HTTPS:"
echo "   node server.js"
