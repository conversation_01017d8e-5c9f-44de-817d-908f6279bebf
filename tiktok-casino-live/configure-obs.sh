#!/bin/bash

echo "🎬 Configurez OBS Studio pentru TikTok Live Casino..."

# Creează directorul de configurare OBS dacă nu există
mkdir -p ~/.config/obs-studio/basic/scenes
mkdir -p ~/.config/obs-studio/basic/profiles

# Creează profilul pentru TikTok Live Casino
cat > ~/.config/obs-studio/basic/profiles/TikTok-Casino/basic.ini << 'EOF'
[General]
Name=TikTok Casino Live

[Video]
BaseCX=1920
BaseCY=1080
OutputCX=1080
OutputCY=1920
FPSType=0
FPSCommon=30

[Output]
Mode=Simple
FilePath=/home/<USER>/Videos
RecFormat=mp4
RecQuality=0
RecEncoder=x264
StreamEncoder=x264
RecRB=false
RecRBTime=20
RecRBSize=512
StreamDelayEnable=false
StreamDelaySec=20
StreamDelayPreserve=true
StreamDelayObsShutdown=true

[Audio]
SampleRate=48000
ChannelSetup=Stereo

[Stream]
Service=Custom Streaming Server
Server=rtmp://live.tiktok.com/live/
key=YOUR_STREAM_KEY_HERE
EOF

# Creează scena pentru TikTok Casino
cat > ~/.config/obs-studio/basic/scenes/TikTok-Casino.json << 'EOF'
{
    "current_scene": "TikTok Casino Live",
    "current_program_scene": "TikTok Casino Live",
    "scene_order": [
        {
            "name": "TikTok Casino Live"
        }
    ],
    "name": "TikTok Casino",
    "sources": [
        {
            "balance": 0.5,
            "deinterlace_field_order": 0,
            "deinterlace_mode": 0,
            "enabled": true,
            "flags": 0,
            "hotkeys": {},
            "id": "browser_source",
            "mixers": 255,
            "monitoring_type": 0,
            "muted": false,
            "name": "Casino App",
            "prev_ver": 469762048,
            "private_settings": {},
            "push-to-mute": false,
            "push-to-mute-delay": 0,
            "push-to-talk": false,
            "push-to-talk-delay": 0,
            "settings": {
                "css": "body { margin: 0px auto; overflow: hidden; }",
                "height": 1920,
                "url": "https://localhost:3443",
                "width": 1080,
                "fps": 30,
                "shutdown": true,
                "restart_when_active": true,
                "refresh": false
            },
            "sync": 0,
            "versioned_id": "browser_source",
            "volume": 1.0
        }
    ],
    "scenes": [
        {
            "id": 0,
            "name": "TikTok Casino Live",
            "sources": [
                {
                    "name": "Casino App",
                    "pos": {
                        "x": 0.0,
                        "y": 0.0
                    },
                    "rot": 0.0,
                    "scale": {
                        "x": 1.0,
                        "y": 1.0
                    },
                    "alignment": 5,
                    "bounds_alignment": 0,
                    "bounds_type": 0,
                    "bounds": {
                        "x": 0.0,
                        "y": 0.0
                    },
                    "crop_left": 0,
                    "crop_top": 0,
                    "crop_right": 0,
                    "crop_bottom": 0,
                    "id": 1,
                    "locked": false,
                    "visible": true
                }
            ]
        }
    ],
    "transitions": [
        {
            "id": "fade_transition",
            "name": "Fade"
        }
    ],
    "current_transition": "Fade",
    "transition_duration": 300,
    "version": "31.1.0"
}
EOF

echo "✅ Configurare OBS completă!"
echo ""
echo "📋 Pașii următori:"
echo "1. Pornește OBS Studio: env -i PATH=/usr/bin:/bin:/usr/local/bin DISPLAY=\$DISPLAY HOME=\$HOME USER=\$USER obs"
echo "2. Încarcă profilul 'TikTok Casino Live'"
echo "3. Încarcă scena 'TikTok Casino'"
echo "4. Obține stream key de la TikTok"
echo "5. Configurează stream key în Settings > Stream"
echo "6. Începe streaming!"
