// TikTok Live Casino - <PERSON><PERSON><PERSON><PERSON>rowser (fără ES6 imports)

class TikTokLiveCasino {
    constructor() {
        console.log('🏗️ Constructing TikTokLiveCasino...');
        
        this.connection = null;
        this.isConnected = false;
        this.players = new Map();
        this.credits = 0; // Credite pentru jocuri
        this.points = 0; // Puncte principale (10% din câștiguri)
        this.currentGame = null;
        this.leaderboard = [];

        // Quiz questions pentru Wheel of Fortune
        this.quizQuestions = [
            { question: "Care este capitala României?", answers: ["<PERSON>ucurești", "<PERSON><PERSON>j", "<PERSON><PERSON><PERSON><PERSON>", "Timișoara"], correct: 0 },
            { question: "Câte continente sunt pe Pământ?", answers: ["5", "6", "7", "8"], correct: 2 },
            { question: "Care este cel mai mare ocean?", answers: ["Atlantic", "Indian", "Arctic", "Pacific"], correct: 3 },
            { question: "În ce an a avut loc Revoluția din România?", answers: ["1987", "1989", "1990", "1991"], correct: 1 },
            { question: "Care este simbolul chimic al aurului?", answers: ["Go", "Au", "Ag", "Al"], correct: 1 },
            { question: "Câte zile are un an bisect?", answers: ["365", "366", "367", "364"], correct: 1 },
            { question: "Care este cel mai înalt munte din lume?", answers: ["K2", "Everest", "Kilimanjaro", "Mont Blanc"], correct: 1 },
            { question: "În ce țară se află Turnul Eiffel?", answers: ["Italia", "Spania", "Franța", "Germania"], correct: 2 },
            { question: "Care este cel mai mic planet din sistemul solar?", answers: ["Venus", "Marte", "Mercur", "Pluto"], correct: 2 },
            { question: "Câte culori are curcubeul?", answers: ["5", "6", "7", "8"], correct: 2 },
            { question: "Care este moneda oficială a Japoniei?", answers: ["Yuan", "Won", "Yen", "Ringgit"], correct: 2 },
            { question: "În ce an s-a terminat Al Doilea Război Mondial?", answers: ["1944", "1945", "1946", "1947"], correct: 1 },
            { question: "Care este cel mai lung râu din lume?", answers: ["Amazon", "Nil", "Mississippi", "Yangtze"], correct: 1 },
            { question: "Câte oase are corpul uman adult?", answers: ["196", "206", "216", "226"], correct: 1 },
            { question: "Care este cel mai mare mamifer din lume?", answers: ["Elefant", "Balena albastră", "Girafa", "Rinocer"], correct: 1 },
            { question: "În ce oraș se află Colosseum-ul?", answers: ["Atena", "Roma", "Paris", "Madrid"], correct: 1 },
            { question: "Care este formula chimică a apei?", answers: ["H2O", "CO2", "O2", "H2SO4"], correct: 0 },
            { question: "Câte minute are o oră?", answers: ["50", "60", "70", "80"], correct: 1 },
            { question: "Care este cel mai rapid animal terestru?", answers: ["Leopard", "Leu", "Ghepard", "Tigru"], correct: 2 },
            { question: "În ce an a fost descoperită America?", answers: ["1490", "1491", "1492", "1493"], correct: 2 }
        ];
        
        try {
            this.initializeUI();
            this.setupEventListeners();
            console.log('✅ Casino constructor completed successfully!');
        } catch (error) {
            console.error('❌ Error in constructor:', error);
        }
    }

    initializeUI() {
        this.elements = {
            connectionPanel: document.getElementById('connection-panel'),
            casinoGames: document.getElementById('casino-games'),
            liveChat: document.getElementById('live-chat'),
            usernameInput: document.getElementById('username-input'),
            connectBtn: document.getElementById('connect-btn'),
            connectionStatus: document.getElementById('connection-status'),
            liveIndicator: document.getElementById('live-indicator'),
            viewerCount: document.getElementById('viewer-count'),
            creditsDisplay: document.getElementById('credits-display'),
            gameArea: document.getElementById('game-area'),
            chatMessages: document.getElementById('chat-messages'),
            leaderboardList: document.getElementById('leaderboard-list')
        };
        
        console.log('🎯 Elements initialized:', this.elements);
    }

    setupEventListeners() {
        console.log('🔧 Setting up event listeners...');
        
        // Connect button
        if (this.elements.connectBtn) {
            this.elements.connectBtn.addEventListener('click', () => {
                console.log('🔗 Connect button clicked');
                this.connectToTikTok();
            });
            console.log('✅ Connect button listener added');
        } else {
            console.error('❌ Connect button not found!');
        }
        
        // Test button pentru a testa casino-ul fără TikTok
        const testBtn = document.getElementById('test-btn');
        if (testBtn) {
            testBtn.addEventListener('click', () => {
                console.log('🧪 Test button clicked');
                this.enableTestMode();
            });
            console.log('✅ Test button listener added');
        } else {
            console.error('❌ Test button not found!');
        }
        
        // Game buttons
        document.querySelectorAll('.game-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const game = e.target.closest('.game-btn').dataset.game;
                console.log('🎮 Game button clicked:', game);
                this.startGame(game);
            });
        });
        console.log('✅ Game buttons listeners added');

        // Enter key pentru conectare
        if (this.elements.usernameInput) {
            this.elements.usernameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    console.log('⌨️ Enter key pressed');
                    this.connectToTikTok();
                }
            });
            console.log('✅ Username input listener added');
        } else {
            console.error('❌ Username input not found!');
        }
        
        console.log('🎉 All event listeners set up!');
    }

    enableTestMode() {
        console.log('🧪 Enabling test mode...');
        
        try {
            // Verifică elementele
            console.log('Elements check:', this.elements);
            
            // Simulează conexiunea reușită
            this.updateConnectionStatus('✅ Mod Test Activat!', 'success');
            
            if (this.elements.liveIndicator) {
                this.elements.liveIndicator.textContent = 'TEST';
                this.elements.liveIndicator.classList.add('online');
            }
            
            // Ascunde panoul de conectare și arată casino-ul
            if (this.elements.connectionPanel) {
                this.elements.connectionPanel.style.display = 'none';
                console.log('✅ Connection panel hidden');
            }
            
            if (this.elements.casinoGames) {
                this.elements.casinoGames.style.display = 'grid';
                console.log('✅ Casino games shown');
            }
            
            if (this.elements.liveChat) {
                this.elements.liveChat.style.display = 'block';
                console.log('✅ Live chat shown');
            }
            
            // Setează credite și puncte de test
            this.credits = 1000;
            this.points = 100; // Puncte inițiale pentru test
            this.updateCreditsDisplay();
            this.updatePointsDisplay();
            console.log('✅ Credits set to 1000, Points set to 100');
            
            // Adaugă mesaj în chat
            this.addChatMessage('🤖 Sistem', 'Mod test activat! Ai 1000 credite pentru a testa jocurile.', 'system');
            
            // Simulează câțiva jucători în leaderboard
            this.leaderboard = [
                { username: 'TestPlayer1', credits: 2500 },
                { username: 'TestPlayer2', credits: 1800 },
                { username: 'TestPlayer3', credits: 1200 }
            ];
            this.updateLeaderboard();
            
            console.log('🎉 Test mode activated successfully!');
            
        } catch (error) {
            console.error('❌ Error in enableTestMode:', error);
        }
    }

    connectToTikTok() {
        const username = this.elements.usernameInput.value.trim();
        
        if (!username) {
            this.updateConnectionStatus('Te rog introdu username-ul TikTok!', 'error');
            return;
        }

        this.elements.connectBtn.disabled = true;
        this.elements.connectBtn.textContent = 'Se conectează...';
        this.updateConnectionStatus('Se conectează la TikTok Live...', 'connecting');

        // Simulează conexiunea (pentru că nu avem TikTok Live Connector în browser)
        setTimeout(() => {
            this.updateConnectionStatus('✅ Conectat la TikTok Live! (Simulat)', 'success');
            this.elements.liveIndicator.textContent = 'LIVE';
            this.elements.liveIndicator.classList.add('online');
            
            // Ascunde panoul de conectare și arată casino-ul
            this.elements.connectionPanel.style.display = 'none';
            this.elements.casinoGames.style.display = 'grid';
            this.elements.liveChat.style.display = 'block';
            
            // Setează credite inițiale
            this.credits = 500;
            this.updateCreditsDisplay();
            
            this.addChatMessage('🎰 Sistem', `Conectat la @${username}! Casino live este activ!`, 'system');
            
            this.elements.connectBtn.disabled = false;
            this.elements.connectBtn.textContent = 'Conectează Live Casino';
        }, 2000);
    }

    updateCreditsDisplay() {
        if (this.elements.creditsDisplay) {
            this.elements.creditsDisplay.textContent = `${this.credits} credite`;
            console.log(`💰 Credits updated: ${this.credits}`);
        } else {
            console.error('❌ Credits display element not found!');
        }
    }

    updatePointsDisplay() {
        const pointsDisplay = document.getElementById('points-display');
        if (pointsDisplay) {
            pointsDisplay.textContent = `${this.points} puncte`;
            console.log(`🏆 Points updated: ${this.points}`);
        }
    }

    addWinnings(amount, gameType) {
        // Adaugă creditele câștigate
        this.credits += amount;

        // Calculează punctele (10% din câștig)
        const pointsEarned = Math.floor(amount * 0.1);
        this.points += pointsEarned;

        // Actualizează afișajele
        this.updateCreditsDisplay();
        this.updatePointsDisplay();

        // Efecte vizuale și sonore
        this.playWinEffects(amount, pointsEarned, gameType);

        console.log(`🎉 Win: ${amount} credite + ${pointsEarned} puncte`);
    }

    playWinEffects(credits, points, gameType) {
        // Efecte vizuale pe baza mărimii câștigului
        if (credits >= 1000) {
            this.createFireworks();
            this.showWinMessage(`🎉 JACKPOT URIAȘ! 🎉`, `+${credits} credite | +${points} puncte`, 'jackpot');
            this.playSound('jackpot');
        } else if (credits >= 500) {
            this.createConfetti();
            this.showWinMessage(`🎊 CÂȘTIG MARE! 🎊`, `+${credits} credite | +${points} puncte`, 'big-win');
            this.playSound('big-win');
        } else if (credits >= 200) {
            this.createSparkles();
            this.showWinMessage(`✨ CÂȘTIG BUN! ✨`, `+${credits} credite | +${points} puncte`, 'good-win');
            this.playSound('win');
        } else if (credits > 0) {
            this.createCoins();
            this.showWinMessage(`💰 Câștig! 💰`, `+${credits} credite | +${points} puncte`, 'small-win');
            this.playSound('coins');
        }
    }

    createFireworks() {
        for (let i = 0; i < 10; i++) {
            setTimeout(() => {
                const firework = document.createElement('div');
                firework.className = 'firework';
                firework.style.left = Math.random() * 100 + '%';
                firework.style.top = Math.random() * 50 + '%';
                firework.innerHTML = '🎆';
                document.body.appendChild(firework);

                setTimeout(() => firework.remove(), 2000);
            }, i * 200);
        }
    }

    createConfetti() {
        for (let i = 0; i < 20; i++) {
            setTimeout(() => {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = Math.random() * 100 + '%';
                confetti.innerHTML = ['🎊', '🎉', '✨', '💫', '⭐'][Math.floor(Math.random() * 5)];
                document.body.appendChild(confetti);

                setTimeout(() => confetti.remove(), 3000);
            }, i * 100);
        }
    }

    createSparkles() {
        for (let i = 0; i < 15; i++) {
            setTimeout(() => {
                const sparkle = document.createElement('div');
                sparkle.className = 'sparkle';
                sparkle.style.left = Math.random() * 100 + '%';
                sparkle.style.top = Math.random() * 100 + '%';
                sparkle.innerHTML = '✨';
                document.body.appendChild(sparkle);

                setTimeout(() => sparkle.remove(), 2000);
            }, i * 150);
        }
    }

    createCoins() {
        for (let i = 0; i < 8; i++) {
            setTimeout(() => {
                const coin = document.createElement('div');
                coin.className = 'coin';
                coin.style.left = Math.random() * 100 + '%';
                coin.style.top = '20%';
                coin.innerHTML = '💰';
                document.body.appendChild(coin);

                setTimeout(() => coin.remove(), 1500);
            }, i * 100);
        }
    }

    showWinMessage(title, subtitle, type) {
        const winMessage = document.createElement('div');
        winMessage.className = `win-message ${type}`;
        winMessage.innerHTML = `
            <div class="win-title">${title}</div>
            <div class="win-subtitle">${subtitle}</div>
        `;
        document.body.appendChild(winMessage);

        setTimeout(() => winMessage.remove(), 4000);
    }

    playSound(type) {
        // Simulare efecte sonore cu emoji și animații
        const soundEmoji = {
            'jackpot': '🔊🎺🎉',
            'big-win': '🔊🎊✨',
            'win': '🔊💫',
            'coins': '🔊💰'
        };

        console.log(`🎵 Playing sound: ${soundEmoji[type] || '🔊'}`);

        // Afișează emoji-ul sonor temporar
        const soundIndicator = document.createElement('div');
        soundIndicator.className = 'sound-indicator';
        soundIndicator.textContent = soundEmoji[type] || '🔊';
        soundIndicator.style.position = 'fixed';
        soundIndicator.style.top = '20px';
        soundIndicator.style.right = '20px';
        soundIndicator.style.fontSize = '2em';
        soundIndicator.style.zIndex = '9999';
        soundIndicator.style.animation = 'soundPulse 1s ease-out';
        document.body.appendChild(soundIndicator);

        setTimeout(() => soundIndicator.remove(), 1000);
    }

    updateConnectionStatus(message, type) {
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.textContent = message;
            this.elements.connectionStatus.className = `connection-status ${type}`;
        }
    }

    addChatMessage(username, message, type = 'normal') {
        if (this.elements.chatMessages) {
            const messageElement = document.createElement('div');
            messageElement.className = `chat-message ${type}`;
            messageElement.innerHTML = `<strong>${username}:</strong> ${message}`;
            
            this.elements.chatMessages.appendChild(messageElement);
            this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
            
            console.log(`💬 Chat message added: ${username}: ${message}`);
        } else {
            console.error('❌ Chat messages element not found!');
            console.log(`💬 Would have added: ${username}: ${message}`);
        }
    }

    updateLeaderboard() {
        if (this.elements.leaderboardList) {
            this.elements.leaderboardList.innerHTML = '';
            
            this.leaderboard.forEach((player, index) => {
                const playerElement = document.createElement('div');
                playerElement.className = 'leaderboard-player';
                playerElement.innerHTML = `
                    <span class="rank">#${index + 1}</span>
                    <span class="username">${player.username}</span>
                    <span class="credits">${player.credits}</span>
                `;
                this.elements.leaderboardList.appendChild(playerElement);
            });
        }
    }

    startGame(gameType) {
        console.log(`🎮 Starting game: ${gameType}`);
        
        this.currentGame = gameType;
        
        // Verifică dacă jucătorul are suficiente credite
        const gameCosts = {
            roulette: 10,
            slots: 5,
            blackjack: 15,
            dice: 8,
            poker: 20,
            bingo: 12,
            wheel: 6,
            crash: 7
        };
        
        const cost = gameCosts[gameType];
        if (this.credits < cost) {
            this.addChatMessage('🚫 Sistem', `Nu ai suficiente credite! Ai nevoie de ${cost} credite pentru ${gameType}.`, 'error');
            return;
        }
        
        // Scade creditele
        this.credits -= cost;
        this.updateCreditsDisplay();
        
        // Creează interfața jocului
        switch(gameType) {
            case 'roulette':
                this.createRouletteGame();
                break;
            case 'slots':
                this.createSlotsGame();
                break;
            case 'blackjack':
                this.createBlackjackGame();
                break;
            case 'dice':
                this.createDiceGame();
                break;
            case 'poker':
                this.createPokerGame();
                break;
            case 'bingo':
                this.createBingoGame();
                break;
            case 'wheel':
                this.createWheelGame();
                break;
            case 'crash':
                this.createCrashGame();
                break;
        }
        
        this.addChatMessage('🎮 Sistem', `Jocul ${gameType} a început! Costul: ${cost} credite.`, 'game');
    }

    createRouletteGame() {
        // Numerele ruletei în ordinea corectă
        const rouletteNumbers = [0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26];
        const redNumbers = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36];

        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🎯 RULETĂ CASINO</div>
                <div class="game-board">
                    <div class="roulette-wheel" id="roulette-wheel">
                        <div class="roulette-numbers">
                            ${rouletteNumbers.map((num, index) => {
                                const angle = (index * 360 / 37) - 90; // -90 pentru a începe de sus
                                const radius = 140; // Distanța de la centru
                                const x = Math.cos(angle * Math.PI / 180) * radius;
                                const y = Math.sin(angle * Math.PI / 180) * radius;
                                const color = num === 0 ? '#00ff00' : (redNumbers.includes(num) ? '#ff0000' : '#000000');

                                return `<div class="roulette-number" style="
                                    left: calc(50% + ${x}px - 12.5px);
                                    top: calc(50% + ${y}px - 12.5px);
                                    background: ${color};
                                    border-radius: 50%;
                                ">${num}</div>`;
                            }).join('')}
                        </div>
                        <div class="roulette-center">
                            <div class="roulette-ball" id="roulette-ball">⚪</div>
                        </div>
                    </div>
                    <div class="betting-interface">
                        <h4>Alege numărul tău norocos (0-36):</h4>
                        <div class="number-grid">
                            ${Array.from({length: 37}, (_, i) => {
                                const isRed = redNumbers.includes(i);
                                const colorClass = i === 0 ? 'green' : (isRed ? 'red' : 'black');
                                return `<button class="number-btn ${colorClass}" data-number="${i}">${i}</button>`;
                            }).join('')}
                        </div>
                        <div id="selected-number" style="margin: 15px 0; font-size: 1.2em; color: #ffd700;">
                            Selectează un număr pentru a paria
                        </div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="spin-roulette" disabled>🎲 ÎNVÂRTE RULETA</button>
                    </div>
                    <div id="roulette-result"></div>
                </div>
            </div>
        `;

        this.rouletteNumbers = rouletteNumbers;
        this.setupRouletteControls();
    }

    isRedNumber(number) {
        const redNumbers = [1,3,5,7,9,12,14,16,18,19,21,23,25,27,30,32,34,36];
        return redNumbers.includes(number);
    }

    setupRouletteControls() {
        let selectedNumber = null;

        // Number selection
        document.querySelectorAll('.number-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // Remove previous selection
                document.querySelectorAll('.number-btn').forEach(b => b.classList.remove('selected'));

                // Select new number
                e.target.classList.add('selected');
                selectedNumber = parseInt(e.target.dataset.number);

                // Update selected number display
                document.getElementById('selected-number').innerHTML = `
                    <strong>Numărul selectat: ${selectedNumber}</strong><br>
                    <small>Apasă "ÎNVÂRTE RULETA" pentru a juca!</small>
                `;

                // Enable spin button
                document.getElementById('spin-roulette').disabled = false;
            });
        });

        // Spin button
        document.getElementById('spin-roulette').addEventListener('click', () => {
            if (selectedNumber === null) {
                this.addChatMessage('🚫 Sistem', 'Alege un număr mai întâi!', 'error');
                return;
            }

            this.spinRoulette(selectedNumber);
        });
    }

    spinRoulette(selectedNumber) {
        const spinBtn = document.getElementById('spin-roulette');
        spinBtn.disabled = true;
        spinBtn.textContent = '🎲 SE ÎNVÂRTE...';

        // Generate winning number
        const winningNumber = this.rouletteNumbers[Math.floor(Math.random() * 37)];
        const winningIndex = this.rouletteNumbers.indexOf(winningNumber);

        // Calculate rotation to land on winning number
        const baseRotations = 5; // Numărul de rotații complete
        const segmentAngle = 360 / 37; // Unghiul pentru fiecare număr
        const targetAngle = winningIndex * segmentAngle;
        const totalRotation = (baseRotations * 360) + (360 - targetAngle); // Rotație inversă

        // Animate wheel
        const wheel = document.getElementById('roulette-wheel');
        wheel.style.transition = 'transform 4s cubic-bezier(0.25, 0.1, 0.25, 1)';
        wheel.style.transform = `rotate(${totalRotation}deg)`;

        // Animate ball
        const ball = document.getElementById('roulette-ball');
        ball.style.animation = 'ballSpin 4s cubic-bezier(0.25, 0.1, 0.25, 1)';

        setTimeout(() => {
            const isWin = winningNumber === selectedNumber;
            const resultDiv = document.getElementById('roulette-result');

            // Highlight winning number
            document.querySelectorAll('.roulette-number').forEach(numEl => {
                if (parseInt(numEl.textContent) === winningNumber) {
                    numEl.style.boxShadow = '0 0 20px #ffd700, 0 0 40px #ffd700';
                    numEl.style.transform = 'scale(1.3)';
                    numEl.style.zIndex = '100';
                }
            });

            if (isWin) {
                const winAmount = 250; // 25x bet
                this.addWinnings(winAmount, 'roulette');

                resultDiv.innerHTML = `
                    <div class="game-result win">
                        🎉 JACKPOT! 🎉<br>
                        <div style="font-size: 2em; margin: 10px 0;">🎯 ${winningNumber} 🎯</div>
                        Numărul tău: ${selectedNumber}<br>
                        Ai câștigat ${winAmount} credite!
                    </div>
                `;

                this.addChatMessage('🎯 Ruletă', `🎉 JACKPOT! Numărul ${winningNumber} a câștigat ${winAmount} credite!`, 'win');
            } else {
                resultDiv.innerHTML = `
                    <div class="game-result lose">
                        😔 Aproape!<br>
                        <div style="font-size: 2em; margin: 10px 0;">🎯 ${winningNumber} 🎯</div>
                        Numărul tău: ${selectedNumber}<br>
                        Încearcă din nou!
                    </div>
                `;

                this.addChatMessage('🎯 Ruletă', `Numărul câștigător a fost ${winningNumber}. Încearcă din nou!`, 'lose');
            }

            // Reset for next game
            setTimeout(() => {
                wheel.style.transition = '';
                wheel.style.transform = '';
                ball.style.animation = '';

                // Remove highlight from numbers
                document.querySelectorAll('.roulette-number').forEach(numEl => {
                    numEl.style.boxShadow = '';
                    numEl.style.transform = '';
                    numEl.style.zIndex = '';
                });

                spinBtn.disabled = false;
                spinBtn.textContent = '🎲 ÎNVÂRTE RULETA';
                document.querySelectorAll('.number-btn').forEach(b => b.classList.remove('selected'));
                document.getElementById('selected-number').innerHTML = 'Selectează un număr pentru a paria';
            }, 3000);

        }, 4000);
    }

    createSlotsGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🎰 SLOTS CASINO</div>
                <div class="game-board">
                    <div class="slots-machine">
                        <div class="slot-reel" id="reel1">🍒</div>
                        <div class="slot-reel" id="reel2">🍋</div>
                        <div class="slot-reel" id="reel3">🍊</div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="spin-slots">🎰 SPIN!</button>
                    </div>
                    <div id="slots-result"></div>
                    <div class="betting-interface">
                        <h4>💰 Combinații câștigătoare:</h4>
                        <p>🍒🍒🍒 = 150 credite | 🍋🍋🍋 = 100 credite | 🍊🍊🍊 = 75 credite</p>
                        <p>🔔🔔🔔 = 200 credite | 💎💎💎 = 500 credite | 🎰🎰🎰 = 1000 credite</p>
                    </div>
                </div>
            </div>
        `;

        this.setupSlotsControls();
    }

    setupSlotsControls() {
        document.getElementById('spin-slots').addEventListener('click', () => {
            this.spinSlots();
        });
    }

    spinSlots() {
        const spinBtn = document.getElementById('spin-slots');
        spinBtn.disabled = true;
        spinBtn.textContent = '🎰 SPINNING...';

        const symbols = ['🍒', '🍋', '🍊', '🔔', '💎', '🎰'];
        const reels = [document.getElementById('reel1'), document.getElementById('reel2'), document.getElementById('reel3')];

        // Animate reels
        reels.forEach((reel, index) => {
            reel.classList.add('spinning');

            // Change symbols during spin
            const spinInterval = setInterval(() => {
                reel.textContent = symbols[Math.floor(Math.random() * symbols.length)];
            }, 100);

            // Stop spinning after delay
            setTimeout(() => {
                clearInterval(spinInterval);
                reel.classList.remove('spinning');

                // Final symbol
                const finalSymbol = symbols[Math.floor(Math.random() * symbols.length)];
                reel.textContent = finalSymbol;

                // Check for win after all reels stop
                if (index === 2) {
                    setTimeout(() => this.checkSlotsWin(), 500);
                }
            }, 2000 + (index * 500));
        });
    }

    checkSlotsWin() {
        const reel1 = document.getElementById('reel1').textContent;
        const reel2 = document.getElementById('reel2').textContent;
        const reel3 = document.getElementById('reel3').textContent;

        const resultDiv = document.getElementById('slots-result');
        const spinBtn = document.getElementById('spin-slots');

        let winAmount = 0;

        if (reel1 === reel2 && reel2 === reel3) {
            // Three of a kind
            const payouts = {
                '🍒': 150,
                '🍋': 100,
                '🍊': 75,
                '🔔': 200,
                '💎': 500,
                '🎰': 1000
            };

            winAmount = payouts[reel1] || 50;

            this.credits += winAmount;
            this.updateCreditsDisplay();

            resultDiv.innerHTML = `
                <div class="game-result win">
                    🎉 JACKPOT! 🎉<br>
                    ${reel1}${reel2}${reel3}<br>
                    Ai câștigat ${winAmount} credite!
                </div>
            `;

            this.addChatMessage('🎰 Slots', `🎉 JACKPOT! ${reel1}${reel2}${reel3} = ${winAmount} credite!`, 'win');
        } else {
            resultDiv.innerHTML = `
                <div class="game-result lose">
                    😔 Încearcă din nou!<br>
                    ${reel1}${reel2}${reel3}<br>
                    Nu ai câștigat de această dată.
                </div>
            `;

            this.addChatMessage('🎰 Slots', `${reel1}${reel2}${reel3} - Încearcă din nou!`, 'lose');
        }

        // Reset for next game
        setTimeout(() => {
            spinBtn.disabled = false;
            spinBtn.textContent = '🎰 SPIN!';
        }, 3000);
    }

    createDiceGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🎲 ZARURI CASINO</div>
                <div class="game-board">
                    <div class="dice-container">
                        <div class="dice" id="dice1">🎲</div>
                        <div class="dice" id="dice2">🎲</div>
                    </div>
                    <div class="betting-interface">
                        <h4>Ghicește suma zarurilor (2-12):</h4>
                        <div class="bet-amount">
                            <label>Suma ta:</label>
                            <input type="number" class="bet-input" id="dice-guess" min="2" max="12" placeholder="2-12">
                        </div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="roll-dice">🎲 ARUNCĂ ZARURILE</button>
                    </div>
                    <div id="dice-result"></div>
                </div>
            </div>
        `;

        this.setupDiceControls();
    }

    setupDiceControls() {
        document.getElementById('roll-dice').addEventListener('click', () => {
            const guess = parseInt(document.getElementById('dice-guess').value);

            if (!guess || guess < 2 || guess > 12) {
                this.addChatMessage('🚫 Sistem', 'Introdu o sumă validă între 2 și 12!', 'error');
                return;
            }

            this.rollDice(guess);
        });
    }

    rollDice(guess) {
        const rollBtn = document.getElementById('roll-dice');
        rollBtn.disabled = true;
        rollBtn.textContent = '🎲 SE ARUNCĂ...';

        const dice1 = document.getElementById('dice1');
        const dice2 = document.getElementById('dice2');

        // Animate dice
        dice1.classList.add('rolling');
        dice2.classList.add('rolling');

        setTimeout(() => {
            const roll1 = Math.floor(Math.random() * 6) + 1;
            const roll2 = Math.floor(Math.random() * 6) + 1;
            const total = roll1 + roll2;

            dice1.textContent = roll1;
            dice2.textContent = roll2;
            dice1.classList.remove('rolling');
            dice2.classList.remove('rolling');

            const resultDiv = document.getElementById('dice-result');

            if (total === guess) {
                const winAmount = 176; // 22x bet
                this.credits += winAmount;
                this.updateCreditsDisplay();

                resultDiv.innerHTML = `
                    <div class="game-result win">
                        🎉 PERFECT! 🎉<br>
                        Zarurile: ${roll1} + ${roll2} = ${total}<br>
                        Ghicirea ta: ${guess}<br>
                        Ai câștigat ${winAmount} credite!
                    </div>
                `;

                this.addChatMessage('🎲 Zaruri', `🎉 PERFECT! ${roll1}+${roll2}=${total} = ${winAmount} credite!`, 'win');
            } else {
                resultDiv.innerHTML = `
                    <div class="game-result lose">
                        😔 Aproape!<br>
                        Zarurile: ${roll1} + ${roll2} = ${total}<br>
                        Ghicirea ta: ${guess}<br>
                        Încearcă din nou!
                    </div>
                `;

                this.addChatMessage('🎲 Zaruri', `Zarurile au dat ${total}, tu ai ghicit ${guess}. Încearcă din nou!`, 'lose');
            }

            // Reset for next game
            setTimeout(() => {
                rollBtn.disabled = false;
                rollBtn.textContent = '🎲 ARUNCĂ ZARURILE';
                document.getElementById('dice-guess').value = '';
            }, 3000);

        }, 2000);
    }

    createBlackjackGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🃏 BLACKJACK CASINO</div>
                <div class="game-board">
                    <div class="blackjack-table">
                        <div class="dealer-area">
                            <h4>🎩 Dealer:</h4>
                            <div class="card-area" id="dealer-cards"></div>
                            <div id="dealer-score">Scor: ?</div>
                        </div>
                        <div class="player-area">
                            <h4>👤 Tu:</h4>
                            <div class="card-area" id="player-cards"></div>
                            <div id="player-score">Scor: 0</div>
                        </div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="hit-btn">🃏 HIT</button>
                        <button class="control-btn" id="stand-btn">✋ STAND</button>
                        <button class="control-btn" id="new-blackjack">🆕 JOC NOU</button>
                    </div>
                    <div id="blackjack-result"></div>
                </div>
            </div>
        `;

        this.setupBlackjackControls();
        this.startNewBlackjackGame();
    }

    setupBlackjackControls() {
        document.getElementById('hit-btn').addEventListener('click', () => this.blackjackHit());
        document.getElementById('stand-btn').addEventListener('click', () => this.blackjackStand());
        document.getElementById('new-blackjack').addEventListener('click', () => this.startNewBlackjackGame());
    }

    startNewBlackjackGame() {
        this.deck = this.createDeck();
        this.playerCards = [];
        this.dealerCards = [];
        this.gameOver = false;

        // Deal initial cards
        this.playerCards.push(this.drawCard());
        this.dealerCards.push(this.drawCard());
        this.playerCards.push(this.drawCard());
        this.dealerCards.push(this.drawCard());

        this.updateBlackjackDisplay();

        // Enable game buttons
        document.getElementById('hit-btn').disabled = false;
        document.getElementById('stand-btn').disabled = false;

        // Clear result
        document.getElementById('blackjack-result').innerHTML = '';
    }

    createDeck() {
        const suits = ['♠', '♥', '♦', '♣'];
        const ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        const deck = [];

        for (let suit of suits) {
            for (let rank of ranks) {
                deck.push({ suit, rank });
            }
        }

        // Shuffle deck
        for (let i = deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [deck[i], deck[j]] = [deck[j], deck[i]];
        }

        return deck;
    }

    drawCard() {
        return this.deck.pop();
    }

    calculateScore(cards) {
        let score = 0;
        let aces = 0;

        for (let card of cards) {
            if (card.rank === 'A') {
                aces++;
                score += 11;
            } else if (['J', 'Q', 'K'].includes(card.rank)) {
                score += 10;
            } else {
                score += parseInt(card.rank);
            }
        }

        // Adjust for aces
        while (score > 21 && aces > 0) {
            score -= 10;
            aces--;
        }

        return score;
    }

    updateBlackjackDisplay() {
        const playerCardsDiv = document.getElementById('player-cards');
        const dealerCardsDiv = document.getElementById('dealer-cards');

        // Player cards
        playerCardsDiv.innerHTML = this.playerCards.map(card =>
            `<div class="playing-card ${['♥', '♦'].includes(card.suit) ? 'red' : 'black'}">
                ${card.rank}<br>${card.suit}
            </div>`
        ).join('');

        // Dealer cards (hide second card if game not over)
        dealerCardsDiv.innerHTML = this.dealerCards.map((card, index) => {
            if (index === 1 && !this.gameOver) {
                return `<div class="playing-card">🂠</div>`;
            }
            return `<div class="playing-card ${['♥', '♦'].includes(card.suit) ? 'red' : 'black'}">
                ${card.rank}<br>${card.suit}
            </div>`;
        }).join('');

        // Scores
        const playerScore = this.calculateScore(this.playerCards);
        document.getElementById('player-score').textContent = `Scor: ${playerScore}`;

        if (this.gameOver) {
            const dealerScore = this.calculateScore(this.dealerCards);
            document.getElementById('dealer-score').textContent = `Scor: ${dealerScore}`;
        } else {
            const firstCardValue = this.calculateScore([this.dealerCards[0]]);
            document.getElementById('dealer-score').textContent = `Scor: ${firstCardValue} + ?`;
        }

        // Check for bust
        if (playerScore > 21) {
            this.endBlackjackGame('bust');
        } else if (playerScore === 21) {
            this.blackjackStand();
        }
    }

    blackjackHit() {
        if (this.gameOver) return;

        this.playerCards.push(this.drawCard());
        this.updateBlackjackDisplay();
    }

    blackjackStand() {
        if (this.gameOver) return;

        this.gameOver = true;

        // Dealer plays
        while (this.calculateScore(this.dealerCards) < 17) {
            this.dealerCards.push(this.drawCard());
        }

        this.updateBlackjackDisplay();

        const playerScore = this.calculateScore(this.playerCards);
        const dealerScore = this.calculateScore(this.dealerCards);

        if (dealerScore > 21) {
            this.endBlackjackGame('dealer-bust');
        } else if (playerScore > dealerScore) {
            this.endBlackjackGame('win');
        } else if (playerScore === dealerScore) {
            this.endBlackjackGame('tie');
        } else {
            this.endBlackjackGame('lose');
        }
    }

    endBlackjackGame(result) {
        this.gameOver = true;

        // Disable game buttons
        document.getElementById('hit-btn').disabled = true;
        document.getElementById('stand-btn').disabled = true;

        const resultDiv = document.getElementById('blackjack-result');
        const playerScore = this.calculateScore(this.playerCards);
        const dealerScore = this.calculateScore(this.dealerCards);

        switch(result) {
            case 'win':
            case 'dealer-bust':
                const winAmount = 30; // 2x bet
                this.credits += winAmount;
                this.updateCreditsDisplay();

                resultDiv.innerHTML = `
                    <div class="game-result win">
                        🎉 AI CÂȘTIGAT! 🎉<br>
                        Tu: ${playerScore} | Dealer: ${dealerScore}<br>
                        Ai câștigat ${winAmount} credite!
                    </div>
                `;

                this.addChatMessage('🃏 Blackjack', `🎉 CÂȘTIG! ${playerScore} vs ${dealerScore} = ${winAmount} credite!`, 'win');
                break;

            case 'bust':
            case 'lose':
                resultDiv.innerHTML = `
                    <div class="game-result lose">
                        😔 AI PIERDUT!<br>
                        Tu: ${playerScore} | Dealer: ${dealerScore}<br>
                        ${result === 'bust' ? 'Ai depășit 21!' : 'Dealer-ul a câștigat!'}
                    </div>
                `;

                this.addChatMessage('🃏 Blackjack', `${playerScore} vs ${dealerScore} - ${result === 'bust' ? 'Bust!' : 'Dealer câștigă!'}`, 'lose');
                break;

            case 'tie':
                // Return bet
                this.credits += 15;
                this.updateCreditsDisplay();

                resultDiv.innerHTML = `
                    <div class="game-result" style="background: #ffa502; color: #fff;">
                        🤝 EGALITATE!<br>
                        Tu: ${playerScore} | Dealer: ${dealerScore}<br>
                        Credite returnate!
                    </div>
                `;

                this.addChatMessage('🃏 Blackjack', `Egalitate ${playerScore}-${dealerScore}! Credite returnate.`, 'tie');
                break;
        }
    }

    createPokerGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🃖 POKER CASINO</div>
                <div class="game-board">
                    <div class="poker-table">
                        <h4>🎯 Cărțile tale:</h4>
                        <div class="card-area" id="poker-cards"></div>
                        <div id="poker-hand" style="margin: 15px 0; font-size: 1.2em; color: #ffd700;"></div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="deal-poker">🃏 ÎMPARTE CĂRȚI</button>
                        <button class="control-btn" id="hold-cards" disabled>✋ ȚINE CĂRȚILE</button>
                        <button class="control-btn" id="draw-cards" disabled>🔄 SCHIMBĂ CĂRȚILE</button>
                    </div>
                    <div class="betting-interface">
                        <h4>💰 Combinații câștigătoare:</h4>
                        <p>🃏 Pereche = 40 credite | 🃏🃏 Două perechi = 80 credite</p>
                        <p>🃏🃏🃏 Brelan = 120 credite | 🃏🃏🃏🃏🃏 Straight = 200 credite</p>
                        <p>🃏🃏🃏🃏🃏 Flush = 300 credite | 🃏🃏🃏🃏 Poker = 800 credite</p>
                        <p>🃏🃏🃏🃏🃏 Royal Flush = 2000 credite</p>
                    </div>
                    <div id="poker-result"></div>
                </div>
            </div>
        `;

        this.setupPokerControls();
    }

    setupPokerControls() {
        this.pokerCards = [];
        this.selectedCards = new Set();

        document.getElementById('deal-poker').addEventListener('click', () => {
            this.dealPokerCards();
        });

        document.getElementById('hold-cards').addEventListener('click', () => {
            this.holdPokerCards();
        });

        document.getElementById('draw-cards').addEventListener('click', () => {
            this.drawPokerCards();
        });
    }

    dealPokerCards() {
        this.pokerCards = [];
        this.selectedCards.clear();

        // Create fresh deck and deal 5 cards
        this.deck = this.createDeck();
        for (let i = 0; i < 5; i++) {
            this.pokerCards.push(this.drawCard());
        }

        this.displayPokerCards();
        this.evaluatePokerHand();

        document.getElementById('deal-poker').disabled = true;
        document.getElementById('hold-cards').disabled = false;
        document.getElementById('draw-cards').disabled = false;

        // Clear previous result
        document.getElementById('poker-result').innerHTML = '';
    }

    displayPokerCards() {
        const cardsDiv = document.getElementById('poker-cards');
        cardsDiv.innerHTML = this.pokerCards.map((card, index) => `
            <div class="playing-card poker-card ${['♥', '♦'].includes(card.suit) ? 'red' : 'black'} ${this.selectedCards.has(index) ? 'selected' : ''}"
                 data-index="${index}" onclick="window.casino.togglePokerCard(${index})" style="
                 width: 100px;
                 height: 140px;
                 background: #fff;
                 border: 2px solid #333;
                 border-radius: 10px;
                 display: flex;
                 flex-direction: column;
                 align-items: center;
                 justify-content: center;
                 font-size: 1.4em;
                 font-weight: bold;
                 margin: 5px;
                 cursor: pointer;
                 position: relative;
                 box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
                 ">
                <div style="font-size: 1.2em;">${card.rank}</div>
                <div style="font-size: 1.5em; margin-top: 5px;">${card.suit}</div>
                ${this.selectedCards.has(index) ? '<div class="hold-indicator">HOLD</div>' : ''}
            </div>
        `).join('');
    }

    togglePokerCard(index) {
        if (this.selectedCards.has(index)) {
            this.selectedCards.delete(index);
        } else {
            this.selectedCards.add(index);
        }
        this.displayPokerCards();
    }

    holdPokerCards() {
        // Replace non-selected cards
        for (let i = 0; i < 5; i++) {
            if (!this.selectedCards.has(i)) {
                this.pokerCards[i] = this.drawCard();
            }
        }

        this.displayPokerCards();
        this.evaluatePokerHand();
        this.endPokerGame();
    }

    drawPokerCards() {
        // Replace all cards
        for (let i = 0; i < 5; i++) {
            this.pokerCards[i] = this.drawCard();
        }

        this.displayPokerCards();
        this.evaluatePokerHand();
        this.endPokerGame();
    }

    evaluatePokerHand() {
        const ranks = this.pokerCards.map(card => card.rank);
        const suits = this.pokerCards.map(card => card.suit);

        // Count ranks
        const rankCounts = {};
        ranks.forEach(rank => {
            rankCounts[rank] = (rankCounts[rank] || 0) + 1;
        });

        const counts = Object.values(rankCounts).sort((a, b) => b - a);
        const isFlush = suits.every(suit => suit === suits[0]);

        // Check for straight
        const rankValues = ranks.map(rank => {
            if (rank === 'A') return 14;
            if (rank === 'K') return 13;
            if (rank === 'Q') return 12;
            if (rank === 'J') return 11;
            return parseInt(rank);
        }).sort((a, b) => a - b);

        const isStraight = rankValues.every((val, i) => i === 0 || val === rankValues[i-1] + 1);
        const isRoyalFlush = isFlush && isStraight && rankValues[0] === 10;

        let handName = '';
        let payout = 0;

        if (isRoyalFlush) {
            handName = 'Royal Flush';
            payout = 2000;
        } else if (isStraight && isFlush) {
            handName = 'Straight Flush';
            payout = 1000;
        } else if (counts[0] === 4) {
            handName = 'Poker (Four of a Kind)';
            payout = 800;
        } else if (counts[0] === 3 && counts[1] === 2) {
            handName = 'Full House';
            payout = 400;
        } else if (isFlush) {
            handName = 'Flush';
            payout = 300;
        } else if (isStraight) {
            handName = 'Straight';
            payout = 200;
        } else if (counts[0] === 3) {
            handName = 'Brelan (Three of a Kind)';
            payout = 120;
        } else if (counts[0] === 2 && counts[1] === 2) {
            handName = 'Două Perechi';
            payout = 80;
        } else if (counts[0] === 2) {
            handName = 'Pereche';
            payout = 40;
        } else {
            handName = 'Carte Mare';
            payout = 0;
        }

        document.getElementById('poker-hand').textContent = `Mâna ta: ${handName}`;
        this.pokerPayout = payout;
    }

    endPokerGame() {
        const resultDiv = document.getElementById('poker-result');

        if (this.pokerPayout > 0) {
            this.credits += this.pokerPayout;
            this.updateCreditsDisplay();

            resultDiv.innerHTML = `
                <div class="game-result win">
                    🎉 CÂȘTIG! 🎉<br>
                    ${document.getElementById('poker-hand').textContent}<br>
                    Ai câștigat ${this.pokerPayout} credite!
                </div>
            `;

            this.addChatMessage('🃖 Poker', `🎉 ${document.getElementById('poker-hand').textContent.split(': ')[1]} = ${this.pokerPayout} credite!`, 'win');
        } else {
            resultDiv.innerHTML = `
                <div class="game-result lose">
                    😔 Fără câștig!<br>
                    ${document.getElementById('poker-hand').textContent}<br>
                    Încearcă din nou!
                </div>
            `;

            this.addChatMessage('🃖 Poker', 'Carte mare - fără câștig. Încearcă din nou!', 'lose');
        }

        // Reset for next game
        setTimeout(() => {
            document.getElementById('deal-poker').disabled = false;
            document.getElementById('hold-cards').disabled = true;
            document.getElementById('draw-cards').disabled = true;
        }, 3000);
    }

    createBingoGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🎱 BINGO CASINO</div>
                <div class="game-board">
                    <div class="bingo-card">
                        <h4>🎯 Cardul tău BINGO:</h4>
                        <div class="bingo-grid" id="bingo-grid"></div>
                    </div>
                    <div class="bingo-numbers">
                        <h4>🎱 Numere extrase:</h4>
                        <div id="drawn-numbers"></div>
                        <div id="current-number" style="font-size: 2em; color: #ffd700; margin: 15px 0;"></div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="start-bingo">🎱 ÎNCEPE BINGO</button>
                        <button class="control-btn" id="auto-mark" disabled>✅ AUTO MARK</button>
                    </div>
                    <div id="bingo-result"></div>
                </div>
            </div>
        `;

        this.setupBingoControls();
        this.generateBingoCard();
    }

    setupBingoControls() {
        this.bingoCard = [];
        this.drawnNumbers = [];
        this.markedNumbers = new Set();

        document.getElementById('start-bingo').addEventListener('click', () => {
            this.startBingo();
        });

        document.getElementById('auto-mark').addEventListener('click', () => {
            this.autoMarkBingo();
        });
    }

    generateBingoCard() {
        this.bingoCard = [];

        // Generate 5x5 bingo card with FREE space in center
        for (let i = 0; i < 25; i++) {
            if (i === 12) { // Center space
                this.bingoCard.push('FREE');
            } else {
                let num;
                do {
                    num = Math.floor(Math.random() * 75) + 1;
                } while (this.bingoCard.includes(num));
                this.bingoCard.push(num);
            }
        }

        const grid = document.getElementById('bingo-grid');
        grid.innerHTML = this.bingoCard.map((num, index) => `
            <div class="bingo-cell ${num === 'FREE' ? 'free-space marked' : ''}"
                 data-index="${index}" onclick="window.casino.toggleBingoCell(${index})">
                ${num}
            </div>
        `).join('');

        if (this.bingoCard[12] === 'FREE') {
            this.markedNumbers.add(12);
        }
    }

    toggleBingoCell(index) {
        const cell = document.querySelector(`[data-index="${index}"]`);
        const number = this.bingoCard[index];

        if (number === 'FREE') return;

        if (this.drawnNumbers.includes(number)) {
            if (this.markedNumbers.has(index)) {
                this.markedNumbers.delete(index);
                cell.classList.remove('marked');
            } else {
                this.markedNumbers.add(index);
                cell.classList.add('marked');
            }
        }
    }

    startBingo() {
        this.drawnNumbers = [];
        this.markedNumbers.clear();
        this.markedNumbers.add(12); // FREE space

        document.getElementById('start-bingo').disabled = true;
        document.getElementById('auto-mark').disabled = false;
        document.getElementById('drawn-numbers').innerHTML = '';
        document.getElementById('current-number').textContent = '';

        // Reset card
        document.querySelectorAll('.bingo-cell').forEach((cell, index) => {
            if (index !== 12) {
                cell.classList.remove('marked');
            }
        });

        this.drawBingoNumbers();
    }

    drawBingoNumbers() {
        const drawInterval = setInterval(() => {
            if (this.drawnNumbers.length >= 30 || this.checkBingo()) {
                clearInterval(drawInterval);
                this.endBingoGame();
                return;
            }

            let number;
            do {
                number = Math.floor(Math.random() * 75) + 1;
            } while (this.drawnNumbers.includes(number));

            this.drawnNumbers.push(number);

            document.getElementById('current-number').textContent = `Numărul: ${number}`;
            document.getElementById('drawn-numbers').innerHTML = this.drawnNumbers.join(', ');

            // Check if number is on card
            const cardIndex = this.bingoCard.indexOf(number);
            if (cardIndex !== -1) {
                document.querySelector(`[data-index="${cardIndex}"]`).style.background = '#ffed4e';
                document.querySelector(`[data-index="${cardIndex}"]`).style.color = '#000';
            }

        }, 1500);
    }

    autoMarkBingo() {
        this.drawnNumbers.forEach(number => {
            const cardIndex = this.bingoCard.indexOf(number);
            if (cardIndex !== -1) {
                this.markedNumbers.add(cardIndex);
                document.querySelector(`[data-index="${cardIndex}"]`).classList.add('marked');
            }
        });

        if (this.checkBingo()) {
            this.endBingoGame();
        }
    }

    checkBingo() {
        const lines = [
            // Rows
            [0,1,2,3,4], [5,6,7,8,9], [10,11,12,13,14], [15,16,17,18,19], [20,21,22,23,24],
            // Columns
            [0,5,10,15,20], [1,6,11,16,21], [2,7,12,17,22], [3,8,13,18,23], [4,9,14,19,24],
            // Diagonals
            [0,6,12,18,24], [4,8,12,16,20]
        ];

        return lines.some(line => line.every(index => this.markedNumbers.has(index)));
    }

    endBingoGame() {
        const resultDiv = document.getElementById('bingo-result');

        if (this.checkBingo()) {
            const winAmount = 240; // 20x bet
            this.credits += winAmount;
            this.updateCreditsDisplay();

            resultDiv.innerHTML = `
                <div class="game-result win">
                    🎉 BINGO! 🎉<br>
                    Ai completat o linie!<br>
                    Ai câștigat ${winAmount} credite!
                </div>
            `;

            this.addChatMessage('🎱 Bingo', `🎉 BINGO! Linie completă = ${winAmount} credite!`, 'win');
        } else {
            resultDiv.innerHTML = `
                <div class="game-result lose">
                    😔 Fără BINGO!<br>
                    Nu ai completat o linie.<br>
                    Încearcă din nou!
                </div>
            `;

            this.addChatMessage('🎱 Bingo', 'Fără BINGO de această dată. Încearcă din nou!', 'lose');
        }

        // Reset for next game
        setTimeout(() => {
            document.getElementById('start-bingo').disabled = false;
            document.getElementById('auto-mark').disabled = true;
            this.generateBingoCard();
        }, 3000);
    }

    createWheelGame() {
        // Roată realistă cu segmente de dimensiuni diferite
        const wheelSegments = [
            // Segmente mari (șanse mari) - 45 grade fiecare
            { type: 'credits', value: 50, text: '50', color: '#3742fa', size: 45, weight: 0.25 },
            { type: 'credits', value: 100, text: '100', color: '#2ed573', size: 45, weight: 0.20 },
            { type: 'lose', value: -30, text: 'LOSE 30', color: '#ff6b6b', size: 45, weight: 0.15 },
            { type: 'credits', value: 75, text: '75', color: '#ffa502', size: 45, weight: 0.15 },

            // Segmente medii - 30 grade fiecare
            { type: 'skip', value: 0, text: 'SKIP', color: '#5f27cd', size: 30, weight: 0.10 },
            { type: 'credits', value: 200, text: '200', color: '#ff4757', size: 30, weight: 0.08 },
            { type: 'lose', value: -50, text: 'LOSE 50', color: '#2f3542', size: 30, weight: 0.05 },

            // Segmente mici (rare) - 15 grade fiecare
            { type: 'double', value: 0, text: 'DOUBLE', color: '#ff9ff3', size: 15, weight: 0.015 },
            { type: 'bonus', value: 500, text: 'BONUS', color: '#00d2d3', size: 15, weight: 0.01 },

            // Segmente foarte mici (foarte rare) - 10 grade fiecare
            { type: 'jackpot', value: 1000, text: 'JACKPOT', color: '#ffd700', size: 10, weight: 0.005 },
            { type: 'bankrupt', value: 0, text: 'BANKRUPT', color: '#000000', size: 10, weight: 0.005 }
        ];

        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🎡 WHEEL OF FORTUNE</div>
                <div class="game-board">
                    <div class="wheel-container">
                        <div class="fortune-wheel" id="fortune-wheel">
                            <div class="wheel-pointer">🔻</div>
                            <div class="wheel-center">
                                <div class="center-logo">🎡</div>
                                <div class="center-text">SPIN</div>
                            </div>
                            <svg class="wheel-svg" viewBox="0 0 400 400" width="400" height="400">
                                ${this.generateWheelSegments(wheelSegments)}
                            </svg>
                        </div>
                    </div>
                    <div class="wheel-legend">
                        <h4>🎯 Premii și probabilități:</h4>
                        <div class="legend-grid">
                            <div class="legend-row">
                                <span class="legend-common">🟦 Comune (70%)</span>
                                <span>50, 75, 100 credite | Lose 30</span>
                            </div>
                            <div class="legend-row">
                                <span class="legend-rare">🟨 Rare (23%)</span>
                                <span>200 credite | Skip Turn | Lose 50</span>
                            </div>
                            <div class="legend-row">
                                <span class="legend-epic">🟪 Epice (2.5%)</span>
                                <span>Double Credits | Bonus 500</span>
                            </div>
                            <div class="legend-row">
                                <span class="legend-legendary">🟨 Legendare (1%)</span>
                                <span>💰 JACKPOT 1000 | 💀 BANKRUPT</span>
                            </div>
                        </div>
                        <div class="wheel-stats">
                            <div class="stat">Ultima rotire: <span id="last-spin">-</span></div>
                            <div class="stat">Jackpot-uri: <span id="jackpot-count">0</span></div>
                            <div class="stat">Bankrupt-uri: <span id="bankrupt-count">0</span></div>
                        </div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="spin-wheel">🎡 ÎNVÂRTE ROATA NOROCULUI</button>
                    </div>
                    <div id="wheel-result"></div>
                </div>
            </div>
        `;

        this.wheelSegments = wheelSegments;
        this.jackpotCount = 0;
        this.bankruptCount = 0;
        this.setupWheelControls();
    }

    generateWheelSegments(segments) {
        let currentAngle = 0;
        return segments.map((segment, index) => {
            const startAngle = currentAngle;
            const endAngle = currentAngle + segment.size;
            currentAngle = endAngle;

            // Convert to radians
            const startRad = (startAngle * Math.PI) / 180;
            const endRad = (endAngle * Math.PI) / 180;

            // Calculate path coordinates
            const centerX = 200;
            const centerY = 200;
            const radius = 180;

            const x1 = centerX + radius * Math.cos(startRad);
            const y1 = centerY + radius * Math.sin(startRad);
            const x2 = centerX + radius * Math.cos(endRad);
            const y2 = centerY + radius * Math.sin(endRad);

            const largeArcFlag = segment.size > 180 ? 1 : 0;

            const pathData = [
                `M ${centerX} ${centerY}`,
                `L ${x1} ${y1}`,
                `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                'Z'
            ].join(' ');

            // Text position
            const textAngle = (startAngle + endAngle) / 2;
            const textRad = (textAngle * Math.PI) / 180;
            const textRadius = radius * 0.7;
            const textX = centerX + textRadius * Math.cos(textRad);
            const textY = centerY + textRadius * Math.sin(textRad);

            return `
                <path d="${pathData}"
                      fill="${segment.color}"
                      stroke="#fff"
                      stroke-width="2"
                      data-index="${index}"
                      class="wheel-segment-path"/>
                <text x="${textX}"
                      y="${textY}"
                      text-anchor="middle"
                      dominant-baseline="middle"
                      fill="${segment.color === '#ffd700' || segment.color === '#00d2d3' ? '#000' : '#fff'}"
                      font-weight="bold"
                      font-size="${segment.size > 30 ? '14' : segment.size > 15 ? '12' : '10'}"
                      class="wheel-text">
                    ${segment.text}
                </text>
            `;
        }).join('');
    }

    setupWheelControls() {
        document.getElementById('spin-wheel').addEventListener('click', () => {
            this.startWheelQuiz();
        });
    }

    startWheelQuiz() {
        // Selectează o întrebare aleatorie
        const randomQuestion = this.quizQuestions[Math.floor(Math.random() * this.quizQuestions.length)];

        // Creează interfața quiz-ului
        const quizOverlay = document.createElement('div');
        quizOverlay.className = 'quiz-overlay';
        quizOverlay.innerHTML = `
            <div class="quiz-container">
                <div class="quiz-header">
                    <h2>🧠 QUIZ PENTRU WHEEL OF FORTUNE</h2>
                    <p>Răspunde corect pentru a avea dreptul să dai la roată!</p>
                    <p style="color: #ff4757;"><strong>Atenție:</strong> Răspuns greșit = -10 credite</p>
                </div>
                <div class="quiz-question">
                    <h3>${randomQuestion.question}</h3>
                </div>
                <div class="quiz-answers">
                    ${randomQuestion.answers.map((answer, index) => `
                        <button class="quiz-answer-btn" data-answer="${index}">
                            ${String.fromCharCode(65 + index)}. ${answer}
                        </button>
                    `).join('')}
                </div>
                <div class="quiz-timer">
                    <div class="timer-bar" id="timer-bar"></div>
                    <div class="timer-text">Timp rămas: <span id="timer-text">15</span>s</div>
                </div>
            </div>
        `;

        document.body.appendChild(quizOverlay);

        // Adaugă event listeners pentru răspunsuri
        document.querySelectorAll('.quiz-answer-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const selectedAnswer = parseInt(e.target.dataset.answer);
                this.handleQuizAnswer(selectedAnswer, randomQuestion.correct, quizOverlay);
            });
        });

        // Timer pentru quiz
        this.startQuizTimer(randomQuestion.correct, quizOverlay);
    }

    startQuizTimer(correctAnswer, quizOverlay) {
        let timeLeft = 15;
        const timerBar = document.getElementById('timer-bar');
        const timerText = document.getElementById('timer-text');

        const timer = setInterval(() => {
            timeLeft--;
            timerText.textContent = timeLeft;
            timerBar.style.width = (timeLeft / 15) * 100 + '%';

            if (timeLeft <= 5) {
                timerBar.style.background = '#ff4757';
            } else if (timeLeft <= 10) {
                timerBar.style.background = '#ffa502';
            }

            if (timeLeft <= 0) {
                clearInterval(timer);
                this.handleQuizAnswer(-1, correctAnswer, quizOverlay); // Timeout
            }
        }, 1000);

        // Salvează timer-ul pentru a-l putea opri
        this.currentQuizTimer = timer;
    }

    handleQuizAnswer(selectedAnswer, correctAnswer, quizOverlay) {
        // Oprește timer-ul
        if (this.currentQuizTimer) {
            clearInterval(this.currentQuizTimer);
        }

        // Disable toate butoanele
        document.querySelectorAll('.quiz-answer-btn').forEach(btn => {
            btn.disabled = true;
        });

        if (selectedAnswer === correctAnswer) {
            // Răspuns corect
            document.querySelector(`[data-answer="${correctAnswer}"]`).style.background = '#2ed573';

            setTimeout(() => {
                quizOverlay.remove();
                this.showQuizResult(true);
                // Permite accesul la roată
                setTimeout(() => this.spinWheel(), 1000);
            }, 2000);

        } else {
            // Răspuns greșit sau timeout
            if (selectedAnswer !== -1) {
                document.querySelector(`[data-answer="${selectedAnswer}"]`).style.background = '#ff4757';
            }
            document.querySelector(`[data-answer="${correctAnswer}"]`).style.background = '#2ed573';

            // Penalizare: -10 credite
            this.credits = Math.max(0, this.credits - 10);
            this.updateCreditsDisplay();

            setTimeout(() => {
                quizOverlay.remove();
                this.showQuizResult(false);
            }, 3000);
        }
    }

    showQuizResult(isCorrect) {
        const resultMessage = document.createElement('div');
        resultMessage.className = `quiz-result ${isCorrect ? 'correct' : 'incorrect'}`;

        if (isCorrect) {
            resultMessage.innerHTML = `
                <div class="result-icon">✅</div>
                <div class="result-title">RĂSPUNS CORECT!</div>
                <div class="result-subtitle">Ai dreptul să dai la Wheel of Fortune!</div>
            `;
            this.playSound('win');
        } else {
            resultMessage.innerHTML = `
                <div class="result-icon">❌</div>
                <div class="result-title">RĂSPUNS GREȘIT!</div>
                <div class="result-subtitle">-10 credite penalizare</div>
            `;
            this.playSound('lose');
        }

        document.body.appendChild(resultMessage);
        setTimeout(() => resultMessage.remove(), 3000);
    }

    spinWheel() {
        const spinBtn = document.getElementById('spin-wheel');
        spinBtn.disabled = true;
        spinBtn.textContent = '🎡 SE ÎNVÂRTE...';

        const wheel = document.getElementById('fortune-wheel');

        // Selectează premiul pe baza probabilităților
        const selectedSegment = this.selectWheelPrize();

        // Calculează unghiul pentru segmentul selectat
        let targetAngle = 0;
        for (let i = 0; i < selectedSegment.index; i++) {
            targetAngle += this.wheelSegments[i].size;
        }
        // Adaugă jumătate din dimensiunea segmentului pentru centru
        targetAngle += this.wheelSegments[selectedSegment.index].size / 2;

        const spins = 5 + Math.random() * 3; // 5-8 rotații
        const totalRotation = (spins * 360) + (360 - targetAngle);

        wheel.style.transition = 'transform 4s cubic-bezier(0.25, 0.1, 0.25, 1)';
        wheel.style.transform = `rotate(${totalRotation}deg)`;

        setTimeout(() => {
            const prize = selectedSegment.segment;
            const resultDiv = document.getElementById('wheel-result');
            let resultMessage = '';
            let resultClass = 'lose';
            let chatMessage = '';

            // Update statistics
            document.getElementById('last-spin').textContent = prize.text;

            switch(prize.type) {
                case 'credits':
                    this.addWinnings(prize.value, 'wheel');
                    const pointsEarned = Math.floor(prize.value * 0.1);
                    resultMessage = `🎉 CÂȘTIG! 🎉<br><div style="font-size: 2em; margin: 10px 0;">💰 +${prize.value} 💰</div>Ai câștigat ${prize.value} credite!<br>🏆 +${pointsEarned} puncte`;
                    resultClass = 'win';
                    chatMessage = `🎡 Wheel: +${prize.value} credite + ${pointsEarned} puncte!`;
                    break;

                case 'jackpot':
                    this.credits += prize.value;
                    this.updateCreditsDisplay();
                    this.jackpotCount++;
                    document.getElementById('jackpot-count').textContent = this.jackpotCount;
                    resultMessage = `🎉 JACKPOT LEGENDAR! 🎉<br><div style="font-size: 2em; margin: 10px 0;">🏆 ${prize.value} 🏆</div>JACKPOT URIAȘ! (1% șansă)`;
                    resultClass = 'win';
                    chatMessage = `🎡 Wheel: 🏆 JACKPOT LEGENDAR ${prize.value} credite!`;
                    break;

                case 'bonus':
                    this.credits += prize.value;
                    this.updateCreditsDisplay();
                    resultMessage = `🎉 BONUS EPIC! 🎉<br><div style="font-size: 2em; margin: 10px 0;">🎁 ${prize.value} 🎁</div>Bonus epic! (2.5% șansă)`;
                    resultClass = 'win';
                    chatMessage = `🎡 Wheel: 🎁 BONUS EPIC ${prize.value} credite!`;
                    break;

                case 'double':
                    const doubleAmount = this.credits;
                    this.credits += doubleAmount;
                    this.updateCreditsDisplay();
                    resultMessage = `🎉 DOUBLE EPIC! 🎉<br><div style="font-size: 2em; margin: 10px 0;">⚡ x2 ⚡</div>Creditele dublate! (2.5% șansă)<br>+${doubleAmount} credite`;
                    resultClass = 'win';
                    chatMessage = `🎡 Wheel: ⚡ DOUBLE EPIC! +${doubleAmount} credite!`;
                    break;

                case 'skip':
                    // Return the bet
                    this.credits += 6;
                    this.updateCreditsDisplay();
                    resultMessage = `🎯 SKIP TURN! 🎯<br><div style="font-size: 2em; margin: 10px 0;">⏭️ FREE ⏭️</div>Joacă din nou gratis!`;
                    resultClass = 'win';
                    chatMessage = `🎡 Wheel: ⏭️ Skip turn - joacă gratis!`;
                    break;

                case 'lose':
                    const loseAmount = Math.min(Math.abs(prize.value), this.credits);
                    this.credits -= loseAmount;
                    this.updateCreditsDisplay();
                    resultMessage = `😔 PIERDERE! 😔<br><div style="font-size: 2em; margin: 10px 0;">📉 -${loseAmount} 📉</div>Ai pierdut ${loseAmount} credite!`;
                    resultClass = 'lose';
                    chatMessage = `🎡 Wheel: 📉 Pierdere ${loseAmount} credite!`;
                    break;

                case 'bankrupt':
                    const lostCredits = this.credits;
                    this.credits = 0;
                    this.updateCreditsDisplay();
                    this.bankruptCount++;
                    document.getElementById('bankrupt-count').textContent = this.bankruptCount;
                    resultMessage = `💥 BANKRUPT LEGENDAR! 💥<br><div style="font-size: 2em; margin: 10px 0;">💀 ZERO 💀</div>Toate creditele pierdute! (1% șansă)<br>-${lostCredits} credite`;
                    resultClass = 'lose';
                    chatMessage = `🎡 Wheel: 💥 BANKRUPT LEGENDAR! Toate creditele pierdute!`;
                    break;
            }

            resultDiv.innerHTML = `<div class="game-result ${resultClass}">${resultMessage}</div>`;
            this.addChatMessage('🎡 Wheel', chatMessage, resultClass);

            // Highlight winning segment
            const segmentPaths = document.querySelectorAll('.wheel-segment-path');
            if (segmentPaths[selectedSegment.index]) {
                segmentPaths[selectedSegment.index].style.stroke = '#ffd700';
                segmentPaths[selectedSegment.index].style.strokeWidth = '4';
                segmentPaths[selectedSegment.index].style.filter = 'drop-shadow(0 0 10px #ffd700)';
            }

            // Reset for next game
            setTimeout(() => {
                wheel.style.transition = '';
                wheel.style.transform = '';

                if (segmentPaths[selectedSegment.index]) {
                    segmentPaths[selectedSegment.index].style.stroke = '#fff';
                    segmentPaths[selectedSegment.index].style.strokeWidth = '2';
                    segmentPaths[selectedSegment.index].style.filter = '';
                }

                spinBtn.disabled = false;
                spinBtn.textContent = '🎡 ÎNVÂRTE ROATA NOROCULUI';
            }, 4000);

        }, 4000);
    }

    selectWheelPrize() {
        const random = Math.random();
        let cumulativeWeight = 0;

        for (let i = 0; i < this.wheelSegments.length; i++) {
            cumulativeWeight += this.wheelSegments[i].weight;
            if (random <= cumulativeWeight) {
                return { segment: this.wheelSegments[i], index: i };
            }
        }

        // Fallback la primul segment
        return { segment: this.wheelSegments[0], index: 0 };
    }

    createCrashGame() {
        this.elements.gameArea.innerHTML = `
            <div class="game-interface">
                <div class="game-title">🚀 CRASH GAME</div>
                <div class="game-board">
                    <div class="crash-display">
                        <div class="crash-stats">
                            <div class="stat-item">
                                <span>Miză:</span>
                                <span id="crash-bet">7 credite</span>
                            </div>
                            <div class="stat-item">
                                <span>Câștig potențial:</span>
                                <span id="potential-win">7 credite</span>
                            </div>
                        </div>
                        <div class="crash-graph">
                            <div id="crash-multiplier">1.00x</div>
                            <div class="rocket" id="rocket">🚀</div>
                            <div class="crash-line" id="crash-line"></div>
                        </div>
                        <div class="crash-history">
                            <h4>📊 Ultimele rezultate:</h4>
                            <div id="crash-history-list">
                                <span class="history-item">2.45x</span>
                                <span class="history-item">1.23x</span>
                                <span class="history-item">5.67x</span>
                                <span class="history-item">1.89x</span>
                                <span class="history-item">3.21x</span>
                            </div>
                        </div>
                    </div>
                    <div class="game-controls">
                        <button class="control-btn" id="start-crash">🚀 LANSEAZĂ RACHETA</button>
                        <button class="control-btn cash-out-btn" id="cash-out" disabled>💰 CASH OUT</button>
                    </div>
                    <div class="betting-interface">
                        <div class="crash-info">
                            <h4>🎯 Cum să joci:</h4>
                            <p>• Racheta pornește cu multiplicatorul 1.00x</p>
                            <p>• Multiplicatorul crește în timp real</p>
                            <p>• Apasă CASH OUT înainte să se prăbușească</p>
                            <p>• Cu cât aștepți mai mult, cu atât câștigi mai mult!</p>
                            <p>• Dar atenție - racheta se poate prăbuși oricând!</p>
                        </div>
                        <div class="risk-meter">
                            <h4>⚠️ Risc:</h4>
                            <div class="risk-bar">
                                <div class="risk-level" id="risk-level"></div>
                            </div>
                            <div class="risk-labels">
                                <span>Sigur</span>
                                <span>Risc</span>
                                <span>Pericol</span>
                            </div>
                        </div>
                    </div>
                    <div id="crash-result"></div>
                </div>
            </div>
        `;

        this.crashHistory = [2.45, 1.23, 5.67, 1.89, 3.21];
        this.setupCrashControls();
    }

    setupCrashControls() {
        document.getElementById('start-crash').addEventListener('click', () => {
            this.startCrash();
        });

        document.getElementById('cash-out').addEventListener('click', () => {
            this.cashOut();
        });
    }

    startCrash() {
        const startBtn = document.getElementById('start-crash');
        const cashOutBtn = document.getElementById('cash-out');

        startBtn.disabled = true;
        startBtn.textContent = '🚀 RACHETA ZBOARĂ...';
        cashOutBtn.disabled = false;
        cashOutBtn.classList.add('pulsing');

        this.crashMultiplier = 1.00;
        this.crashTarget = this.generateCrashTarget();
        this.crashActive = true;

        const multiplierDisplay = document.getElementById('crash-multiplier');
        const rocket = document.getElementById('rocket');
        const potentialWin = document.getElementById('potential-win');
        const riskLevel = document.getElementById('risk-level');
        const crashLine = document.getElementById('crash-line');

        // Reset rocket position
        rocket.style.bottom = '50px';
        rocket.style.left = '20px';
        rocket.style.transition = 'all 0.1s linear';

        // Start line drawing
        crashLine.style.width = '0%';
        crashLine.style.height = '2px';
        crashLine.style.background = '#00ff00';

        let lineProgress = 0;

        const crashInterval = setInterval(() => {
            if (!this.crashActive) {
                clearInterval(crashInterval);
                return;
            }

            // Increase multiplier with variable speed
            const increment = 0.01 + (this.crashMultiplier * 0.001);
            this.crashMultiplier += increment;

            // Update displays
            multiplierDisplay.textContent = this.crashMultiplier.toFixed(2) + 'x';
            potentialWin.textContent = Math.floor(7 * this.crashMultiplier) + ' credite';

            // Move rocket
            const progress = Math.min((this.crashMultiplier - 1) / 9, 1);
            rocket.style.bottom = (50 + progress * 200) + 'px';
            rocket.style.left = (20 + progress * 250) + 'px';

            // Update line
            lineProgress = Math.min(progress * 100, 100);
            crashLine.style.width = lineProgress + '%';

            // Update risk meter
            const riskPercent = Math.min((this.crashMultiplier - 1) / (this.crashTarget - 1) * 100, 100);
            riskLevel.style.width = riskPercent + '%';

            if (riskPercent < 30) {
                riskLevel.style.background = '#00ff00';
                crashLine.style.background = '#00ff00';
            } else if (riskPercent < 70) {
                riskLevel.style.background = '#ffa500';
                crashLine.style.background = '#ffa500';
            } else {
                riskLevel.style.background = '#ff0000';
                crashLine.style.background = '#ff0000';
            }

            // Check for crash
            if (this.crashMultiplier >= this.crashTarget) {
                this.crashGame();
                clearInterval(crashInterval);
            }
        }, 100);
    }

    generateCrashTarget() {
        // More realistic crash distribution
        const rand = Math.random();
        if (rand < 0.3) return 1 + Math.random() * 0.5; // 30% chance: 1.00x - 1.50x
        if (rand < 0.6) return 1.5 + Math.random() * 1.5; // 30% chance: 1.50x - 3.00x
        if (rand < 0.85) return 3 + Math.random() * 2; // 25% chance: 3.00x - 5.00x
        return 5 + Math.random() * 15; // 15% chance: 5.00x - 20.00x
    }

    cashOut() {
        if (!this.crashActive) return;

        this.crashActive = false;
        const winAmount = Math.floor(7 * this.crashMultiplier);

        this.credits += winAmount;
        this.updateCreditsDisplay();

        // Add to history
        this.crashHistory.unshift(this.crashMultiplier);
        if (this.crashHistory.length > 5) this.crashHistory.pop();
        this.updateCrashHistory();

        const resultDiv = document.getElementById('crash-result');
        resultDiv.innerHTML = `
            <div class="game-result win">
                🎉 CASH OUT PERFECT! 🎉<br>
                <div style="font-size: 2em; margin: 10px 0;">🚀 ${this.crashMultiplier.toFixed(2)}x 🚀</div>
                Miză: 7 credite → Câștig: ${winAmount} credite<br>
                <strong>Profit: +${winAmount - 7} credite</strong>
            </div>
        `;

        this.addChatMessage('🚀 Crash', `🎉 Cash out perfect la ${this.crashMultiplier.toFixed(2)}x = ${winAmount} credite!`, 'win');

        this.resetCrashGame();
    }

    crashGame() {
        this.crashActive = false;

        // Add to history
        this.crashHistory.unshift(this.crashTarget);
        if (this.crashHistory.length > 5) this.crashHistory.pop();
        this.updateCrashHistory();

        // Crash animation
        const rocket = document.getElementById('rocket');
        rocket.style.transform = 'rotate(180deg)';
        rocket.textContent = '💥';

        const resultDiv = document.getElementById('crash-result');
        resultDiv.innerHTML = `
            <div class="game-result lose">
                💥 CRASH! 💥<br>
                <div style="font-size: 2em; margin: 10px 0;">💀 ${this.crashTarget.toFixed(2)}x 💀</div>
                Racheta s-a prăbușit!<br>
                <strong>Pierdere: -7 credite</strong>
            </div>
        `;

        this.addChatMessage('🚀 Crash', `💥 Crash la ${this.crashTarget.toFixed(2)}x! Racheta s-a prăbușit!`, 'lose');

        this.resetCrashGame();
    }

    updateCrashHistory() {
        const historyList = document.getElementById('crash-history-list');
        if (historyList) {
            historyList.innerHTML = this.crashHistory.map(mult =>
                `<span class="history-item ${mult >= 2 ? 'good' : mult >= 1.5 ? 'ok' : 'bad'}">${mult.toFixed(2)}x</span>`
            ).join('');
        }
    }

    resetCrashGame() {
        setTimeout(() => {
            const startBtn = document.getElementById('start-crash');
            const cashOutBtn = document.getElementById('cash-out');

            startBtn.disabled = false;
            startBtn.textContent = '🚀 LANSEAZĂ RACHETA';
            cashOutBtn.disabled = true;
            cashOutBtn.classList.remove('pulsing');

            document.getElementById('crash-multiplier').textContent = '1.00x';
            document.getElementById('potential-win').textContent = '7 credite';
            document.getElementById('risk-level').style.width = '0%';

            const rocket = document.getElementById('rocket');
            rocket.style.transition = '';
            rocket.style.bottom = '50px';
            rocket.style.left = '20px';
            rocket.style.transform = '';
            rocket.textContent = '🚀';

            const crashLine = document.getElementById('crash-line');
            crashLine.style.width = '0%';
        }, 4000);
    }
}

// Inițializează aplicația când pagina se încarcă
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing casino...');
    
    try {
        window.casino = new TikTokLiveCasino();
        console.log('TikTok Live Casino initialized successfully!');
        
        // Debug: Verifică dacă butoanele există
        const connectBtn = document.getElementById('connect-btn');
        const testBtn = document.getElementById('test-btn');
        
        console.log('Connect button:', connectBtn);
        console.log('Test button:', testBtn);
        
        if (!connectBtn) {
            console.error('Connect button not found!');
        }
        if (!testBtn) {
            console.error('Test button not found!');
        }
        
    } catch (error) {
        console.error('Error initializing casino:', error);
    }
});
