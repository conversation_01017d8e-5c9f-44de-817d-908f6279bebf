{"name": "dprint-node", "version": "1.0.8", "description": "A node API for the dprint TypeScript and JavaScript code formatter", "repository": "https://github.com/devongovett/dprint-node", "author": "<PERSON> Go<PERSON>t <<EMAIL>>", "license": "MIT", "napi": {"name": "dprint-node"}, "files": ["*.node", "index.js", "index.d.ts", "options.d.ts"], "scripts": {"build": "napi build --platform --dts false --js false", "build-release": "napi build --platform --release --dts false --js false"}, "dependencies": {"detect-libc": "^1.0.3"}, "devDependencies": {"@napi-rs/cli": "^2.11.4", "prettier": "^2.7.1", "tiny-benchy": "^2.1.0"}}