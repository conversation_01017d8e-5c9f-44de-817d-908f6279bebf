# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

<a name="2.0.3"></a>
## [2.0.3](https://github.com/medikoo/es6-iterator/compare/v2.0.2...v2.0.3) (2017-10-17)


### Bug Fixes

* configurability of toStringTag ([b99f692](https://github.com/medikoo/es6-iterator/commit/b99f692))



<a name="2.0.2"></a>
## [2.0.2](https://github.com/medikoo/es6-iterator/compare/v2.0.1...v2.0.2) (2017-10-17)


### Bug Fixes

* constructor exposure ([dbc0c51](https://github.com/medikoo/es6-iterator/commit/dbc0c51))
* do not allow non constructor calls ([1f2f800](https://github.com/medikoo/es6-iterator/commit/1f2f800))
* toString and toStringTag symbol definitions. ([2d17786](https://github.com/medikoo/es6-iterator/commit/2d17786)), closes [#6](https://github.com/medikoo/es6-iterator/issues/6)

## Changelog for previous versions

See `CHANGES` file
