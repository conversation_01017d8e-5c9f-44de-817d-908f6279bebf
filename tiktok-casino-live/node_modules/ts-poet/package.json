{"name": "ts-poet", "version": "6.12.0", "description": "code generation DSL for TypeScript", "main": "build/index.js", "types": "build/index.d.ts", "scripts": {"prepare": "yarn build && yarn test", "test": "yarn jest", "build": "yarn tsc", "format": "prettier --write './src/**/(*.ts|*.tsx|*.html|*.css)'"}, "repository": {"type": "git", "url": "https://github.com/stephenh/ts-poet.git"}, "keywords": [], "author": "<PERSON>", "license": "Apache-2.0", "devDependencies": {"@types/jest": "^29.5.11", "@types/mock-fs": "^4.13.1", "@types/node": "^20.10.6", "jest": "^29.7.0", "mock-fs": "^5.2.0", "prettier": "3.1.1", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "dependencies": {"dprint-node": "^1.0.8"}, "packageManager": "yarn@4.0.2"}