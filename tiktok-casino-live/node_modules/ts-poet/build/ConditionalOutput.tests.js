"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const src_1 = require("../src");
describe("conditional outputs in the utilities file", () => {
    it("basic usage", () => {
        const util = (0, src_1.conditionalOutput)("add", (0, src_1.code) `const add(a: number, b: number) => a + b`);
        const codeChunk = (0, src_1.code) `const result = ${util}(1, 2)`;
        expect(codeChunk.toString({ conditionalUtils: "./utils.ts" })).toBe('import { add } from "./utils.ts";\n\nconst result = add(1, 2);\n');
    });
    it("import type", () => {
        const util = (0, src_1.conditionalOutput)("SomeId", (0, src_1.code) `type SomeId = string | number`, true);
        const codeChunk = (0, src_1.code) `declare const id: ${util}`;
        expect(codeChunk.toString({ conditionalUtils: "./utils.ts" })).toBe('import type { SomeId } from "./utils.ts";\n\ndeclare const id: SomeId;\n');
    });
    it("nested code generation", () => {
        const someId = (0, src_1.conditionalOutput)("SomeId", (0, src_1.code) `type SomeId = string | number`, true);
        const add = (0, src_1.conditionalOutput)("add", (0, src_1.code) `const add(a: number, b: number) => a + b`);
        const childCode = (0, src_1.code) `declare const id: ${someId}`;
        const codeChunk = (0, src_1.code) `
            ${childCode}
            const result = ${add}(1, 2)
        `;
        expect(codeChunk.toString({ conditionalUtils: "./utils.ts" })).toBe('import { add, type SomeId } from "./utils.ts";\n\ndeclare const id: SomeId;\nconst result = add(1, 2);\n');
    });
    it("generate utils code", () => {
        const someType = (0, src_1.conditionalOutput)("SomeType", (0, src_1.code) `type SomeType = number`);
        const someId = (0, src_1.conditionalOutput)("SomeId", (0, src_1.code) `type SomeId = string | ${someType}`);
        const codeChunk = (0, src_1.code) `declare const id: ${someId}`;
        const utils = codeChunk.collectConditionalOutputs();
        const utilsCode = (0, src_1.generateConditionalsUtils)(utils);
        expect(utilsCode.toString()).toBe("type SomeType = number;\ntype SomeId = string | SomeType;\n");
    });
});
