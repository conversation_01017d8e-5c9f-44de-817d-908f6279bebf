"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.saveFiles = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const crypto_1 = require("crypto");
/**
 * Saves multiple {@link CodegenFile}s.
 */
async function saveFiles(opts) {
    const { toolName = "ts-poet", directory = "./", files, toStringOpts = {} } = opts;
    await Promise.all(files.map(async (file) => {
        const path = `${directory}/${file.name}`;
        // We might be writing to a subdirectory, so check this for each file
        await fs_1.promises.mkdir((0, path_1.dirname)(path), { recursive: true });
        const exists = await trueIfResolved(fs_1.promises.access(path));
        if (file.overwrite) {
            if (!file.hash) {
                // Just write the file w/o the hash hint (i.e. for JSON/etc)
                await fs_1.promises.writeFile(path, contentToString(file, toStringOpts));
            }
            else {
                // Create a hash of the unformatted option (to avoid the cost of formatting)
                const hash = sha1(contentToString(file, { ...toStringOpts, format: false }));
                if (exists) {
                    const existing = (await fs_1.promises.readFile(path)).toString();
                    const match = existing.match(/\(hash=([0-9a-z]+)\.([0-9]+)\)/);
                    // console.log({ existing: existing.length, hash: match?.[1], length: match?.[2] });
                    //
                    // If the pre-format hash matches _and_ our post-format file length has not changed,
                    // just skip formatting + outputting this file.
                    //
                    // We use the length check to catch programmer's making manual changes to the
                    // generated file that, b/c it is changing to the post-format text, won't get
                    // caught by our pre-format hash check.
                    if (match && match[1] === hash && String(existing.length) === match[2])
                        return;
                }
                // Now write the post-format output, but tagged with the pre-format hash
                const formatted = contentToString(file, toStringOpts);
                const prefix = `// Generated by ${toolName} (hash=${hash}.`;
                const suffix = `)\n` + formatted;
                const initialLength = prefix.length + suffix.length;
                const lengthLength = String(initialLength).length;
                // If initialLength is 998, and lengthLength is 3, then newLength will be 1001 and need 4 digits
                const pad = lengthLength !== String(initialLength + lengthLength).length ? 1 : 0;
                await fs_1.promises.writeFile(path, prefix + String(initialLength + lengthLength + pad) + suffix);
            }
        }
        else if (!exists) {
            await fs_1.promises.writeFile(path, contentToString(file, toStringOpts));
        }
    }));
}
exports.saveFiles = saveFiles;
function contentToString(file, toStringOpts) {
    var _a;
    if (typeof file.contents === "string") {
        return file.contents;
    }
    // Do a lazy/by-hand deep merge of toStringOpts and file.toStringOpts
    return file.contents.toString({
        path: file.name,
        ...toStringOpts,
        ...file.toStringOpts,
        dprintOptions: {
            ...toStringOpts.dprintOptions,
            ...(_a = file.toStringOpts) === null || _a === void 0 ? void 0 : _a.dprintOptions,
        },
    });
}
// We purposefully use sha1 for speed b/c this isn't for crypto/auth
function sha1(content) {
    const sum = (0, crypto_1.createHash)("sha1");
    sum.update(content);
    return sum.digest("hex").substring(0, 6);
}
/** Returns true if `p` is resolved, otherwise false if it is rejected. */
async function trueIfResolved(p) {
    return p.then(() => true, () => false);
}
