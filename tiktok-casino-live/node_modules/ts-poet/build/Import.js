"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sameModule = exports.maybeRelativePath = exports.emitImports = exports.SideEffect = exports.ImportsAll = exports.ImportsDefault = exports.ImportsName = exports.Imported = exports.Implicit = exports.Import = exports.importType = void 0;
const path = __importStar(require("path"));
const Node_1 = require("./Node");
const utils_1 = require("./utils");
const typeImportMarker = "(?:t:)?";
const modulePattern = `.+`;
const identPattern = `(?:(?:[a-zA-Z][_a-zA-Z0-9]*)|(?:[_a-zA-Z][_a-zA-Z0-9]+))`;
exports.importType = "[*@+=]";
const importPattern = `^(${typeImportMarker}${identPattern})(\\.${identPattern})?(${exports.importType})(${modulePattern})`;
// This is for doing `SourceSymbol:ImportSymbol`
const sourceIdentPattern = `(?:(?:${identPattern}:)?)`;
const sourceImportPattern = `^(${typeImportMarker}${sourceIdentPattern}${identPattern})(\\.${identPattern})?(@)(${modulePattern})`;
/**
 * Specifies a symbol and its related origin, either via import or implicit/local declaration.
 */
class Import extends Node_1.Node {
    /**
     * Parses a symbol reference pattern to create a symbol. The pattern
     * allows the simple definition of all symbol types including any possible
     * import variation. If the spec to parse does not follow the proper format
     * an implicit symbol is created from the unparsed spec.
     *
     * Pattern: `symbolName? importType modulePath`
     *
     * Where:
     *
     * - `symbolName` is any legal JS/TS symbol. If none, we use the last part of the module path as a guess.
     * - `importType` is one of `@` or `*` or `+`, where:
     *    - `@` is a named import
     *       - `Foo@bar` becomes `import { Foo } from 'bar'`
     *    - `*` is a star import,
     *       - `Foo*foo` becomes `import * as Foo from 'foo'`
     *    - `+` is an implicit import
     *       - E.g. `Foo+foo` becomes `import 'foo'`
     * - `modulePath` is a path
     *    - E.g. `<filename>(/<filename)*`
     *
     *
     * @param spec Symbol spec to parse.
     * @return Parsed symbol specification
     */
    static from(spec) {
        var _a;
        let matched = spec.match(importPattern);
        if (matched === null) {
            matched = spec.match(sourceImportPattern);
        }
        if (matched != null) {
            const modulePath = matched[4];
            const childSymbol = ((_a = matched[2]) === null || _a === void 0 ? void 0 : _a.substring(1)) || undefined; // I.e. `Transaction` in `Knex.Transaction
            const kind = matched[3] || "@";
            const symbolName = matched[1] || "";
            switch (kind) {
                case "*":
                    return Import.importsAll(symbolName, modulePath);
                case "@":
                    const isTypeImport = symbolName.startsWith("t:");
                    let exportedNames;
                    if (isTypeImport) {
                        exportedNames = symbolName.substring(2).split(":");
                    }
                    else {
                        exportedNames = symbolName.split(":");
                    }
                    const exportedName = exportedNames.pop();
                    const sourceExportedName = exportedNames[0];
                    return Import.importsName(exportedName, modulePath, isTypeImport, sourceExportedName, childSymbol);
                case "=":
                    return Import.importsDefault(symbolName, modulePath);
                case "+":
                    return Import.sideEffect(symbolName, modulePath);
                default:
                    throw new Error("Invalid import kind character");
            }
        }
        return Import.implicit(spec);
    }
    static fromMaybeString(spec) {
        return typeof spec === "string" ? Import.from(spec) : spec;
    }
    constructor(symbol) {
        super();
        this.symbol = symbol;
    }
    toCodeString() {
        return this.symbol;
    }
    get childNodes() {
        return [];
    }
    /**
     * Creates an import of all the modules exported symbols as a single
     * local named symbol
     *
     * e.g. `import * as Engine from 'templates';`
     *
     * @param localName The local name of the imported symbols
     * @param from The module to import the symbols from
     */
    static importsAll(localName, from) {
        return new ImportsAll(localName, from);
    }
    /**
     * Creates an import of a single named symbol from the module's exported
     * symbols.
     *
     * - e.g. `import { Engine } from 'templates';`
     * - e.g. `import { Foo as Bar } from 'templates';`
     *
     * @param exportedName The symbol that is both exported and imported
     * @param from The module the symbol is exported from
     * @param typeImport whether this is an `import type` import
     * @param sourceSymbol The symbol as exported from the module, i.e. if we're doing a `Foo as Bar` rename
     * @param childSymbol The additional `Transaction` symbol in a `Knex.Transaction` import
     */
    static importsName(exportedName, from, typeImport, sourceSymbol, childSymbol) {
        return new ImportsName(exportedName, from, sourceSymbol, typeImport, childSymbol);
    }
    /**
     * Creates a symbol that is brought in as a side effect of
     * an import.
     *
     * e.g. `import 'mocha'`
     *
     * @param symbolName The symbol to be imported
     * @param from The entire import that does the augmentation
     */
    static sideEffect(symbolName, from) {
        return new SideEffect(symbolName, from);
    }
    /**
     * An implied symbol that does no tracking of imports
     *
     * @param name The implicit symbol name
     */
    static implicit(name) {
        return new Implicit(name);
    }
    /**
     * Creates an import of a single named symbol from the module's exported
     * default.
     *
     * e.g. `import Engine from 'engine';`
     *
     * @param exportedName The symbol that is both exported and imported
     * @param from The module the symbol is exported from
     */
    static importsDefault(exportedName, from) {
        return new ImportsDefault(exportedName, from);
    }
}
exports.Import = Import;
/**
 * Non-imported symbol
 */
class Implicit extends Import {
    constructor(symbol) {
        super(symbol);
        this.source = undefined;
    }
}
exports.Implicit = Implicit;
/** Common base class for imported symbols. */
class Imported extends Import {
    /** The symbol is the imported symbol, i.e. `BarClass`, and source is the path it comes from. */
    constructor(symbol, source) {
        super(source);
        this.symbol = symbol;
        this.source = source;
    }
}
exports.Imported = Imported;
/**
 * Imports a single named symbol from the module's exported
 * symbols.
 *
 * E.g.:
 *
 * `import { Engine } from 'templates'` or
 * `import { Engine as Engine2 } from 'templates'`
 */
class ImportsName extends Imported {
    /**
     * @param symbol
     * @param source
     * @param sourceSymbol is the optional original symbol, i.e. if we're renaming the symbol it is `Engine`
     * @param typeImport whether this is an `import type` import
     * @param childSymbol the `Transaction` in a `Knex.Transaction`
     */
    constructor(symbol, source, sourceSymbol, typeImport, childSymbol) {
        super(symbol, source);
        this.sourceSymbol = sourceSymbol;
        this.typeImport = typeImport;
        this.childSymbol = childSymbol;
    }
    toImportPiece(skipTypeImport = false) {
        const maybeTypePrefix = this.typeImport && !skipTypeImport ? "type " : "";
        return maybeTypePrefix + (this.sourceSymbol ? `${this.sourceSymbol} as ${this.symbol}` : this.symbol);
    }
    toCodeString() {
        return this.childSymbol ? `${this.symbol}.${this.childSymbol}` : this.symbol;
    }
}
exports.ImportsName = ImportsName;
/**
 * Imports a single named symbol from the module's exported
 * default.
 *
 * e.g. `import Engine from 'engine';`
 */
class ImportsDefault extends Imported {
    constructor(symbol, source) {
        super(symbol, source);
    }
}
exports.ImportsDefault = ImportsDefault;
/**
 * Imports all of the modules exported symbols as a single
 * named symbol
 *
 * e.g. `import * as Engine from 'templates';`
 */
class ImportsAll extends Imported {
    constructor(symbol, source) {
        super(symbol, source);
    }
}
exports.ImportsAll = ImportsAll;
/**
 * A symbol that is brought in as a side effect of an import.
 *
 * E.g. `from("Foo+mocha")` will add `import 'mocha'`
 */
class SideEffect extends Imported {
    constructor(symbol, source) {
        super(symbol, source);
    }
}
exports.SideEffect = SideEffect;
/** Generates the `import ...` lines for the given `imports`. */
function emitImports(imports, ourModulePath, importMappings, forceRequireImports, importExtensions) {
    if (imports.length == 0) {
        return "";
    }
    let result = "";
    // Group the imports by source module they're imported from
    const importsByModule = (0, utils_1.groupBy)(imports.filter((it) => it.source !== undefined &&
        // Ignore imports that are in our own file
        !(it instanceof ImportsName && it.definedIn && sameModule(it.definedIn, ourModulePath))), (it) => it.source);
    // Output each source module as one line
    Object.entries(importsByModule).forEach(([modulePath, imports]) => {
        // Skip imports from the current module
        if (sameModule(ourModulePath, modulePath))
            return;
        if (modulePath in importMappings) {
            modulePath = importMappings[modulePath];
        }
        const importPath = maybeAdjustExtension(maybeRelativePath(ourModulePath, modulePath), importExtensions);
        // Output star imports individually
        unique(filterInstances(imports, ImportsAll).map((i) => i.symbol)).forEach((symbol) => {
            result += `import * as ${symbol} from '${importPath}';\n`;
        });
        // Partition named imported into `import type` vs. regular imports
        const symbolImports = filterInstancesAnd(imports, ImportsName, (it) => !it.typeImport);
        const typeImports = filterInstancesAnd(imports, ImportsName, (it) => {
            return !!it.typeImport && !symbolImports.some((s) => s.symbol === it.symbol);
        });
        // Use `import type { ... }` for module declaration files, see
        // https://github.com/stephenh/ts-proto/issues/1184#issuecomment-2910635357
        const useSingleTypeImport = typeImports.length > 0 && symbolImports.length === 0;
        const namedImports = useSingleTypeImport
            ? unique(typeImports.map((it) => it.toImportPiece(true)))
            : unique([...typeImports, ...symbolImports].map((it) => it.toImportPiece()));
        const defaultImports = unique(filterInstances(imports, ImportsDefault).map((it) => it.symbol));
        if (forceRequireImports.includes(modulePath) && defaultImports.length > 0) {
            result += `import ${defaultImports[0]} = require('${importPath}');\n`;
        }
        else if (namedImports.length > 0 || defaultImports.length > 0) {
            // Output named imports as a group
            const namesPart = namedImports.length > 0 ? [`{ ${namedImports.join(", ")} }`] : [];
            const defPart = defaultImports.length > 0 ? [defaultImports[0]] : [];
            result += `import${useSingleTypeImport ? " type" : ""} ${[...defPart, ...namesPart].join(", ")} from '${importPath}';\n`;
        }
    });
    const sideEffectImports = (0, utils_1.groupBy)(filterInstances(imports, SideEffect), (a) => a.source);
    Object.keys(sideEffectImports).forEach((it) => (result += `import '${it}';\n`));
    return result;
}
exports.emitImports = emitImports;
function filterInstances(list, t) {
    return list.filter((e) => e instanceof t);
}
function filterInstancesAnd(list, t, fn) {
    return list.filter((e) => e instanceof t && fn(e));
}
function unique(list) {
    return [...new Set(list)];
}
function maybeRelativePath(outputPath, importPath) {
    if (!importPath.startsWith("./")) {
        return importPath;
    }
    importPath = path.normalize(importPath);
    outputPath = path.normalize(outputPath);
    const outputPathDir = path.dirname(outputPath);
    if (outputPathDir === importPath) {
        // importing a file that is named the same thing as the directory the file doing the importing is in.
        // e.g. importing `./foo` from `./foo/index.ts`
        // the import statement should read `../foo` not `./`
        return ".." + path.sep + path.basename(importPath);
    }
    let relativePath = path.relative(outputPathDir, importPath).split(path.sep).join(path.posix.sep);
    if (!relativePath.startsWith(".")) {
        // ensure the js compiler recognizes this is a relative path.
        relativePath = "./" + relativePath;
    }
    return relativePath;
}
exports.maybeRelativePath = maybeRelativePath;
const tsRe = /\.ts$/;
const tsxRe = /\.tsx$/;
const jsRe = /\.js$/;
const jsxRe = /\.jsx$/;
function maybeAdjustExtension(path, importExtensions) {
    if (importExtensions === true) {
        return path;
    }
    else if (importExtensions === false) {
        return path.replace(extensionRegex, "");
    }
    else if (importExtensions === "js") {
        return path.replace(tsRe, ".js").replace(tsxRe, ".jsx");
    }
    else if (importExtensions === "ts") {
        return path.replace(jsRe, ".ts").replace(jsxRe, ".tsx");
    }
    else {
        throw new Error("Unsupported importExtensions value ${importExtensions}");
    }
}
const extensionRegex = /\.[tj]sx?/;
/** Checks if `path1 === path2` despite minor path differences like `./foo` and `foo`. */
function sameModule(path1, path2) {
    // TypeScript: import paths ending in .js and .ts are resolved to the .ts file.
    // Check the base paths (without the .js or .ts suffix).
    const [basePath1, basePath2] = [path1, path2].map((p) => p.replace(extensionRegex, ""));
    return basePath1 === basePath2 || path.resolve(basePath1) === path.resolve(basePath2);
}
exports.sameModule = sameModule;
