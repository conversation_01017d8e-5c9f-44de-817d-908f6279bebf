"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Def = exports.deepGenerate = exports.Code = void 0;
const Node_1 = require("./Node");
const Import_1 = require("./Import");
const is_plain_object_1 = require("./is-plain-object");
const ConditionalOutput_1 = require("./ConditionalOutput");
const index_1 = require("./index");
const dprint_node_1 = __importDefault(require("dprint-node"));
class Code extends Node_1.Node {
    constructor(literals, placeholders) {
        super();
        this.literals = literals;
        this.placeholders = placeholders;
        // Used by joinCode
        this.trim = false;
        this.oneline = false;
    }
    /** Returns the formatted code, with imports. */
    toString(opts = {}) {
        var _a;
        (_a = this.codeWithImports) !== null && _a !== void 0 ? _a : (this.codeWithImports = this.generateCodeWithImports(opts));
        return opts.format === false ? this.codeWithImports : maybePretty(this.codeWithImports, opts.dprintOptions);
    }
    asOneline() {
        this.oneline = true;
        return this;
    }
    get childNodes() {
        return this.placeholders;
    }
    /**
     * Returns the unformatted, import-less code.
     *
     * This is an internal API, see `toString` for the public API.
     */
    toCodeString(used) {
        var _a;
        return ((_a = this.code) !== null && _a !== void 0 ? _a : (this.code = this.generateCode(used)));
    }
    /**
     * Recursively collects all `ConditionalOutput` instances used within this `Code` block.
     */
    collectConditionalOutputs() {
        return this.placeholders
            .map((placeholder) => {
            if (placeholder instanceof ConditionalOutput_1.ConditionalOutput)
                return placeholder;
            if (placeholder instanceof Code)
                return placeholder.collectConditionalOutputs();
            return null;
        })
            .flat()
            .filter((val) => !!val);
    }
    deepFindAll(utilsUrl) {
        const used = [];
        const imports = [];
        const defs = [];
        const todo = [this];
        let i = 0;
        while (i < todo.length) {
            const placeholder = todo[i++];
            if (utilsUrl && placeholder instanceof ConditionalOutput_1.ConditionalOutput) {
                imports.push(Import_1.Import.importsName(placeholder.usageSiteName, utilsUrl, placeholder.isType));
                continue;
            }
            if (placeholder instanceof Node_1.Node) {
                todo.push(...placeholder.childNodes);
            }
            else if (Array.isArray(placeholder)) {
                todo.push(...placeholder);
            }
            if (placeholder instanceof ConditionalOutput_1.ConditionalOutput) {
                used.push(placeholder);
                todo.push(...placeholder.declarationSiteCode.childNodes);
            }
            else if (placeholder instanceof Import_1.Import) {
                imports.push(placeholder);
            }
            else if (placeholder instanceof Def) {
                defs.push(placeholder);
            }
            else if (placeholder instanceof ConditionalOutput_1.MaybeOutput) {
                if (used.includes(placeholder.parent)) {
                    todo.push(placeholder.code);
                }
            }
        }
        return [used, imports, defs];
    }
    deepReplaceNamedImports(forceDefaultImport, forceModuleImport) {
        // Keep a map of module name --> symbol we're importing, i.e. protobufjs/simple is _m1
        const assignedNames = {};
        function getName(source) {
            let name = assignedNames[source];
            if (!name) {
                name = `_m${Object.values(assignedNames).length}`;
                assignedNames[source] = name;
            }
            return name;
        }
        const todo = [this];
        let i = 0;
        while (i < todo.length) {
            const placeholder = todo[i++];
            if (placeholder instanceof Node_1.Node) {
                const array = placeholder.childNodes;
                for (let j = 0; j < array.length; j++) {
                    const maybeImp = array[j];
                    if (maybeImp instanceof Import_1.ImportsName && forceDefaultImport.includes(maybeImp.source)) {
                        const name = getName(maybeImp.source);
                        array[j] = (0, index_1.code) `${new Import_1.ImportsDefault(name, maybeImp.source)}.${maybeImp.sourceSymbol || maybeImp.symbol}`;
                    }
                    else if (maybeImp instanceof Import_1.ImportsName && forceModuleImport.includes(maybeImp.source)) {
                        const name = getName(maybeImp.source);
                        array[j] = (0, index_1.code) `${new Import_1.ImportsAll(name, maybeImp.source)}.${maybeImp.sourceSymbol || maybeImp.symbol}`;
                    }
                    else if (maybeImp instanceof Import_1.ImportsDefault && forceModuleImport.includes(maybeImp.source)) {
                        // Change `import DataLoader from "dataloader"` to `import * as DataLoader from "dataloader"`
                        array[j] = new Import_1.ImportsAll(maybeImp.symbol, maybeImp.source);
                    }
                }
                todo.push(...placeholder.childNodes);
            }
            else if (Array.isArray(placeholder)) {
                todo.push(...placeholder);
            }
        }
    }
    generateCode(used) {
        const { literals, placeholders } = this;
        let result = "";
        // interleave the literals with the placeholders
        for (let i = 0; i < placeholders.length; i++) {
            result += literals[i] + deepGenerate(used, placeholders[i]);
        }
        // add the last literal
        result += literals[literals.length - 1];
        if (this.trim) {
            result = result.trim();
        }
        if (this.oneline) {
            result = result.replace(/\n/g, "");
        }
        return result;
    }
    generateCodeWithImports(opts) {
        const { path = "", forceDefaultImport, forceModuleImport, forceRequireImport = [], importExtensions = true, prefix, importMappings = {}, } = opts || {};
        const ourModulePath = path.replace(/\.[tj]sx?/, "");
        if (forceDefaultImport || forceModuleImport) {
            this.deepReplaceNamedImports(forceDefaultImport || [], forceModuleImport || []);
        }
        const [used, imports, defs] = this.deepFindAll(opts.conditionalUtils);
        assignAliasesIfNeeded(defs, imports, ourModulePath);
        const importPart = (0, Import_1.emitImports)(imports, ourModulePath, importMappings, forceRequireImport, importExtensions);
        const bodyPart = this.generateCode(used);
        const maybePrefix = prefix ? `${prefix}\n` : "";
        return maybePrefix + importPart + "\n" + bodyPart;
    }
}
exports.Code = Code;
function deepGenerate(used, object) {
    let result = "";
    let todo = [object];
    let i = 0;
    while (i < todo.length) {
        const current = todo[i++];
        if (Array.isArray(current)) {
            todo.push(...current);
        }
        else if (current instanceof Node_1.Node) {
            result += current.toCodeString(used);
        }
        else if (current instanceof ConditionalOutput_1.MaybeOutput) {
            if (used.includes(current.parent)) {
                result += current.code.toCodeString(used);
            }
        }
        else if (current === null) {
            result += "null";
        }
        else if (current !== undefined) {
            if ((0, is_plain_object_1.isPlainObject)(current)) {
                result += JSON.stringify(current);
            }
            else {
                result += current.toString();
            }
        }
        else {
            result += "undefined";
        }
    }
    return result;
}
exports.deepGenerate = deepGenerate;
/** Finds any namespace collisions of a named import colliding with def and assigns the import an alias it. */
function assignAliasesIfNeeded(defs, imports, ourModulePath) {
    // Keep track of used (whether declared or imported) symbols
    const usedSymbols = new Set();
    // Mark all locally-defined symbols as used
    defs.forEach((def) => usedSymbols.add(def.symbol));
    // A mapping of original to assigned alias, i.e. Foo@foo --> Foo2
    const assignedAliases = {};
    let j = 1;
    imports.forEach((i) => {
        if (i instanceof Import_1.ImportsName &&
            // Don't both aliasing imports from our own module
            !((0, Import_1.sameModule)(i.source, ourModulePath) || (i.definedIn && (0, Import_1.sameModule)(i.definedIn, ourModulePath)))) {
            const key = `${i.symbol}@${i.source}`;
            if (usedSymbols.has(i.symbol)) {
                let alias = assignedAliases[key];
                if (!alias) {
                    alias = `${i.symbol}${j++}`;
                    assignedAliases[key] = alias;
                }
                // Move the original symbol over
                if (alias !== i.symbol) {
                    i.sourceSymbol = i.symbol;
                }
                i.symbol = alias;
            }
            else {
                usedSymbols.add(i.symbol);
                assignedAliases[key] = i.symbol;
            }
        }
    });
}
// This default options are both "prettier-ish" plus also suite the ts-poet pre-formatted
// output which is all bunched together, so we want to force braces / force new lines.
const baseOptions = {
    useTabs: false,
    useBraces: "always",
    singleBodyPosition: "nextLine",
    "arrowFunction.useParentheses": "force",
    // dprint-node uses `node: true`, which we want to undo
    "module.sortImportDeclarations": "caseSensitive",
    lineWidth: 120,
    // For some reason dprint seems to wrap lines "before it should" w/o this set (?)
    preferSingleLine: true,
};
function maybePretty(input, options) {
    try {
        return dprint_node_1.default.format("file.ts", input.trim(), { ...baseOptions, ...options });
    }
    catch (e) {
        return input; // assume it's invalid syntax and ignore
    }
}
/**
 * Represents a symbol defined in the current file.
 *
 * We use this to know if a symbol imported from a different file is going to
 * have a namespace collision.
 */
class Def extends Node_1.Node {
    constructor(symbol) {
        super();
        this.symbol = symbol;
    }
    toCodeString() {
        return this.symbol;
    }
    /** Any potentially string/SymbolSpec/Code nested nodes within us. */
    get childNodes() {
        return [];
    }
}
exports.Def = Def;
