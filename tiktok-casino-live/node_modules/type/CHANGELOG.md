# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [2.7.3](https://github.com/medikoo/type/compare/v2.7.2...v2.7.3) (2024-05-30)

### Maintenance Improvements

- Add security vulnerability policy ([a28cc58](https://github.com/medikoo/type/commit/a28cc586882ee8b1f0af6789b5af927cc4f14227))
- Prettify ([aaa9256](https://github.com/medikoo/type/commit/aaa92561a812d756ece952e1afade7d4aa0f116c))
- Upgrade `prettier-elastic` to v3 ([7f10b2c](https://github.com/medikoo/type/commit/7f10b2c995bb590903b2d3d2c1eacfd7e045d73b))

### [2.7.2](https://github.com/medikoo/type/compare/v2.7.1...v2.7.2) (2022-08-05)

### Maintenance Improvements

- **TS:** Improve `ensure` options handling ([#8](https://github.com/medikoo/type/issues/8)) ([4a54066](https://github.com/medikoo/type/commit/4a54066d7b55cef14ac4aa25a6f070296a043a6f)) ([Marco](https://github.com/borracciaBlu))

### [2.7.1](https://github.com/medikoo/type/compare/v2.7.0...v2.7.1) (2022-08-04)

### Maintenance Improvements

- **TS:** Fix support for `isOptional` in `ensure` options ([#7](https://github.com/medikoo/type/issues/7)) ([320f89b](https://github.com/medikoo/type/commit/320f89b89237e3e0ceff5e26b67cb18bd52cb42d)) ([Marco](https://github.com/borracciaBlu))

## [2.7.0](https://github.com/medikoo/type/compare/v2.6.1...v2.7.0) (2022-08-03)

### Features

- `BigInt.coerce` and `BigInt.ensure` ([e49ad78](https://github.com/medikoo/type/commit/e49ad787bd3aa67b7aa9f7a8ea4cde22a08bebc5))

### [2.6.1](https://github.com/medikoo/type/compare/v2.6.0...v2.6.1) (2022-07-29)

### Maintenance Improvements

- Declare TS types ([#6](https://github.com/medikoo/type/issues/6)) ([6378e2c](https://github.com/medikoo/type/commit/6378e2c457670bcb8a9b898e0f2502ed5b942d44)) ([Marco](https://github.com/borracciaBlu))

## [2.6.0](https://github.com/medikoo/type/compare/v2.5.0...v2.6.0) (2022-02-02)

### Features

- `constructor` validation utils ([74b99bb](https://github.com/medikoo/type/commit/74b99bbf6be27083bf9a053961edb2a585ae3e77))

## [2.5.0](https://github.com/medikoo/type/compare/v2.4.0...v2.5.0) (2021-03-08)

### Features

- `errorCode` option for `ensure*` utils ([777a1f2](https://github.com/medikoo/type/commit/777a1f2c9fd76defcd24d3a30cce49491947fef7))

## [2.4.0](https://github.com/medikoo/type/compare/v2.3.0...v2.4.0) (2021-03-08)

### Features

- `set/is` and `set/ensure` utils ([083ec23](https://github.com/medikoo/type/commit/083ec2351718c310f316dcfd8c624a13201e227f))

## [2.3.0](https://github.com/medikoo/type/compare/v2.2.0...v2.3.0) (2021-02-16)

### Features

- `map/is` and `map/ensure` utils ([aafd1cb](https://github.com/medikoo/type/commit/aafd1cbd8c888fda98d39fd17e59f38b078d7bcf))

## [2.2.0](https://github.com/medikoo/type/compare/v2.1.0...v2.2.0) (2021-02-11)

### Features

- Support `ensureItem` option in `array/ensure` ([8f74973](https://github.com/medikoo/type/commit/8f749739df9bfebf44087093e09c8f7341a33a09))

## [2.1.0](https://github.com/medikoo/type/compare/v2.0.0...v2.1.0) (2020-08-21)

### Features

- `ensure` util for cumulated input validation ([814c5a8](https://github.com/medikoo/type/commit/814c5a801ecac23d06d8a5f4bcafc4763a04408c))
- Provide an alternative error message with `options.name` ([c7751c0](https://github.com/medikoo/type/commit/c7751c084ee4f3d3ed10500db0edde2ff00e03a1))
- Support `%n` (meaningful name) token in error message resolver ([b0f374e](https://github.com/medikoo/type/commit/b0f374e54345c714fe37a90887ecfe60577ce133))
- Support `min` validation for natural numbers ([e703512](https://github.com/medikoo/type/commit/e70351248818d3e113110106ad174b42c5fd9b25))
- Support custom Error constructors ([c6ecb90](https://github.com/medikoo/type/commit/c6ecb90e21c1c778210934204cbe393fb89ef2f6))

### Bug Fixes

- Fix typo in error message ([2735533](https://github.com/medikoo/type/commit/2735533de28d33dfa13222743698169c92d08c09))

## [2.0.0](https://github.com/medikoo/type/compare/v1.2.0...v2.0.0) (2019-10-10)

### Features

- `allowedKeys` option for plain-object/ensure ([f81e72e](https://github.com/medikoo/type/commit/f81e72e))
- `ensurePropertyValue` option for plain-object/ensure ([c5ff8fb](https://github.com/medikoo/type/commit/c5ff8fb))
- Replace `coerceItem` with `ensureItem` option in iterable/ensure ([721494f](https://github.com/medikoo/type/commit/721494f))
- Seclude lib/resolve-error-message ([12636d9](https://github.com/medikoo/type/commit/12636d9))
- Validate options.ensureItem in iterable/ensure ([78da6c1](https://github.com/medikoo/type/commit/78da6c1))

### BREAKING CHANGES

- iterable/ensure no longer supports `coerceItem` option. Instead `ensureItem` was introduced

## [1.2.0](https://github.com/medikoo/type/compare/v1.1.0...v1.2.0) (2019-09-20)

### Bug Fixes

- Improve error message so it's not confusing ([97cd6b9](https://github.com/medikoo/type/commit/97cd6b9))

### Features

- 'coerceItem' option for iterable/ensure ([0818860](https://github.com/medikoo/type/commit/0818860))

## [1.1.0](https://github.com/medikoo/type/compare/v1.0.3...v1.1.0) (2019-09-20)

### Features

- `denyEmpty` option for iterables validation ([301d071](https://github.com/medikoo/type/commit/301d071))

### [1.0.3](https://github.com/medikoo/type/compare/v1.0.2...v1.0.3) (2019-08-06)

### Bug Fixes

- Recognize custom built ES5 era errors ([6462fac](https://github.com/medikoo/type/commit/6462fac))

### [1.0.2](https://github.com/medikoo/type/compare/v1.0.1...v1.0.2) (2019-08-06)

### Bug Fixes

- Recognize host errors (e.g. DOMException) ([96ef399](https://github.com/medikoo/type/commit/96ef399))

## [1.0.1](https://github.com/medikoo/type/compare/v1.0.0...v1.0.1) (2019-04-08)

# 1.0.0 (2019-04-05)

### Bug Fixes

- ensure 'is' functions can't crash ([59ceb78](https://github.com/medikoo/type/commit/59ceb78))

### Features

- array-length/coerce ([af8ddec](https://github.com/medikoo/type/commit/af8ddec))
- array-length/ensure ([d313eb6](https://github.com/medikoo/type/commit/d313eb6))
- array-like/ensure ([45f1ddd](https://github.com/medikoo/type/commit/45f1ddd))
- array-like/is ([9a026a5](https://github.com/medikoo/type/commit/9a026a5))
- array/ensure ([9db1515](https://github.com/medikoo/type/commit/9db1515))
- array/is ([9672839](https://github.com/medikoo/type/commit/9672839))
- date/ensure ([44e25a0](https://github.com/medikoo/type/commit/44e25a0))
- date/is ([0316558](https://github.com/medikoo/type/commit/0316558))
- ensure to not crash ([3998348](https://github.com/medikoo/type/commit/3998348))
- ensure/number ([134b5cb](https://github.com/medikoo/type/commit/134b5cb))
- error/ensure ([d5c8a30](https://github.com/medikoo/type/commit/d5c8a30))
- error/is-error ([4d6b899](https://github.com/medikoo/type/commit/4d6b899))
- finite/coerce ([accaad1](https://github.com/medikoo/type/commit/accaad1))
- finite/ensure ([51e4174](https://github.com/medikoo/type/commit/51e4174))
- function/ensure ([b624c9a](https://github.com/medikoo/type/commit/b624c9a))
- function/is ([dab8026](https://github.com/medikoo/type/commit/dab8026))
- integer/coerce ([89dea2e](https://github.com/medikoo/type/commit/89dea2e))
- integer/ensure ([44a7071](https://github.com/medikoo/type/commit/44a7071))
- iterable/ensure ([3d48841](https://github.com/medikoo/type/commit/3d48841))
- iterable/is ([cf09513](https://github.com/medikoo/type/commit/cf09513))
- lib/is-to-string-tag-supported ([c8c001d](https://github.com/medikoo/type/commit/c8c001d))
- natural-number/coerce ([d08fdd9](https://github.com/medikoo/type/commit/d08fdd9))
- natural-number/ensure ([6c24d12](https://github.com/medikoo/type/commit/6c24d12))
- number/coerce ([86ccf08](https://github.com/medikoo/type/commit/86ccf08))
- object/ensure ([a9e8eed](https://github.com/medikoo/type/commit/a9e8eed))
- object/is ([d2d7251](https://github.com/medikoo/type/commit/d2d7251))
- plain-function/ensure ([5186518](https://github.com/medikoo/type/commit/5186518))
- plain-function/is ([51bc791](https://github.com/medikoo/type/commit/51bc791))
- plain-object/ensure ([91cf5e5](https://github.com/medikoo/type/commit/91cf5e5))
- plain-object/is ([4dcf393](https://github.com/medikoo/type/commit/4dcf393))
- promise/ensure ([8d096a4](https://github.com/medikoo/type/commit/8d096a4))
- promise/is ([a00de02](https://github.com/medikoo/type/commit/a00de02))
- prototype/is ([b23bdcc](https://github.com/medikoo/type/commit/b23bdcc))
- reg-exp/ensure ([6f7bbcb](https://github.com/medikoo/type/commit/6f7bbcb))
- reg-exp/is ([9728519](https://github.com/medikoo/type/commit/9728519))
- safe-integer/coerce ([b8549c4](https://github.com/medikoo/type/commit/b8549c4))
- safe-integer/ensure ([a70ef3f](https://github.com/medikoo/type/commit/a70ef3f))
- string/coerce ([b25c71f](https://github.com/medikoo/type/commit/b25c71f))
- string/ensure ([b62577d](https://github.com/medikoo/type/commit/b62577d))
- support 'default' in resolveException ([e08332a](https://github.com/medikoo/type/commit/e08332a))
- switch config to ES3 based ([37606d9](https://github.com/medikoo/type/commit/37606d9))
- thenable/ensure ([6762c0d](https://github.com/medikoo/type/commit/6762c0d))
- thenable/is ([2711d70](https://github.com/medikoo/type/commit/2711d70))
- time-value/coerce ([27fd109](https://github.com/medikoo/type/commit/27fd109))
- time-value/ensure ([1f6a8ea](https://github.com/medikoo/type/commit/1f6a8ea))
- **string/coerce:** restrict toString acceptance ([2a87100](https://github.com/medikoo/type/commit/2a87100))
- value/ensure ([dd6d8cb](https://github.com/medikoo/type/commit/dd6d8cb))
- value/is ([fdf4763](https://github.com/medikoo/type/commit/fdf4763))
