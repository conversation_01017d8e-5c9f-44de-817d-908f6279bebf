"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignConfig = exports.Screen = exports.Location = exports.Device = exports.Devices = exports.UserAgents = exports.Screens = exports.Locations = void 0;
const utilities_1 = require("../lib/utilities");
const version_1 = require("../version");
exports.Locations = [
    {
        'lang_country': 'en-GB',
        'lang': 'en',
        'country': 'GB',
        'tz_name': 'Europe/London'
    },
    {
        'lang_country': 'en-CA',
        'lang': 'en',
        'country': 'CA',
        'tz_name': 'America/Toronto'
    },
    {
        'lang_country': 'en-AU',
        'lang': 'en',
        'country': 'AU',
        'tz_name': 'Australia/Sydney'
    },
    {
        'lang_country': 'en-NZ',
        'lang': 'en',
        'country': 'NZ',
        'tz_name': 'Pacific/Auckland'
    },
    {
        'lang_country': 'en-ZA',
        'lang': 'en',
        'country': 'ZA',
        'tz_name': 'Africa/Johannesburg'
    },
    {
        'lang_country': 'en-IE',
        'lang': 'en',
        'country': 'IE',
        'tz_name': 'Europe/Dublin'
    },
    {
        'lang_country': 'en-JM',
        'lang': 'en',
        'country': 'JM',
        'tz_name': 'America/Jamaica'
    },
    {
        'lang_country': 'en-BZ',
        'lang': 'en',
        'country': 'BZ',
        'tz_name': 'America/Belize'
    },
    {
        'lang_country': 'en-TT',
        'lang': 'en',
        'country': 'TT',
        'tz_name': 'America/Port_of_Spain'
    }
];
exports.Screens = [
    {
        'screen_width': 1920,
        'screen_height': 1080
    },
    {
        'screen_width': 2560,
        'screen_height': 1440
    },
    {
        'screen_width': 3840,
        'screen_height': 2160
    },
    {
        'screen_width': 4096,
        'screen_height': 2160
    },
    {
        'screen_width': 5120,
        'screen_height': 2880
    },
    {
        'screen_width': 7680,
        'screen_height': 4320
    },
    {
        'screen_width': 1152,
        'screen_height': 2048
    },
    {
        'screen_width': 1440,
        'screen_height': 2560
    },
    {
        'screen_width': 2160,
        'screen_height': 3840
    },
    {
        'screen_width': 4320,
        'screen_height': 7680
    }
];
exports.UserAgents = [
    // Latest Chrome UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/chrome
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',
    // Latest Safari UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/safari
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.15',
    // Latest Firefox UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/firefox
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:130.0) Gecko/20100101 Firefox/130.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 14.7; rv:130.0) Gecko/20100101 Firefox/130.0',
    // Latest Edge UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/edge
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/128.0.2739.79',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/128.0.2739.79',
    // Latest Opera UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/opera
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 OPR/113.0.0.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 OPR/113.0.0.0'
];
exports.Devices = exports.UserAgents.map((userAgent) => (0, utilities_1.userAgentToDevicePreset)(userAgent));
// Pick a device
exports.Device = (process.env.RANDOMIZE_TIKTOK_DEVICE?.toLowerCase() === 'true') ? exports.Devices[Math.floor(Math.random() * exports.Devices.length)] : exports.Devices[6];
// Pick a location
exports.Location = (process.env.RANDOMIZE_TIKTOK_LOCATION?.toLowerCase() === 'true') ? exports.Locations[Math.floor(Math.random() * exports.Locations.length)] : {
    lang_country: 'en-US',
    lang: 'en',
    country: 'GB',
    tz_name: 'Europe/Berlin'
};
exports.Screen = (process.env.RANDOMIZE_TIKTOK_SCREEN?.toLowerCase() === 'true') ? exports.Screens[Math.floor(Math.random() * exports.Screens.length)] : {
    screen_width: 1920,
    screen_height: 1080
};
const Config = {
    TIKTOK_HOST_WEB: 'www.tiktok.com',
    TIKTOK_HOST_WEBCAST: 'webcast.tiktok.com',
    TIKTOK_HTTP_ORIGIN: 'https://www.tiktok.com',
    DEFAULT_HTTP_CLIENT_COOKIES: { 'tt-target-idc': 'useast1a' },
    DEFAULT_HTTP_CLIENT_OPTIONS: {},
    DEFAULT_HTTP_CLIENT_PARAMS: {
        'aid': (1988).toString(),
        'app_language': exports.Location['lang'],
        'app_name': 'tiktok_web',
        'browser_language': exports.Location['lang_country'],
        'browser_name': exports.Device['browser_name'],
        'browser_online': 'true',
        'browser_platform': exports.Device['browser_platform'],
        'browser_version': exports.Device['browser_version'],
        'cookie_enabled': 'true',
        'device_platform': 'web_pc',
        'focus_state': 'true',
        'from_page': 'user',
        'history_len': '10',
        'is_fullscreen': 'false',
        'is_page_visible': 'true',
        'screen_height': exports.Screen['screen_height'].toString(),
        'screen_width': exports.Screen['screen_width'].toString(),
        'tz_name': exports.Location['tz_name'],
        'referer': 'https://www.tiktok.com/',
        'root_referer': 'https://www.tiktok.com/',
        'channel': 'tiktok_web',
        'data_collection_enabled': 'true',
        'os': exports.Device['os'],
        'priority_region': exports.Location['country'],
        'region': exports.Location['country'],
        'user_is_login': 'true',
        'webcast_language': exports.Location['lang'],
        'device_id': (0, utilities_1.generateDeviceId)()
    },
    DEFAULT_WS_CLIENT_PARAMS: {
        'version_code': '180800',
        'aid': (1988).toString(),
        'app_language': exports.Location['lang'],
        'app_name': 'tiktok_web',
        'browser_platform': exports.Device['browser_platform'],
        'browser_language': exports.Location['lang_country'],
        'browser_name': exports.Device['browser_name'],
        'browser_version': exports.Device['browser_version'],
        'browser_online': 'true',
        'cookie_enabled': 'true',
        'tz_name': exports.Location['tz_name'],
        'device_platform': 'web',
        'debug': 'false',
        'host': 'https://webcast.tiktok.com',
        'identity': 'audience',
        'live_id': '12',
        'sup_ws_ds_opt': '1',
        'update_version_code': '2.0.0',
        'did_rule': '3',
        'screen_height': (exports.Screen['screen_height']).toString(),
        'screen_width': (exports.Screen['screen_width']).toString(),
        'heartbeat_duration': '0',
        'resp_content_type': 'protobuf',
        'history_comment_count': '6',
        // We think last_rtt means "last round trip time" in millis.
        'last_rtt': (Math.floor(Math.random() * 100) + 100).toString(),
    },
    DEFAULT_WS_CLIENT_PARAMS_APPEND_PARAMETER: "&version_code=270000",
    DEFAULT_HTTP_CLIENT_HEADERS: {
        'Connection': 'keep-alive',
        'Cache-Control': 'max-age=0',
        'User-Agent': exports.Device['user_agent'],
        'Accept': 'text/html,application/json,application/protobuf',
        'Referer': 'https://www.tiktok.com/',
        'Origin': 'https://www.tiktok.com',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate',
        'Sec-Fetch-Site': 'same-site',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Ua-Mobile': '?0'
    },
    DEFAULT_WS_CLIENT_HEADERS: {
        'User-Agent': exports.Device['user_agent'],
    }
};
exports.SignConfig = {
    basePath: process.env.SIGN_API_URL || 'https://tiktok.eulerstream.com',
    apiKey: process.env.SIGN_API_KEY,
    baseOptions: {
        headers: { 'User-Agent': `tiktok-live-connector/${version_1.VERSION} ${process.platform}` },
        validateStatus: () => true
    }
};
exports.default = Config;
//# sourceMappingURL=config.js.map