{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../src/lib/client.ts"], "names": [], "mappings": ";;;;;;AAAA,2CAOwB;AAGxB,6CAA2C;AAC3C,yDAA8F;AAC9F,uEAAoD;AACpD,0DAAkC;AAElC,+CAA+D;AAC/D,mCAA8D;AAE9D,2CAOwB;AAIxB,MAAa,oBAAqB,SAAS,0BAAsD;IAmCzE;IAEA;IAnCpB,oBAAoB;IACb,SAAS,CAAkB;IAC3B,QAAQ,GAA0B,IAAI,CAAC;IAE9C,uBAAuB;IACb,SAAS,GAAoB,IAAI,CAAC;IAClC,eAAe,GAA4B,IAAI,CAAC;IAChD,aAAa,GAAiB,qBAAY,CAAC,YAAY,CAAC;IAClD,OAAO,CAA8B;IAErD;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,YACoB,QAAgB,EAChC,OAA8C,EAC9B,MAAoB;QAEpC,KAAK,EAAE,CAAC;QAJQ,aAAQ,GAAR,QAAQ,CAAQ;QAEhB,WAAM,GAAN,MAAM,CAAc;QAGpC,IAAI,CAAC,QAAQ,GAAG,IAAA,wCAA4B,EAAC,QAAQ,CAAC,CAAC;QAEvD,qBAAqB;QACrB,IAAI,CAAC,OAAO,GAAG;YACX,iBAAiB,EAAE,EAAE;YACrB,mBAAmB,EAAE,KAAK;YAC1B,kBAAkB,EAAE,IAAI;YACxB,sBAAsB,EAAE,KAAK;YAC7B,sBAAsB,EAAE,KAAK;YAC7B,oBAAoB,EAAE,IAAI;YAC1B,wBAAwB,EAAE,IAAI;YAC9B,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,IAAI;YAEhB,8BAA8B;YAC9B,eAAe,EAAE,EAAE;YACnB,gBAAgB,EAAE,EAAE;YACpB,gBAAgB,EAAE,EAAE;YAEpB,4BAA4B;YAC5B,eAAe,EAAE,EAAE;YACnB,eAAe,EAAE,EAAE;YACnB,cAAc,EAAE,EAAE;YAElB,cAAc,EAAE,KAAK;YACrB,uBAAuB,EAAE,SAAS;YAClC,sBAAsB,EAAE,KAAK;YAC7B,GAAG,OAAO;SACb,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAe,CAChC;YACI,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,gBAAgB,IAAI,EAAE;YACnD,YAAY,EAAE,IAAI,CAAC,OAAO,EAAE,gBAAgB;YAC5C,YAAY,EAAE,IAAI,CAAC,OAAO,EAAE,eAAe,IAAI,EAAE;YACjD,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,cAAc,IAAI,KAAK;YACrD,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,SAAS;SACpD,EACD,MAAM,CACT,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;QAC7D,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACO,eAAe;QACrB,IAAI,CAAC,aAAa,GAAG,qBAAY,CAAC,YAAY,CAAC;QAC/C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,8BAA8B;QAC9B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC;QAC9B,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,EAAE,CAAC;QAC/B,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,IAAW,cAAc;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,KAAK,qBAAY,CAAC,UAAU,CAAC;IAC1D,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,aAAa,KAAK,qBAAY,CAAC,SAAS,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IACjC,CAAC;IAGD;;;OAGG;IACH,IAAW,KAAK;QACZ,OAAO;YACH,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,cAAc,EAAE,IAAI,CAAC,cAAc;SACtC,CAAC;IACN,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,OAAO,CAAC,MAAe;QAEzB,QAAQ,IAAI,CAAC,aAAa,EAAE;YACxB,KAAK,qBAAY,CAAC,SAAS;gBACvB,MAAM,IAAI,8BAAqB,CAAC,oBAAoB,CAAC,CAAC;YAE1D,KAAK,qBAAY,CAAC,UAAU;gBACxB,MAAM,IAAI,+BAAsB,CAAC,qBAAqB,CAAC,CAAC;YAE5D,QAAQ;YACR,KAAK,qBAAY,CAAC,YAAY;gBAC1B,IAAI;oBACA,IAAI,CAAC,aAAa,GAAG,qBAAY,CAAC,UAAU,CAAC;oBAC7C,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;oBAC5B,IAAI,CAAC,aAAa,GAAG,qBAAY,CAAC,SAAS,CAAC;oBAC5C,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC9C,OAAO,IAAI,CAAC,KAAK,CAAC;iBACrB;gBAAC,OAAO,GAAG,EAAE;oBACV,IAAI,CAAC,aAAa,GAAG,qBAAY,CAAC,YAAY,CAAC;oBAC/C,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;oBAChD,MAAM,GAAG,CAAC;iBACb;SACR;IACL,CAAC;IAED;;;;;OAKG;IACO,KAAK,CAAC,QAAQ,CAAC,MAAe;QAEpC,2BAA2B;QAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB,EAAE;YACjH,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;SAC/F;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,OAAO,EAAE,sBAAsB,EAAE;YACtC,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAE5C,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,MAAM,IAAI,yBAAgB,CAAC,gBAAgB,CAAC,CAAC;aAChD;SAEJ;QAED,6BAA6B;QAC7B,IAAI,IAAI,CAAC,OAAO,EAAE,sBAAsB,EAAE;YACtC,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC3D;QAED,0HAA0H;QAC1H,MAAM,eAAe,GAAoB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,IAAI,CAAC,SAAS,CAAC,6BAA6B,CAAC,CACjI;YACI,MAAM,EAAE,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YAC/E,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;YACtE,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACjD,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;SAC9E,CACJ,CAAC;QAEF,sCAAsC;QACtC,IAAI,IAAI,CAAC,OAAO,EAAE,kBAAkB,EAAE;YAClC,MAAM,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;SACtD;QAED,gCAAgC;QAChC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;YACzB,MAAM,IAAI,6BAAoB,CAAC,2CAA2C,CAAC,CAAC;SAC/E;QAED,2BAA2B;QAC3B,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;QAClD,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,eAAe,CAAC,WAAW,CAAC;QAE7D,2BAA2B;QAC3B,MAAM,QAAQ,GAA2B;YACrC,QAAQ,EAAE,MAAM;YAChB,OAAO,EAAE,IAAI,CAAC,MAAM;YACpB,YAAY,EAAE,eAAe,CAAC,WAAW;YACzC,MAAM,EAAE,eAAe,CAAC,MAAM;SACjC,CAAC;QAEF,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QACtF,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC3E,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,mBAAmB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAE/D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;SAChC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,WAAW,CAAC,QAAiB;QACtC,IAAI,MAAM,GAAU,EAAE,CAAC;QAEvB,QAAQ,KAAK,IAAI,CAAC,QAAQ,CAAC;QAE3B,WAAW;QACX,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;YACpF,MAAM,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC;YACzD,IAAI,CAAC,MAAM;gBAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACpE,OAAO,MAAM,CAAC;SACjB;QAAC,OAAO,EAAE,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,OAAO,CAAC,KAAK,CAAC,yEAAyE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;YAC1I,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACnB;QAED,0BAA0B;QAC1B,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;YACvF,MAAM,MAAM,GAAG,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;YAC5C,IAAI,CAAC,MAAM;gBAAE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACnE,OAAO,MAAM,CAAC;SACjB;QAAC,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,OAAO,CAAC,KAAK,CAAC,4EAA4E,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;YAC9I,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACpB;QAED,4BAA4B;QAC5B,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;YACnF,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,yDAAyD,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/G,IAAI,CAAC,QAAQ,CAAC,OAAO;gBAAE,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;YAC/E,OAAO,QAAQ,CAAC,OAAO,CAAC;SAC3B;QAAC,OAAO,GAAG,EAAE;YACV,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjB,MAAM,IAAI,2BAAkB,CAAC,MAAM,EAAE,gDAAgD,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;SACvG;IAEL,CAAC;IAEM,KAAK,CAAC,WAAW;QACpB,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,MAAM,QAAQ,GAAG,CAAC,MAAc,EAAE,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC;QAElD,kBAAkB;QAClB,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACzF,IAAI,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,KAAK,SAAS;gBAAE,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACvH,OAAO,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;SACjE;QAAC,OAAO,EAAE,EAAE;YACT,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,OAAO,CAAC,KAAK,CAAC,4EAA4E,EAAE,EAAE,CAAC,CAAC;YACvI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACnB;QAED,iBAAiB;QACjB,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC5F,IAAI,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,SAAS;gBAAE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC1G,OAAO,QAAQ,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;SACrD;QAAC,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,OAAO,CAAC,KAAK,CAAC,+EAA+E,EAAE,GAAG,CAAC,CAAC;YAC3I,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACpB;QAED,mBAAmB;QACnB,IAAI;YACA,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACxF,IAAI,QAAQ,CAAC,IAAI,KAAK,GAAG;gBAAE,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACnF,OAAO,QAAQ,CAAC,OAAO,CAAC;SAC3B;QAAC,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,OAAO,CAAC,sBAAsB,IAAI,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,GAAG,CAAC,CAAC;YAC/G,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjB,MAAM,IAAI,yBAAgB,CAAC,MAAM,EAAE,gDAAgD,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;SACrG;IAEL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE;QAC3C,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAEhC,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACjC,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;gBAC3B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;gBAExC,IAAI,MAAM,EAAE;oBACR,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO,EAAE,CAAC;iBACb;YACL,CAAC,CAAC;YAEF,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,OAAO,GAAG,IAAI,CAAC,CAAC;YACxE,MAAM,WAAW,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IAEP,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,aAAa;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;YAAE,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACrD,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;QACtD,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,mBAAmB;QAC5B,IAAI;YACA,IAAI,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,2BAA2B,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACjG,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;SAC9B;QAAC,OAAO,GAAG,EAAE;YACV,MAAM,IAAI,6BAAoB,CAAC,oCAAoC,GAAG,CAAC,OAAO,EAAE,EAAE,GAAG,CAAC,CAAC;SAC1F;IACL,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,OAA2D;QAEjG,MAAM,MAAM,GAAG,OAAO,EAAE,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;SAC7D;QAED,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC;QAC3E,IAAI,CAAC,SAAS,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;SAChE;QAED,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CACvC;YACI,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,SAAS;SACvB,CACJ,CAAC;IACN,CAAC;IAED;;;;;;;OAOG;IACO,KAAK,CAAC,cAAc,CAAC,KAAa,EAAE,QAAgC;QAC1E,OAAO,IAAI,OAAO,CAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAEnD,yBAAyB;YACzB,MAAM,QAAQ,GAAG,IAAI,mBAAc,CAC/B,KAAK,EACL,IAAI,CAAC,SAAS,CAAC,SAAS,EACxB,EAAE,GAAG,gBAAM,CAAC,wBAAwB,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,QAAQ,EAAE,EACnF,EAAE,GAAG,gBAAM,CAAC,yBAAyB,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,eAAe,EAAE,EACzE,IAAI,CAAC,OAAO,EAAE,eAAe,CAChC,CAAC;YAEF,wBAAwB;YACxB,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE;gBAC1B,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC7B,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAM,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC;gBACnE,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;oBAChB,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,YAAY,CAAC,CAAC;gBACzC,CAAC,CAAC,CAAC;gBACH,OAAO,CAAC,QAAQ,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,GAAQ,EAAE,EAAE,CAAC,MAAM,CAAC,gCAAgC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC1F,QAAQ,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,GAAoB,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC;YAC3F,QAAQ,CAAC,EAAE,CAAC,uBAAuB,EAAE,CAAC,GAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,mCAAmC,CAAC,CAAC,CAAC;YAC/G,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,0BAA0B,CAAC,EAAE,KAAM,CAAC,CAAC;QACxF,CAAC,CAAC,CAAC;IACP,CAAC;IAES,KAAK,CAAC,sBAAsB,CAAC,eAAgC;QAEnE,gBAAgB;QAChB,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAC7B,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAC7E,CAAC;QAEF,8DAA8D;QAC9D,KAAK,IAAI,OAAO,IAAI,eAAe,CAAC,QAAQ,EAAE;YAC1C,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,EAAS,CAAC;YAErD,4BAA4B;YAC5B,IAAI,CAAC,IAAI,CACL,qBAAY,CAAC,YAAY,EACzB,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,WAAW,IAAI,EAAE,EACzB,OAAO,CAAC,MAAM,CACjB,CAAC;YAEF,iCAAiC;YACjC,MAAM,UAAU,GAAG,wBAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjD,IAAI,UAAU,EAAE;gBACZ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;gBACnC,OAAO;aACV;YAED,uBAAuB;YACvB,QAAQ,OAAO,CAAC,IAAI,EAAE;gBAClB,KAAK,uBAAuB;oBACxB,MAAM,cAAc,GAAG,WAAoC,CAAC;oBAC5D,IAAI,cAAc,CAAC,MAAM,KAAK,6BAAa,CAAC,+BAA+B,IAAI,cAAc,CAAC,MAAM,KAAK,6BAAa,CAAC,2BAA2B,EAAE;wBAChJ,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC;wBACtE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;qBAC3B;oBACD,MAAM;gBACV,KAAK,oBAAoB;oBACrB,2CAA2C;oBAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,WAAW,CAAC,MAAM,EAAE;wBAC1D,WAAW,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC;qBAC/F;oBACD,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;oBAC1C,MAAM;gBACV,KAAK,sBAAsB;oBACvB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;oBAC5C,IAAI,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE;wBAC7C,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;qBAC/C;oBACD,IAAI,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;wBAC5C,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;qBAC9C;oBACD,MAAM;aACb;SAEJ;IAGL,CAAC;IAED;;;;;;OAMG;IACO,WAAW,CAAC,SAAgB,EAAE,IAAY;QAChD,IAAI,IAAI,CAAC,aAAa,CAAC,qBAAY,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YAC5C,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IACvD,CAAC;CAEJ;AA7gBD,oDA6gBC", "sourcesContent": ["import {\n    AlreadyConnectedError,\n    AlreadyConnectingError,\n    ExtractRoomIdError,\n    FetchIsLiveError,\n    InvalidResponseError,\n    UserOfflineError\n} from '@/types/errors';\n\nimport TypedEventEmitter from 'typed-emitter';\nimport { EventEmitter } from 'node:events';\nimport { ControlAction, WebcastControlMessage, WebcastResponse } from '@/types/tiktok-schema';\nimport TikTokWsClient from '@/lib/ws/lib/ws-client';\nimport Config from '@/lib/config';\nimport { RoomGiftInfo, RoomInfo, TikTokLiveConnectionOptions } from '@/types/client';\nimport { validateAndNormalizeUniqueId } from '@/lib/utilities';\nimport { RoomInfoResponse, TikTokWebClient } from '@/lib/web';\nimport { EulerSigner } from '@/lib/web/lib/tiktok-signer';\nimport {\n    ConnectState,\n    ControlEvent,\n    EventMap,\n    TikTokLiveConnectionState,\n    WebcastEvent,\n    WebcastEventMap\n} from '@/types/events';\nimport { IWebcastRoomChatPayload, IWebcastRoomChatRouteResponse } from '@eulerstream/euler-api-sdk';\n\n\nexport class TikTokLiveConnection extends (EventEmitter as new () => TypedEventEmitter<EventMap>) {\n\n    // Public properties\n    public webClient: TikTokWebClient;\n    public wsClient: TikTokWsClient | null = null;\n\n    // Protected properties\n    protected _roomInfo: RoomInfo | null = null;\n    protected _availableGifts: Record<any, any> | null = null;\n    protected _connectState: ConnectState = ConnectState.DISCONNECTED;\n    public readonly options: TikTokLiveConnectionOptions;\n\n    /**\n     * Create a new TikTokLiveConnection instance\n     * @param {string} uniqueId TikTok username (from URL)\n     * @param {object} [options] Connection options\n     * @param {boolean} [options[].authenticateWs=false] Authenticate the WebSocket connection using the session ID from the \"sessionid\" cookie\n     * @param {boolean} [options[].processInitialData=true] Process the initital data which includes messages of the last minutes\n     * @param {boolean} [options[].fetchRoomInfoOnConnect=false] Fetch the room info (room status, streamer info, etc.) on connect (will be returned when calling connect())\n     * @param {boolean} [options[].enableExtendedGiftInfo=false] Enable this option to get extended information on 'gift' events like gift name and cost\n     * @param {boolean} [options[].enableRequestPolling=true] Use request polling if no WebSocket upgrade is offered. If `false` an exception will be thrown if TikTok does not offer a WebSocket upgrade.\n     * @param {number} [options[].requestPollingIntervalMs=1000] Request polling interval if WebSocket is not used\n     * @param {string} [options[].sessionId=null] The session ID from the \"sessionid\" cookie is required if you want to send automated messages in the chat.\n     * @param {object} [options[].webClientParams={}] Custom client params for Webcast API\n     * @param {object} [options[].webClientHeaders={}] Custom request headers for axios\n     * @param {object} [options[].websocketHeaders={}] Custom request headers for websocket.client\n     * @param {object} [options[].webClientOptions={}] Custom request options for axios. Here you can specify an `httpsAgent` to use a proxy and a `timeout` value for example.\n     * @param {object} [options[].websocketOptions={}] Custom request options for websocket.client. Here you can specify an `agent` to use a proxy and a `timeout` value for example.\n     * @param {string[]} [options[].preferredAgentIds=[]] Preferred agent IDs to use for the WebSocket connection. If not specified, the default agent IDs will be used.\n     * @param {boolean} [options[].connectWithUniqueId=false] Connect to the live stream using the unique ID instead of the room ID. If `true`, the room ID will be fetched from the TikTok API.\n     * @param {boolean} [options[].logFetchFallbackErrors=false] Log errors when falling back to the API or Euler source\n     * @param {function} [options[].signedWebSocketProvider] Custom function to fetch the signed WebSocket URL. If not specified, the default function will be used.\n     * @param {EulerSigner} [signer] TikTok Signer instance. If not provided, a new instance will be created using the provided options\n     */\n    constructor(\n        public readonly uniqueId: string,\n        options?: Partial<TikTokLiveConnectionOptions>,\n        public readonly signer?: EulerSigner\n    ) {\n        super();\n        this.uniqueId = validateAndNormalizeUniqueId(uniqueId);\n\n        // Assign the options\n        this.options = {\n            preferredAgentIds: [],\n            connectWithUniqueId: false,\n            processInitialData: true,\n            fetchRoomInfoOnConnect: false,\n            enableExtendedGiftInfo: false,\n            enableRequestPolling: true,\n            requestPollingIntervalMs: 1000,\n            sessionId: null,\n            signApiKey: null,\n\n            // Override Http client params\n            webClientParams: {},\n            webClientHeaders: {},\n            webClientOptions: {},\n\n            // Override WebSocket params\n            wsClientHeaders: {},\n            wsClientOptions: {},\n            wsClientParams: {},\n\n            authenticateWs: false,\n            signedWebSocketProvider: undefined,\n            logFetchFallbackErrors: false,\n            ...options\n        };\n\n        this.webClient = new TikTokWebClient(\n            {\n                customHeaders: this.options?.webClientHeaders || {},\n                axiosOptions: this.options?.webClientOptions,\n                clientParams: this.options?.webClientParams || {},\n                authenticateWs: this.options?.authenticateWs || false,\n                signApiKey: this.options?.signApiKey ?? undefined\n            },\n            signer\n        );\n\n        this.webClient.cookieJar.sessionId = this.options?.sessionId;\n        this.setDisconnected();\n    }\n\n    /**\n     * Set the connection state to disconnected\n     * @protected\n     */\n    protected setDisconnected() {\n        this._connectState = ConnectState.DISCONNECTED;\n        this._roomInfo = null;\n\n        // Reset the client parameters\n        this.clientParams.cursor = '';\n        this.clientParams.room_id = '';\n        this.clientParams.internal_ext = '';\n    }\n\n    /**\n     * Get the current Room Info\n     */\n    public get roomInfo(): RoomInfoResponse {\n        return this._roomInfo;\n    }\n\n    /**\n     * Get the available gifts\n     */\n    public get availableGifts() {\n        return this._availableGifts;\n    }\n\n    /**\n     * Get the current connection state\n     */\n    public get isConnecting() {\n        return this._connectState === ConnectState.CONNECTING;\n    }\n\n    /**\n     * Check if the connection is established\n     */\n    public get isConnected() {\n        return this._connectState === ConnectState.CONNECTED;\n    }\n\n    /**\n     * Get the current client parameters\n     */\n    public get clientParams() {\n        return this.webClient.clientParams;\n    }\n\n    /**\n     * Get the current room ID\n     */\n    public get roomId(): string {\n        return this.webClient.roomId;\n    }\n\n\n    /**\n     * Get the current connection state including the cached room info and all available gifts\n     * (if `enableExtendedGiftInfo` option enabled)\n     */\n    public get state(): TikTokLiveConnectionState {\n        return {\n            isConnected: this.isConnected,\n            roomId: this.roomId,\n            roomInfo: this.roomInfo,\n            availableGifts: this.availableGifts\n        };\n    }\n\n    /**\n     * Connects to the live stream of the specified streamer\n     * @param roomId Room ID to connect to. If not specified, the room ID will be retrieved from the TikTok API\n     * @returns The current connection state\n     */\n    async connect(roomId?: string): Promise<TikTokLiveConnectionState> {\n\n        switch (this._connectState) {\n            case ConnectState.CONNECTED:\n                throw new AlreadyConnectedError('Already connected!');\n\n            case ConnectState.CONNECTING:\n                throw new AlreadyConnectingError('Already connecting!');\n\n            default:\n            case ConnectState.DISCONNECTED:\n                try {\n                    this._connectState = ConnectState.CONNECTING;\n                    await this._connect(roomId);\n                    this._connectState = ConnectState.CONNECTED;\n                    this.emit(ControlEvent.CONNECTED, this.state);\n                    return this.state;\n                } catch (err) {\n                    this._connectState = ConnectState.DISCONNECTED;\n                    this.handleError(err, 'Error while connecting');\n                    throw err;\n                }\n        }\n    }\n\n    /**\n     * Connects to the live stream of the specified streamer\n     *\n     * @param roomId Room ID to connect to. If not specified, the room ID will be retrieved from the TikTok API\n     * @protected\n     */\n    protected async _connect(roomId?: string): Promise<void> {\n\n        // First we set the Room ID\n        if (!this.options.connectWithUniqueId || this.options.fetchRoomInfoOnConnect || this.options.enableExtendedGiftInfo) {\n            this.clientParams.room_id = roomId || this.clientParams.room_id || await this.fetchRoomId();\n        }\n\n        // <Optional> Fetch Room Info\n        if (this.options?.fetchRoomInfoOnConnect) {\n            this._roomInfo = await this.fetchRoomInfo();\n\n            if (this._roomInfo.status === 4) {\n                throw new UserOfflineError('LIVE has ended');\n            }\n\n        }\n\n        // <Optional> Fetch Gift Info\n        if (this.options?.enableExtendedGiftInfo) {\n            this._availableGifts = await this.fetchAvailableGifts();\n        }\n\n        // <Required> Fetch initial room info. Let the user specify their own backend for signing, if they don't want to use Euler\n        const webcastResponse: WebcastResponse = await (this.options.signedWebSocketProvider || this.webClient.fetchSignedWebSocketFromEuler)(\n            {\n                roomId: (roomId || !this.options.connectWithUniqueId) ? this.roomId : undefined,\n                uniqueId: this.options.connectWithUniqueId ? this.uniqueId : undefined,\n                preferredAgentIds: this.options.preferredAgentIds,\n                sessionId: this.options.authenticateWs ? this.options.sessionId : undefined\n            }\n        );\n\n        // <Optional> Process the initial data\n        if (this.options?.processInitialData) {\n            await this.processWebcastResponse(webcastResponse);\n        }\n\n        // If we didn't receive a cursor\n        if (!webcastResponse.cursor) {\n            throw new InvalidResponseError('Missing cursor in initial fetch response.');\n        }\n\n        // Update client parameters\n        this.clientParams.cursor = webcastResponse.cursor;\n        this.clientParams.internal_ext = webcastResponse.internalExt;\n\n        // Connect to the WebSocket\n        const wsParams: Record<string, string> = {\n            compress: 'gzip',\n            room_id: this.roomId,\n            internal_ext: webcastResponse.internalExt,\n            cursor: webcastResponse.cursor\n        };\n\n        webcastResponse.wsParams.forEach((wsParam) => wsParams[wsParam.name] = wsParam.value);\n        this.wsClient = await this.setupWebsocket(webcastResponse.wsUrl, wsParams);\n        this.emit(ControlEvent.WEBSOCKET_CONNECTED, this.wsClient);\n\n    }\n\n    /**\n     * Disconnects the connection to the live stream\n     */\n    async disconnect(): Promise<void> {\n        if (this.isConnected) {\n            await this.wsClient?.close();\n        }\n    }\n\n    /**\n     * Fetch the room ID from the TikTok API\n     * @param uniqueId Optional unique ID to use instead of the current one\n     */\n    public async fetchRoomId(uniqueId?: string): Promise<string> {\n        let errors: any[] = [];\n\n        uniqueId ||= this.uniqueId;\n\n        // Method 1\n        try {\n            const roomInfo = await this.webClient.fetchRoomInfoFromHtml({ uniqueId: uniqueId });\n            const roomId = roomInfo.liveRoomUserInfo.liveRoom.roomId;\n            if (!roomId) throw new Error('Failed to extract roomId from HTML.');\n            return roomId;\n        } catch (ex) {\n            this.options.logFetchFallbackErrors && console.error('Failed to retrieve roomId from main page, falling back to API source...', ex.stack);\n            errors.push(ex);\n        }\n\n        // Method 2 (API Fallback)\n        try {\n            const roomData = await this.webClient.fetchRoomInfoFromApiLive({ uniqueId: uniqueId });\n            const roomId = roomData?.data?.user?.roomId;\n            if (!roomId) throw new Error('Failed to extract roomId from API.');\n            return roomId;\n        } catch (err) {\n            this.options.logFetchFallbackErrors && console.error('Failed to retrieve roomId from API source, falling back to Euler source...', err.stack);\n            errors.push(err);\n        }\n\n        // Method 3 (Euler Fallback)\n        try {\n            const response = await this.webClient.fetchRoomIdFromEuler({ uniqueId: uniqueId });\n            if (!response.ok) throw new Error(`Failed to retrieve roomId from Euler due to an error: ${response.message}`);\n            if (!response.room_id) throw new Error('Failed to extract roomId from Euler.');\n            return response.room_id;\n        } catch (err) {\n            errors.push(err);\n            throw new ExtractRoomIdError(errors, `Failed to retrieve room_id from all sources. ${err.message}`);\n        }\n\n    }\n\n    public async fetchIsLive(): Promise<boolean> {\n        const errors: any[] = [];\n        const isOnline = (status: number) => status !== 4;\n\n        // Method 1 (HTML)\n        try {\n            const roomInfo = await this.webClient.fetchRoomInfoFromHtml({ uniqueId: this.uniqueId });\n            if (roomInfo?.liveRoomUserInfo?.liveRoom?.status === undefined) throw new Error('Failed to extract status from HTML.');\n            return isOnline(roomInfo?.liveRoomUserInfo?.liveRoom?.status);\n        } catch (ex) {\n            this.options.logFetchFallbackErrors && console.error('Failed to retrieve room info from main page, falling back to API source...', ex);\n            errors.push(ex);\n        }\n\n        // Method 2 (API)\n        try {\n            const roomData = await this.webClient.fetchRoomInfoFromApiLive({ uniqueId: this.uniqueId });\n            if (roomData?.data?.liveRoom?.status === undefined) throw new Error('Failed to extract status from API.');\n            return isOnline(roomData?.data?.liveRoom?.status);\n        } catch (err) {\n            this.options.logFetchFallbackErrors && console.error('Failed to retrieve room info from API source, falling back to Euler source...', err);\n            errors.push(err);\n        }\n\n        // Method 3 (Euler)\n        try {\n            const roomData = await this.webClient.fetchRoomIdFromEuler({ uniqueId: this.uniqueId });\n            if (roomData.code !== 200) throw new Error('Failed to extract status from Euler.');\n            return roomData.is_live;\n        } catch (err) {\n            this.options.logFetchFallbackErrors && console.error('Failed to retrieve room info from Euler source...', err);\n            errors.push(err);\n            throw new FetchIsLiveError(errors, `Failed to retrieve room_id from all sources. ${err.message}`);\n        }\n\n    }\n\n    /**\n     * Wait until the streamer is live\n     * @param seconds Number of seconds to wait before checking if the streamer is live again\n     */\n    public async waitUntilLive(seconds: number = 60): Promise<void> {\n        seconds = Math.max(30, seconds);\n\n        return new Promise(async (resolve) => {\n            const fetchIsLive = async () => {\n                const isLive = await this.fetchIsLive();\n\n                if (isLive) {\n                    clearInterval(interval);\n                    resolve();\n                }\n            };\n\n            const interval = setInterval(async () => fetchIsLive(), seconds * 1000);\n            await fetchIsLive();\n        });\n\n    }\n\n    /**\n     * Get the current room info (including streamer info, room status and statistics)\n     * @returns Promise that will be resolved when the room info has been retrieved from the API\n     */\n    public async fetchRoomInfo(): Promise<RoomInfoResponse> {\n        if (!this.webClient.roomId) await this.fetchRoomId();\n        this._roomInfo = await this.webClient.fetchRoomInfo();\n        return this._roomInfo;\n    }\n\n    /**\n     * Get the available gifts in the current room\n     * @returns Promise that will be resolved when the available gifts have been retrieved from the API\n     */\n    public async fetchAvailableGifts(): Promise<RoomGiftInfo> {\n        try {\n            let response = await this.webClient.getJsonObjectFromWebcastApi('gift/list/', this.clientParams);\n            return response.data.gifts;\n        } catch (err) {\n            throw new InvalidResponseError(`Failed to fetch available gifts. ${err.message}`, err);\n        }\n    }\n\n    /**\n     * Send a message to a TikTok LIVE Room\n     *\n     * @param content Message content to send to the stream\n     * @param options Optional parameters for the message (incl. parameter overrides)\n     */\n    public async sendMessage(content: string, options?: Partial<Omit<IWebcastRoomChatPayload, 'content'>>): Promise<IWebcastRoomChatRouteResponse> {\n\n        const roomId = options?.roomId || this.roomId;\n        if (!roomId) {\n            throw new Error('Room ID is required to send a message.');\n        }\n\n        const sessionId = options?.sessionId || this.webClient.cookieJar.sessionId;\n        if (!sessionId) {\n            throw new Error('Session ID is required to send a message.');\n        }\n\n        return this.webClient.sendRoomChatFromEuler(\n            {\n                content: content,\n                roomId: roomId,\n                sessionId: sessionId\n            }\n        );\n    }\n\n    /**\n     * Set up the WebSocket connection\n     *\n     * @param wsUrl WebSocket URL\n     * @param wsParams WebSocket parameters\n     * @returns Promise that will be resolved when the WebSocket connection is established\n     * @protected\n     */\n    protected async setupWebsocket(wsUrl: string, wsParams: Record<string, string>): Promise<TikTokWsClient> {\n        return new Promise<TikTokWsClient>((resolve, reject) => {\n\n            // Instantiate the client\n            const wsClient = new TikTokWsClient(\n                wsUrl,\n                this.webClient.cookieJar,\n                { ...Config.DEFAULT_WS_CLIENT_PARAMS, ...this.options.wsClientParams, ...wsParams },\n                { ...Config.DEFAULT_WS_CLIENT_HEADERS, ...this.options?.wsClientHeaders },\n                this.options?.wsClientOptions\n            );\n\n            // Handle the connection\n            wsClient.on('connect', (ws) => {\n                clearTimeout(connectTimeout);\n                ws.on('error', (e: any) => this.handleError(e, 'WebSocket Error'));\n                ws.on('close', () => {\n                    this.setDisconnected();\n                    this.emit(ControlEvent.DISCONNECTED);\n                });\n                resolve(wsClient);\n            });\n\n            wsClient.on('connectFailed', (err: any) => reject(`Websocket connection failed, ${err}`));\n            wsClient.on('webcastResponse', (msg: WebcastResponse) => this.processWebcastResponse(msg));\n            wsClient.on('messageDecodingFailed', (err: any) => this.handleError(err, 'Websocket message decoding failed'));\n            const connectTimeout = setTimeout(() => reject('Websocket not responding'), 20_000);\n        });\n    }\n\n    protected async processWebcastResponse(webcastResponse: WebcastResponse): Promise<void> {\n\n        // Emit Raw Data\n        webcastResponse.messages.forEach((\n            message) => this.emit(ControlEvent.RAW_DATA, message.type, message.binary)\n        );\n\n        // Process and emit decoded data depending on the message type\n        for (let message of webcastResponse.messages) {\n            const messageData = message.decodedData || {} as any;\n\n            // Emit a decoded data event\n            this.emit(\n                ControlEvent.DECODED_DATA,\n                message.type,\n                message.decodedData || {},\n                message.binary\n            );\n\n            // Attempt to get it from the map\n            const basicEvent = WebcastEventMap[message.type];\n            if (basicEvent) {\n                this.emit(basicEvent, messageData);\n                return;\n            }\n\n            // Handle custom events\n            switch (message.type) {\n                case 'WebcastControlMessage':\n                    const controlMessage = messageData as WebcastControlMessage;\n                    if (controlMessage.action === ControlAction.CONTROL_ACTION_STREAM_SUSPENDED || controlMessage.action === ControlAction.CONTROL_ACTION_STREAM_ENDED) {\n                        this.emit(WebcastEvent.STREAM_END, { action: controlMessage.action });\n                        await this.disconnect();\n                    }\n                    break;\n                case 'WebcastGiftMessage':\n                    // Add extended gift info if option enabled\n                    if (Array.isArray(this.availableGifts) && messageData.giftId) {\n                        messageData.extendedGiftInfo = this.availableGifts.find((x) => x.id === messageData.giftId);\n                    }\n                    this.emit(WebcastEvent.GIFT, messageData);\n                    break;\n                case 'WebcastSocialMessage':\n                    this.emit(WebcastEvent.SOCIAL, messageData);\n                    if (messageData.displayType?.includes('follow')) {\n                        this.emit(WebcastEvent.FOLLOW, messageData);\n                    }\n                    if (messageData.displayType?.includes('share')) {\n                        this.emit(WebcastEvent.SHARE, messageData);\n                    }\n                    break;\n            }\n\n        }\n\n\n    }\n\n    /**\n     * Handle the error event\n     *\n     * @param exception Exception object\n     * @param info Additional information about the error\n     * @protected\n     */\n    protected handleError(exception: Error, info: string): void {\n        if (this.listenerCount(ControlEvent.ERROR) < 1) {\n            return;\n        }\n\n        this.emit(ControlEvent.ERROR, { info, exception });\n    }\n\n}\n\n\n"]}