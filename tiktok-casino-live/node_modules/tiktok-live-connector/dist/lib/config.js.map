{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/lib/config.ts"], "names": [], "mappings": ";;;AAGA,+CAA4E;AAE5E,uCAAoC;AAuBvB,QAAA,SAAS,GAAqB;IACvC;QACI,cAAc,EAAE,OAAO;QACvB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,eAAe;KAC7B;IACD;QACI,cAAc,EAAE,OAAO;QACvB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,iBAAiB;KAC/B;IACD;QACI,cAAc,EAAE,OAAO;QACvB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,kBAAkB;KAChC;IACD;QACI,cAAc,EAAE,OAAO;QACvB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,kBAAkB;KAChC;IACD;QACI,cAAc,EAAE,OAAO;QACvB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,qBAAqB;KACnC;IACD;QACI,cAAc,EAAE,OAAO;QACvB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,eAAe;KAC7B;IACD;QACI,cAAc,EAAE,OAAO;QACvB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,iBAAiB;KAC/B;IACD;QACI,cAAc,EAAE,OAAO;QACvB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,gBAAgB;KAC9B;IACD;QACI,cAAc,EAAE,OAAO;QACvB,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,uBAAuB;KACrC;CACJ,CAAC;AAGW,QAAA,OAAO,GAAmB;IACnC;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;IACD;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;IACD;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;IACD;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;IACD;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;IACD;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;IACD;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;IACD;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;IACD;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;IACD;QACI,cAAc,EAAE,IAAI;QACpB,eAAe,EAAE,IAAI;KACxB;CACJ,CAAC;AAEW,QAAA,UAAU,GAAa;IAChC,4FAA4F;IAC5F,iHAAiH;IACjH,uHAAuH;IAEvH,4FAA4F;IAC5F,oHAAoH;IAEpH,8FAA8F;IAC9F,kFAAkF;IAClF,qFAAqF;IAErF,wFAAwF;IACxF,mIAAmI;IACnI,yIAAyI;IAEzI,0FAA0F;IAC1F,+HAA+H;IAC/H,kIAAkI;CACrI,CAAC;AAGW,QAAA,OAAO,GAAmB,kBAAU,CAAC,GAAG,CAAC,CAAC,SAAiB,EAAE,EAAE,CAAC,IAAA,mCAAuB,EAAC,SAAS,CAAC,CAAC,CAAC;AAEjH,gBAAgB;AACH,QAAA,MAAM,GAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,eAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,eAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,eAAO,CAAC,CAAC,CAAC,CAAC;AAEvK,kBAAkB;AACL,QAAA,QAAQ,GAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,iBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,iBAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAClK,YAAY,EAAE,OAAO;IACrB,IAAI,EAAE,IAAI;IACV,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,eAAe;CAC3B,CAAC;AAEW,QAAA,MAAM,GAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,eAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,eAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACxJ,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE,IAAI;CACtB,CAAC;AAGF,MAAM,MAAM,GAAmB;IAC3B,eAAe,EAAE,gBAAgB;IACjC,mBAAmB,EAAE,oBAAoB;IACzC,kBAAkB,EAAE,wBAAwB;IAC5C,2BAA2B,EAAE,EAAE,eAAe,EAAE,UAAU,EAAE;IAC5D,2BAA2B,EAAE,EAAE;IAC/B,0BAA0B,EAAE;QACxB,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;QACxB,cAAc,EAAE,gBAAQ,CAAC,MAAM,CAAC;QAChC,UAAU,EAAE,YAAY;QACxB,kBAAkB,EAAE,gBAAQ,CAAC,cAAc,CAAC;QAC5C,cAAc,EAAE,cAAM,CAAC,cAAc,CAAC;QACtC,gBAAgB,EAAE,MAAM;QACxB,kBAAkB,EAAE,cAAM,CAAC,kBAAkB,CAAC;QAC9C,iBAAiB,EAAE,cAAM,CAAC,iBAAiB,CAAC;QAC5C,gBAAgB,EAAE,MAAM;QACxB,iBAAiB,EAAE,QAAQ;QAC3B,aAAa,EAAE,MAAM;QACrB,WAAW,EAAE,MAAM;QACnB,aAAa,EAAE,IAAI;QACnB,eAAe,EAAE,OAAO;QACxB,iBAAiB,EAAE,MAAM;QACzB,eAAe,EAAE,cAAM,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE;QACnD,cAAc,EAAE,cAAM,CAAC,cAAc,CAAC,CAAC,QAAQ,EAAE;QACjD,SAAS,EAAE,gBAAQ,CAAC,SAAS,CAAC;QAC9B,SAAS,EAAE,yBAAyB;QACpC,cAAc,EAAE,yBAAyB;QACzC,SAAS,EAAE,YAAY;QACvB,yBAAyB,EAAE,MAAM;QACjC,IAAI,EAAE,cAAM,CAAC,IAAI,CAAC;QAClB,iBAAiB,EAAE,gBAAQ,CAAC,SAAS,CAAC;QACtC,QAAQ,EAAE,gBAAQ,CAAC,SAAS,CAAC;QAC7B,eAAe,EAAE,MAAM;QACvB,kBAAkB,EAAE,gBAAQ,CAAC,MAAM,CAAC;QACpC,WAAW,EAAE,IAAA,4BAAgB,GAAE;KAClC;IACD,wBAAwB,EAAE;QACtB,cAAc,EAAE,QAAQ;QACxB,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;QACxB,cAAc,EAAE,gBAAQ,CAAC,MAAM,CAAC;QAChC,UAAU,EAAE,YAAY;QACxB,kBAAkB,EAAE,cAAM,CAAC,kBAAkB,CAAC;QAC9C,kBAAkB,EAAE,gBAAQ,CAAC,cAAc,CAAC;QAC5C,cAAc,EAAE,cAAM,CAAC,cAAc,CAAC;QACtC,iBAAiB,EAAE,cAAM,CAAC,iBAAiB,CAAC;QAC5C,gBAAgB,EAAE,MAAM;QACxB,gBAAgB,EAAE,MAAM;QACxB,SAAS,EAAE,gBAAQ,CAAC,SAAS,CAAC;QAC9B,iBAAiB,EAAE,KAAK;QACxB,OAAO,EAAE,OAAO;QAChB,MAAM,EAAE,4BAA4B;QACpC,UAAU,EAAE,UAAU;QACtB,SAAS,EAAE,IAAI;QACf,eAAe,EAAE,GAAG;QACpB,qBAAqB,EAAE,OAAO;QAC9B,UAAU,EAAE,GAAG;QACf,eAAe,EAAE,CAAC,cAAM,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE;QACrD,cAAc,EAAE,CAAC,cAAM,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,EAAE;QACnD,oBAAoB,EAAE,GAAG;QACzB,mBAAmB,EAAE,UAAU;QAC/B,uBAAuB,EAAE,GAAG;QAC5B,4DAA4D;QAC5D,UAAU,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,EAAE;KACjE;IACD,yCAAyC,EAAE,sBAAsB;IACjE,2BAA2B,EAAE;QACzB,YAAY,EAAE,YAAY;QAC1B,eAAe,EAAE,WAAW;QAC5B,YAAY,EAAE,cAAM,CAAC,YAAY,CAAC;QAClC,QAAQ,EAAE,iDAAiD;QAC3D,SAAS,EAAE,yBAAyB;QACpC,QAAQ,EAAE,wBAAwB;QAClC,iBAAiB,EAAE,gBAAgB;QACnC,iBAAiB,EAAE,eAAe;QAClC,gBAAgB,EAAE,WAAW;QAC7B,gBAAgB,EAAE,MAAM;QACxB,gBAAgB,EAAE,OAAO;QACzB,qBAAqB,EAAE,IAAI;KAC9B;IACD,yBAAyB,EAAE;QACvB,YAAY,EAAE,cAAM,CAAC,YAAY,CAAC;KACrC;CACJ,CAAC;AAGW,QAAA,UAAU,GAAiC;IACpD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,gCAAgC;IACtE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;IAChC,WAAW,EAAE;QACT,OAAO,EAAE,EAAE,YAAY,EAAE,yBAAyB,iBAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,EAAE;QACjF,cAAc,EAAE,GAAG,EAAE,CAAC,IAAI;KAC7B;CACJ,CAAC;AAEF,kBAAe,MAAM,CAAC", "sourcesContent": ["import { IWebcastConfig } from '@/types/client';\n\n\nimport { generateDeviceId, userAgentToDevicePreset } from '@/lib/utilities';\nimport { ClientConfiguration } from '@eulerstream/euler-api-sdk';\nimport { VERSION } from '@/version';\n\nexport type LocationPreset = {\n    lang: string,\n    lang_country: string,\n    country: string,\n    tz_name: string\n}\n\nexport type DevicePreset = {\n    browser_version: string,\n    browser_name: string,\n    browser_platform: string,\n    user_agent: string,\n    os: string\n}\n\nexport type ScreenPreset = {\n    screen_width: number;\n    screen_height: number;\n}\n\n\nexport const Locations: LocationPreset[] = [\n    {\n        'lang_country': 'en-GB',\n        'lang': 'en',\n        'country': 'GB',\n        'tz_name': 'Europe/London'\n    },\n    {\n        'lang_country': 'en-CA',\n        'lang': 'en',\n        'country': 'CA',\n        'tz_name': 'America/Toronto'\n    },\n    {\n        'lang_country': 'en-AU',\n        'lang': 'en',\n        'country': 'AU',\n        'tz_name': 'Australia/Sydney'\n    },\n    {\n        'lang_country': 'en-NZ',\n        'lang': 'en',\n        'country': 'NZ',\n        'tz_name': 'Pacific/Auckland'\n    },\n    {\n        'lang_country': 'en-ZA',\n        'lang': 'en',\n        'country': 'ZA',\n        'tz_name': 'Africa/Johannesburg'\n    },\n    {\n        'lang_country': 'en-IE',\n        'lang': 'en',\n        'country': 'IE',\n        'tz_name': 'Europe/Dublin'\n    },\n    {\n        'lang_country': 'en-JM',\n        'lang': 'en',\n        'country': 'JM',\n        'tz_name': 'America/Jamaica'\n    },\n    {\n        'lang_country': 'en-BZ',\n        'lang': 'en',\n        'country': 'BZ',\n        'tz_name': 'America/Belize'\n    },\n    {\n        'lang_country': 'en-TT',\n        'lang': 'en',\n        'country': 'TT',\n        'tz_name': 'America/Port_of_Spain'\n    }\n];\n\n\nexport const Screens: ScreenPreset[] = [\n    {\n        'screen_width': 1920,\n        'screen_height': 1080\n    },\n    {\n        'screen_width': 2560,\n        'screen_height': 1440\n    },\n    {\n        'screen_width': 3840,\n        'screen_height': 2160\n    },\n    {\n        'screen_width': 4096,\n        'screen_height': 2160\n    },\n    {\n        'screen_width': 5120,\n        'screen_height': 2880\n    },\n    {\n        'screen_width': 7680,\n        'screen_height': 4320\n    },\n    {\n        'screen_width': 1152,\n        'screen_height': 2048\n    },\n    {\n        'screen_width': 1440,\n        'screen_height': 2560\n    },\n    {\n        'screen_width': 2160,\n        'screen_height': 3840\n    },\n    {\n        'screen_width': 4320,\n        'screen_height': 7680\n    }\n];\n\nexport const UserAgents: string[] = [\n    // Latest Chrome UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/chrome\n    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',\n    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36',\n\n    // Latest Safari UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/safari\n    'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.6 Safari/605.1.15',\n\n    // Latest Firefox UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/firefox\n    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:130.0) Gecko/20100101 Firefox/130.0',\n    'Mozilla/5.0 (Macintosh; Intel Mac OS X 14.7; rv:130.0) Gecko/20100101 Firefox/130.0',\n\n    // Latest Edge UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/edge\n    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/128.0.2739.79',\n    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/128.0.2739.79',\n\n    // Latest Opera UA's -> https://www.whatismybrowser.com/guides/the-latest-user-agent/opera\n    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 OPR/113.0.0.0',\n    'Mozilla/5.0 (Macintosh; Intel Mac OS X 14_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 OPR/113.0.0.0'\n];\n\n\nexport const Devices: DevicePreset[] = UserAgents.map((userAgent: string) => userAgentToDevicePreset(userAgent));\n\n// Pick a device\nexport const Device: DevicePreset = (process.env.RANDOMIZE_TIKTOK_DEVICE?.toLowerCase() === 'true') ? Devices[Math.floor(Math.random() * Devices.length)] : Devices[6];\n\n// Pick a location\nexport const Location: LocationPreset = (process.env.RANDOMIZE_TIKTOK_LOCATION?.toLowerCase() === 'true') ? Locations[Math.floor(Math.random() * Locations.length)] : {\n    lang_country: 'en-US',\n    lang: 'en',\n    country: 'GB',\n    tz_name: 'Europe/Berlin'\n};\n\nexport const Screen: ScreenPreset = (process.env.RANDOMIZE_TIKTOK_SCREEN?.toLowerCase() === 'true') ? Screens[Math.floor(Math.random() * Screens.length)] : {\n    screen_width: 1920,\n    screen_height: 1080\n};\n\n\nconst Config: IWebcastConfig = {\n    TIKTOK_HOST_WEB: 'www.tiktok.com',\n    TIKTOK_HOST_WEBCAST: 'webcast.tiktok.com',\n    TIKTOK_HTTP_ORIGIN: 'https://www.tiktok.com',\n    DEFAULT_HTTP_CLIENT_COOKIES: { 'tt-target-idc': 'useast1a' },\n    DEFAULT_HTTP_CLIENT_OPTIONS: {},\n    DEFAULT_HTTP_CLIENT_PARAMS: {\n        'aid': (1988).toString(),\n        'app_language': Location['lang'],\n        'app_name': 'tiktok_web',\n        'browser_language': Location['lang_country'],\n        'browser_name': Device['browser_name'],\n        'browser_online': 'true',\n        'browser_platform': Device['browser_platform'],\n        'browser_version': Device['browser_version'],\n        'cookie_enabled': 'true',\n        'device_platform': 'web_pc',\n        'focus_state': 'true',\n        'from_page': 'user',\n        'history_len': '10',\n        'is_fullscreen': 'false',\n        'is_page_visible': 'true',\n        'screen_height': Screen['screen_height'].toString(),\n        'screen_width': Screen['screen_width'].toString(),\n        'tz_name': Location['tz_name'],\n        'referer': 'https://www.tiktok.com/',\n        'root_referer': 'https://www.tiktok.com/',\n        'channel': 'tiktok_web',\n        'data_collection_enabled': 'true',\n        'os': Device['os'],\n        'priority_region': Location['country'],\n        'region': Location['country'],\n        'user_is_login': 'true',\n        'webcast_language': Location['lang'],\n        'device_id': generateDeviceId()\n    },\n    DEFAULT_WS_CLIENT_PARAMS: {\n        'version_code': '180800',\n        'aid': (1988).toString(),\n        'app_language': Location['lang'],\n        'app_name': 'tiktok_web',\n        'browser_platform': Device['browser_platform'],\n        'browser_language': Location['lang_country'],\n        'browser_name': Device['browser_name'],\n        'browser_version': Device['browser_version'],\n        'browser_online': 'true',\n        'cookie_enabled': 'true',\n        'tz_name': Location['tz_name'],\n        'device_platform': 'web',\n        'debug': 'false',\n        'host': 'https://webcast.tiktok.com',\n        'identity': 'audience',\n        'live_id': '12',\n        'sup_ws_ds_opt': '1',\n        'update_version_code': '2.0.0',\n        'did_rule': '3',\n        'screen_height': (Screen['screen_height']).toString(),\n        'screen_width': (Screen['screen_width']).toString(),\n        'heartbeat_duration': '0',\n        'resp_content_type': 'protobuf',\n        'history_comment_count': '6',\n        // We think last_rtt means \"last round trip time\" in millis.\n        'last_rtt': (Math.floor(Math.random() * 100) + 100).toString(),\n    },\n    DEFAULT_WS_CLIENT_PARAMS_APPEND_PARAMETER: \"&version_code=270000\",\n    DEFAULT_HTTP_CLIENT_HEADERS: {\n        'Connection': 'keep-alive',\n        'Cache-Control': 'max-age=0',\n        'User-Agent': Device['user_agent'],\n        'Accept': 'text/html,application/json,application/protobuf',\n        'Referer': 'https://www.tiktok.com/',\n        'Origin': 'https://www.tiktok.com',\n        'Accept-Language': 'en-US,en;q=0.9',\n        'Accept-Encoding': 'gzip, deflate',\n        'Sec-Fetch-Site': 'same-site',\n        'Sec-Fetch-Mode': 'cors',\n        'Sec-Fetch-Dest': 'empty',\n        'Sec-Fetch-Ua-Mobile': '?0'\n    },\n    DEFAULT_WS_CLIENT_HEADERS: {\n        'User-Agent': Device['user_agent'],\n    }\n};\n\n\nexport const SignConfig: Partial<ClientConfiguration> = {\n    basePath: process.env.SIGN_API_URL || 'https://tiktok.eulerstream.com',\n    apiKey: process.env.SIGN_API_KEY,\n    baseOptions: {\n        headers: { 'User-Agent': `tiktok-live-connector/${VERSION} ${process.platform}` },\n        validateStatus: () => true\n    }\n};\n\nexport default Config;\n"]}