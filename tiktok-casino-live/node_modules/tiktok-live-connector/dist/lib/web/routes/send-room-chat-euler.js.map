{"version": 3, "file": "send-room-chat-euler.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/routes/send-room-chat-euler.ts"], "names": [], "mappings": ";;;AAAA,yCAAsC;AAGtC,mCAA8C;AAI9C,MAAa,0BAA2B,SAAQ,aAAsE;IAElH,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE;QAC9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC;YAClE,MAAM;YACN,OAAO;YACP,SAAS;SACZ,EACD,OAAO,CACV,CAAC;QAEF,QAAQ,aAAa,CAAC,MAAM,EAAE;YAC1B,KAAK,GAAG,CAAC;YACT,KAAK,GAAG;gBACJ,MAAM,IAAI,2BAAmB,CACzB,qFAAqF,EACrF,aAAa,CAAC,IAAI,CAAC,OAAO,EAC1B,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CACrC,CAAC;YACN,KAAK,GAAG;gBACJ,OAAO,aAAa,CAAC,IAAI,CAAC;YAC9B;gBACI,MAAM,IAAI,KAAK,CAAC,wBAAwB,aAAa,EAAE,IAAI,EAAE,OAAO,IAAI,eAAe,EAAE,CAAC,CAAC;SAClG;IAEL,CAAC;CAEJ;AA3BD,gEA2BC", "sourcesContent": ["import { Route } from '@/types/route';\nimport { IWebcastRoomChatPayload, IWebcastRoomChatRouteResponse } from '@eulerstream/euler-api-sdk';\nimport { AxiosRequestConfig } from 'axios';\nimport { PremiumFeatureError } from '@/types';\n\nexport type SendRoomChatFromEulerRouteParams = IWebcastRoomChatPayload & AxiosRequestConfig;\n\nexport class SendRoomChatFromEulerRoute extends Route<SendRoomChatFromEulerRouteParams, IWebcastRoomChatRouteResponse> {\n\n    async call({ roomId, content, sessionId, options }): Promise<IWebcastRoomChatRouteResponse> {\n        const fetchResponse = await this.webClient.webSigner.webcast.sendRoomChat({\n                roomId,\n                content,\n                sessionId\n            },\n            options\n        );\n\n        switch (fetchResponse.status) {\n            case 401:\n            case 403:\n                throw new PremiumFeatureError(\n                    'Sending chats requires an API key & a paid plan, as it uses cloud managed services.',\n                    fetchResponse.data.message,\n                    JSON.stringify(fetchResponse.data)\n                );\n            case 200:\n                return fetchResponse.data;\n            default:\n                throw new Error(`Failed to send chat: ${fetchResponse?.data?.message || 'Unknown error'}`);\n        }\n\n    }\n\n}\n"]}