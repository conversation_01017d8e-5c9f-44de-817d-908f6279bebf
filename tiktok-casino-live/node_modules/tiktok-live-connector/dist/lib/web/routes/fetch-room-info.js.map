{"version": 3, "file": "fetch-room-info.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/routes/fetch-room-info.ts"], "names": [], "mappings": ";;;AAAA,yCAAsC;AACtC,2CAA0E;AAM1E,MAAa,kBAAmB,SAAQ,aAA4C;IAEhF,KAAK,CAAC,IAAI,CAAC,MAA2B;QAClC,iBAAiB;QACjB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC;QAG5C,+BAA+B;QAC/B,IAAI,MAAM,IAAI,IAAI,EAAE;YAChB,MAAM,IAAI,2BAAkB,CAAC,6DAA6D,CAAC,CAAC;SAC/F;QAED,kBAAkB;QAClB,IAAI;YACA,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,2BAA2B,CACnD,YAAY,EACZ,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,EAClD,KAAK,CACR,CAAC;SACL;QAAC,OAAO,GAAG,EAAE;YACV,MAAM,IAAI,6BAAoB,CAAC,8BAA8B,GAAG,CAAC,OAAO,EAAE,EAAE,GAAG,CAAC,CAAC;SACpF;IAEL,CAAC;CAEJ;AAzBD,gDAyBC", "sourcesContent": ["import { Route } from '@/types/route';\nimport { InvalidResponseError, MissingRoomIdError } from '@/types/errors';\nimport { RoomInfo } from '@/types/client';\n\nexport type RoomInfoRouteParams = { roomId?: string; } | void;\nexport type RoomInfoResponse = any;\n\nexport class FetchRoomInfoRoute extends Route<RoomInfoRouteParams, RoomInfoResponse> {\n\n    async call(params: RoomInfoRouteParams) {\n        // Assign Room ID\n        const { roomId } = params || this.webClient;\n\n\n        // Must have a Room ID to fetch\n        if (roomId == null) {\n            throw new MissingRoomIdError('Missing roomId. Please provide a roomId to the HTTP client.');\n        }\n\n        // Fetch room info\n        try {\n            return await this.webClient.getJsonObjectFromWebcastApi<RoomInfo>(\n                'room/info/',\n                { ...this.webClient.clientParams, roomId: roomId },\n                false\n            );\n        } catch (err) {\n            throw new InvalidResponseError(`Failed to fetch room info. ${err.message}`, err);\n        }\n\n    }\n\n}\n"]}