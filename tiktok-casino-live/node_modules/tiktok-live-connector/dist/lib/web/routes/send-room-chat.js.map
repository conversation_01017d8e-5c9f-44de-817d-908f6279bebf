{"version": 3, "file": "send-room-chat.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/routes/send-room-chat.ts"], "names": [], "mappings": ";;;AAAA,yCAAsC;AACtC,2CAA0E;AAK1E,MAAa,iBAAkB,SAAQ,aAAyD;IAE5F,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;QAC1B,MAAM,EAAE,OAAO,EAAE,GAAG,EAAG,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;QAErF,iBAAiB;QACjB,MAAM,KAAK,GAAG,CAAC;QAEf,+BAA+B;QAC/B,IAAI,MAAM,IAAI,IAAI,EAAE;YAChB,MAAM,IAAI,2BAAkB,CAAC,6DAA6D,CAAC,CAAC;SAC/F;QAED,kBAAkB;QAClB,IAAI;YACA,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAClD,YAAY,EACZ,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,EAC9C,SAAS,EACT,IAAI,CACP,CAAC;SACL;QAAC,OAAO,GAAG,EAAE;YACV,MAAM,IAAI,6BAAoB,CAAC,8BAA8B,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;SAC/E;IACL,CAAC;CAEJ;AA1BD,8CA0BC", "sourcesContent": ["import { Route } from '@/types/route';\nimport { InvalidResponseError, MissingRoomIdError } from '@/types/errors';\n\nexport type SendRoomChatRouteParams = { content: string, roomId?: string };\nexport type SendRoomChatRouteResponse = any;\n\nexport class SendRoomChatRoute extends Route<SendRoomChatRouteParams, SendRoomChatRouteResponse> {\n\n    async call({ roomId, content }) {\n        const { room_id: rId,  cursor, internal_ext, ...rest } = this.webClient.clientParams;\n\n        // Assign Room ID\n        roomId ||= rId;\n\n        // Must have a Room ID to fetch\n        if (roomId == null) {\n            throw new MissingRoomIdError('Missing roomId. Please provide a roomId to the HTTP client.');\n        }\n\n        // Fetch room info\n        try {\n            return await this.webClient.postJsonObjectToWebcastApi(\n                'room/chat/',\n                { ...rest, room_id: roomId, content: content },\n                undefined,\n                true\n            );\n        } catch (err) {\n            throw new InvalidResponseError(`Failed to fetch room info. ${err.message}`);\n        }\n    }\n\n}\n"]}