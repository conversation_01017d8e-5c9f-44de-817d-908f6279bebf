{"version": 3, "file": "fetch-room-info-euler.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/routes/fetch-room-info-euler.ts"], "names": [], "mappings": ";;;AAAA,yCAAsC;AAMtC,MAAa,2BAA4B,SAAQ,aAAuE;IAEpH,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACjG,OAAO,aAAa,CAAC,IAAI,CAAC;IAC9B,CAAC;CAEJ;AAPD,kEAOC", "sourcesContent": ["import { Route } from '@/types/route';\nimport { IWebcastRoomInfoRouteResponse } from '@eulerstream/euler-api-sdk';\nimport { AxiosRequestConfig } from 'axios';\n\nexport type FetchRoomInfoFromEulerRouteParams = { uniqueId: string, options?: AxiosRequestConfig };\n\nexport class FetchRoomInfoFromEulerRoute extends Route<FetchRoomInfoFromEulerRouteParams, IWebcastRoomInfoRouteResponse> {\n\n    async call({ uniqueId, options }): Promise<IWebcastRoomInfoRouteResponse> {\n        const fetchResponse = await this.webClient.webSigner.webcast.retrieveRoomInfo(uniqueId, options);\n        return fetchResponse.data;\n    }\n\n}\n"]}