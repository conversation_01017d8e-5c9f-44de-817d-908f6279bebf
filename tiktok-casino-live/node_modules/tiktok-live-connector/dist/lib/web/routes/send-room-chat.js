"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SendRoomChatRoute = void 0;
const route_1 = require("../../../types/route");
const errors_1 = require("../../../types/errors");
class SendRoomChatRoute extends route_1.Route {
    async call({ roomId, content }) {
        const { room_id: rId, cursor, internal_ext, ...rest } = this.webClient.clientParams;
        // Assign Room ID
        roomId ||= rId;
        // Must have a Room ID to fetch
        if (roomId == null) {
            throw new errors_1.MissingRoomIdError('Missing roomId. Please provide a roomId to the HTTP client.');
        }
        // Fetch room info
        try {
            return await this.webClient.postJsonObjectToWebcastApi('room/chat/', { ...rest, room_id: roomId, content: content }, undefined, true);
        }
        catch (err) {
            throw new errors_1.InvalidResponseError(`Failed to fetch room info. ${err.message}`);
        }
    }
}
exports.SendRoomChatRoute = SendRoomChatRoute;
//# sourceMappingURL=send-room-chat.js.map