{"version": 3, "file": "fetch-room-id-euler.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/routes/fetch-room-id-euler.ts"], "names": [], "mappings": ";;;AAAA,yCAAsC;AAMtC,MAAa,yBAA0B,SAAQ,aAAmE;IAE9G,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC5B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC/F,OAAO,aAAa,CAAC,IAAI,CAAC;IAC9B,CAAC;CAEJ;AAPD,8DAOC", "sourcesContent": ["import { Route } from '@/types/route';\nimport { IWebcastRoomIdRouteResponse } from '@eulerstream/euler-api-sdk';\nimport { AxiosRequestConfig } from 'axios';\n\nexport type FetchRoomIdFromEulerRouteParams = { uniqueId: string, options?: AxiosRequestConfig };\n\nexport class FetchRoomIdFromEulerRoute extends Route<FetchRoomIdFromEulerRouteParams, IWebcastRoomIdRouteResponse> {\n\n    async call({ uniqueId, options }): Promise<IWebcastRoomIdRouteResponse> {\n        const fetchResponse = await this.webClient.webSigner.webcast.retrieveRoomId(uniqueId, options);\n        return fetchResponse.data;\n    }\n\n}\n"]}