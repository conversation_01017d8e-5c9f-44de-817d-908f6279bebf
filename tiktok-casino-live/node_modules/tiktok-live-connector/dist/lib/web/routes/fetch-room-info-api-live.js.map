{"version": 3, "file": "fetch-room-info-api-live.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/routes/fetch-room-info-api-live.ts"], "names": [], "mappings": ";;;AAAA,yCAAsC;AACtC,2CAAsD;AAYtD,MAAa,6BAA8B,SAAQ,aAAyE;IAExH,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE;QAEnB,+BAA+B;QAC/B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAoC,qBAAqB,EAAE;YACvH,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY;YAC9B,QAAQ,EAAE,QAAQ;YAClB,UAAU,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,QAAQ,CAAC,UAAU,EAAE;YACrB,MAAM,IAAI,6BAAoB,CAAC,aAAa,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC,OAAO,IAAI,eAAe,GAAG,EAAE,SAAS,CAAC,CAAC;SAC1H;QAED,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;YAC/B,MAAM,IAAI,6BAAoB,CAAC,8BAA8B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;SACvG;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;CAEJ;AAvBD,sEAuBC", "sourcesContent": ["import { Route } from '@/types/route';\nimport { InvalidResponseError } from '@/types/errors';\n\nexport type FetchRoomInfoFromApiRouteParams = { uniqueId: string };\nexport type FetchRoomInfoFromApiRouteResponse = {\n    statusCode: number;\n    message?: string;\n    data?: {\n        user?: Record<string, any> & { roomId: string },\n        liveRoom?: Record<string, any> & {status: number, roomId: string}\n    },\n};\n\nexport class FetchRoomInfoFromApiLiveRoute extends Route<FetchRoomInfoFromApiRouteParams, FetchRoomInfoFromApiRouteResponse> {\n\n    async call({ uniqueId }): Promise<FetchRoomInfoFromApiRouteResponse> {\n\n        // Fetch object from TikTok API\n        const roomData = await this.webClient.getJsonObjectFromTikTokApi<FetchRoomInfoFromApiRouteResponse>('api-live/user/room/', {\n            ...this.webClient.clientParams,\n            uniqueId: uniqueId,\n            sourceType: '54'\n        });\n\n        // Check if the response is valid\n        if (roomData.statusCode) {\n            throw new InvalidResponseError(`API Error ${roomData.statusCode} (${roomData.message || 'Unknown Error'})`, undefined);\n        }\n\n        if (!roomData?.data?.user?.roomId) {\n            throw new InvalidResponseError(`Invalid response from API: ${JSON.stringify(roomData)}`, undefined);\n        }\n\n        return roomData;\n    }\n\n}\n"]}