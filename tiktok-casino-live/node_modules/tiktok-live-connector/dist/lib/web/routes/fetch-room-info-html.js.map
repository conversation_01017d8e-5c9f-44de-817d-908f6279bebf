{"version": 3, "file": "fetch-room-info-html.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/routes/fetch-room-info-html.ts"], "names": [], "mappings": ";;;AAAA,yCAAsC;AAQtC,MAAM,YAAY,GAAG,kEAAkE,CAAC;AAGxF,MAAa,0BAA2B,SAAQ,aAA2E;IAEvH,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE;QACnB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,IAAI,QAAQ,OAAO,CAAC,CAAC;QAEhF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;SACjG;QAED,IAAI,SAAc,CAAC;QACnB,IAAI;YACA,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACpC;QAAC,OAAO,CAAC,EAAE;YACR,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;SAC/F;QAED,MAAM,QAAQ,GAAG,SAAS,EAAE,QAAQ,CAAC;QACrC,IAAI,CAAC,QAAQ,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;SAC7E;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;CAEJ;AAzBD,gEAyBC", "sourcesContent": ["import { Route } from '@/types/route';\nimport { FetchRoomInfoFromApiRouteResponse } from '@/lib/web/routes/fetch-room-info-api-live';\n\nexport type FetchRoomInfoFromHtmlRouteParams = { uniqueId: string };\nexport type FetchRoomInfoFromHtmlRouteResponse = Record<string, any> & {\n    liveRoomUserInfo?: FetchRoomInfoFromApiRouteResponse['data']\n};\n\nconst SIGI_PATTERN = /<script id=\"SIGI_STATE\" type=\"application\\/json\">(.*?)<\\/script>/;\n\n\nexport class FetchRoomInfoFromHtmlRoute extends Route<FetchRoomInfoFromHtmlRouteParams, FetchRoomInfoFromHtmlRouteResponse> {\n\n    async call({ uniqueId }): Promise<FetchRoomInfoFromHtmlRouteResponse> {\n        const html = await this.webClient.getHtmlFromTikTokWebsite(`@${uniqueId}/live`);\n\n        const match = html.match(SIGI_PATTERN);\n        if (!match || match.length < 2) {\n            throw new Error('Failed to extract the SIGI_STATE HTML tag, you might be blocked by TikTok.');\n        }\n\n        let sigiState: any;\n        try {\n            sigiState = JSON.parse(match[1]);\n        } catch (e) {\n            throw new Error('Failed to parse SIGI_STATE into JSON. Are you captcha-blocked by TikTok?');\n        }\n\n        const liveRoom = sigiState?.LiveRoom;\n        if (!liveRoom) {\n            throw new Error('Failed to extract the LiveRoom object from SIGI_STATE.');\n        }\n\n        return liveRoom;\n    }\n\n}\n"]}