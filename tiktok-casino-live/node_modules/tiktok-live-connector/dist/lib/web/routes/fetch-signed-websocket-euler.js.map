{"version": 3, "file": "fetch-signed-websocket-euler.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/routes/fetch-signed-websocket-euler.ts"], "names": [], "mappings": ";;;;;;AAAA,yCAAsC;AAEtC,2CAOwB;AACxB,0DAAkC;AAClC,+BAA2C;AAO3C,MAAa,kCAAmC,SAAQ,aAAgE;IAEpH,KAAK,CAAC,IAAI,CACN,EACI,MAAM,EACN,QAAQ,EACR,iBAAiB,EACjB,SAAS,EAC8B;QAG3C,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE;YACtB,MAAM,IAAI,mDAA0C,CAChD,6CAA6C,CAChD,CAAC;SACL;QAED,IAAI,MAAM,IAAI,QAAQ,EAAE;YACpB,MAAM,IAAI,mDAA0C,CAChD,+DAA+D,CAClE,CAAC;SACL;QAED,MAAM,sBAAsB,GAAG,iBAAiB,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;QACpE,MAAM,iBAAiB,GAAG,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC;QAE1E,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,cAAc,IAAI,iBAAiB,EAAE;YAClE,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC;YACpE,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;YAEnF,IAAI,CAAC,OAAO,EAAE;gBACV,MAAM,IAAI,8CAAqC,CAC3C,oIAAoI,CACvI,CAAC;aACL;YAED,IAAI,OAAO,KAAK,YAAY,EAAE;gBAC1B,MAAM,IAAI,8CAAqC,CAC3C,wDAAwD,OAAO,sCAAsC,YAAY,IAAI,CACxH,CAAC;aACL;SAEJ;QAED,IAAI,QAAoC,CAAC;QACzC,IAAI;YACA,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,CAC7D,aAAa,EACb,MAAM,EACN,QAAQ,EACR,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,MAAM,EACnC,iBAAiB,EACjB,gBAAM,CAAC,2BAA2B,CAAC,YAAY,CAAC,EAChD,sBAAsB,EACtB,EAAE,YAAY,EAAE,aAAa,EAAE,CAC3B,CAAC;SACZ;QAAC,OAAO,GAAQ,EAAE;YACf,MAAM,IAAI,qBAAY,CAAC,oBAAW,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,mCAAmC,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;SAC3H;QAED,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;YACzB,8BAA8B;YAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAQ,CAAC;YAC7E,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC;YAChF,MAAM,KAAK,GAAG,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YAChE,MAAM,IAAI,gCAAuB,CAAC,OAAO,EAAE,GAAG,KAAK,gDAAgD,EAAE,QAAQ,CAAC,CAAC;SAClH;QAED,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;YACzB,8BAA8B;YAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAQ,CAAC;YAC7E,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC;YAChF,MAAM,IAAI,4BAAmB,CAAC,OAAO,EAAE,4CAA4C,CAAC,CAAC;SACxF;QAED,MAAM,KAAK,GAAuB,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACzG,MAAM,OAAO,GAAuB,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEnE,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;YACzB,IAAI,OAAe,CAAC;YACpB,IAAI;gBACA,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;aAC1D;YAAC,MAAM;gBACJ,OAAO,GAAG,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC;aACxC;YAED,MAAM,IAAI,qBAAY,CAClB,oBAAW,CAAC,YAAY,EACxB,KAAK,EACL,OAAO,EACP,iCAAiC,QAAQ,CAAC,MAAM,eAAe,OAAO,EAAE,CAC3E,CAAC;SACL;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACtC,MAAM,IAAI,qBAAY,CAClB,oBAAW,CAAC,aAAa,EACzB,KAAK,EACL,OAAO,EACP,uCAAuC,CAC1C,CAAC;SACL;QAED,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,sBAAsB,CAAC,QAAQ,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3F,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QAC/E,OAAO,IAAA,wBAAkB,EAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7E,CAAC;CAEJ;AA5GD,gFA4GC", "sourcesContent": ["import { Route } from '@/types/route';\nimport { AxiosResponse } from 'axios';\nimport {\n    AuthenticatedWebSocketConnectionError,\n    ErrorReason,\n    FetchSignedWebSocketIdentityParameterError,\n    PremiumFeatureError,\n    SignAPIError,\n    SignatureRateLimitError\n} from '@/types/errors';\nimport Config from '@/lib/config';\nimport { deserializeMessage } from '@/lib';\nimport { WebcastResponse } from '@/types/tiktok-schema';\nimport { FetchSignedWebSocketParams } from '@/types/client';\n\n\nexport type FetchSignedWebSocketFromEulerRouteParams = FetchSignedWebSocketParams;\n\nexport class FetchSignedWebSocketFromEulerRoute extends Route<FetchSignedWebSocketFromEulerRouteParams, WebcastResponse> {\n\n    async call(\n        {\n            roomId,\n            uniqueId,\n            preferredAgentIds,\n            sessionId\n        }: FetchSignedWebSocketFromEulerRouteParams\n    ): Promise<WebcastResponse> {\n\n        if (!roomId && !uniqueId) {\n            throw new FetchSignedWebSocketIdentityParameterError(\n                'Either roomId or uniqueId must be provided.'\n            );\n        }\n\n        if (roomId && uniqueId) {\n            throw new FetchSignedWebSocketIdentityParameterError(\n                'Both roomId and uniqueId cannot be provided at the same time.'\n            );\n        }\n\n        const preferredAgentIdsParam = preferredAgentIds?.join(',') ?? null;\n        const resolvedSessionId = sessionId || this.webClient.cookieJar.sessionId;\n\n        if (this.webClient.configuration.authenticateWs && resolvedSessionId) {\n            const envHost = process.env.WHITELIST_AUTHENTICATED_SESSION_ID_HOST;\n            const expectedHost = new URL(this.webClient.webSigner.configuration.basePath).host;\n\n            if (!envHost) {\n                throw new AuthenticatedWebSocketConnectionError(\n                    `authenticate_websocket is true, but no whitelist host defined. Set the env var WHITELIST_AUTHENTICATED_SESSION_ID_HOST to proceed.`\n                );\n            }\n\n            if (envHost !== expectedHost) {\n                throw new AuthenticatedWebSocketConnectionError(\n                    `The env var WHITELIST_AUTHENTICATED_SESSION_ID_HOST \"${envHost}\" does not match sign server host \"${expectedHost}\".`\n                );\n            }\n\n        }\n\n        let response: AxiosResponse<ArrayBuffer>;\n        try {\n            response = await this.webClient.webSigner.webcast.fetchWebcastURL(\n                'ttlive-node',\n                roomId,\n                uniqueId,\n                this.webClient.clientParams?.cursor,\n                resolvedSessionId,\n                Config.DEFAULT_HTTP_CLIENT_HEADERS['User-Agent'],\n                preferredAgentIdsParam,\n                { responseType: 'arraybuffer' }\n            ) as any;\n        } catch (err: any) {\n            throw new SignAPIError(ErrorReason.CONNECT_ERROR, undefined, undefined, 'Failed to connect to sign server.', null, err);\n        }\n\n        if (response.status === 429) {\n            // Convert arraybuffer to JSON\n            const data = JSON.parse(Buffer.from(response.data).toString('utf-8')) as any;\n            const message = process.env.SIGN_SERVER_MESSAGE_DISABLED ? null : data?.message;\n            const label = data?.limit_label ? `(${data.limit_label}) ` : '';\n            throw new SignatureRateLimitError(message, `${label}Too many connections started, try again later.`, response);\n        }\n\n        if (response.status === 402) {\n            // Convert arraybuffer to JSON\n            const data = JSON.parse(Buffer.from(response.data).toString('utf-8')) as any;\n            const message = process.env.SIGN_SERVER_MESSAGE_DISABLED ? null : data?.message;\n            throw new PremiumFeatureError(message, 'Error fetching the signed TikTok WebSocket');\n        }\n\n        const logId: number | undefined = response.headers['X-Log-Id'] && parseInt(response.headers['X-Log-Id']);\n        const agentId: string | undefined = response.headers['X-Agent-ID'];\n\n        if (response.status !== 200) {\n            let payload: string;\n            try {\n                payload = Buffer.from(response.data).toString('utf-8');\n            } catch {\n                payload = `\"${response.statusText}\"`;\n            }\n\n            throw new SignAPIError(\n                ErrorReason.SIGN_NOT_200,\n                logId,\n                agentId,\n                `Unexpected sign server status ${response.status}. Payload:\\n${payload}`,\n            );\n        }\n\n        if (!response.headers['x-set-tt-cookie']) {\n            throw new SignAPIError(\n                ErrorReason.EMPTY_COOKIES,\n                logId,\n                agentId,\n                'No cookies received from sign server.'\n            );\n        }\n\n        this.webClient.cookieJar.processSetCookieHeader(response.headers['x-set-tt-cookie'] || '');\n        this.webClient.roomId = response.headers['x-room-id'] || this.webClient.roomId;\n        return deserializeMessage('WebcastResponse', Buffer.from(response.data));\n    }\n\n}\n"]}