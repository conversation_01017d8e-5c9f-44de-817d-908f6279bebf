{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/lib/web/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,4EAA0D;AAC1D,6CAAkF;AAClF,gFAAmF;AACnF,gGAAmG;AACnG,8EAAiF;AACjF,kFAAqF;AACrF,wFAA0F;AAE1F,+BAA+B;AAC/B,2CAAyB;AACzB,wCAAsB;AAEtB,+CAA+C;AAC/C,MAAa,eAAgB,SAAQ,qBAAiB;IAElD,sBAAsB;IACN,aAAa,CAAqB;IAClC,wBAAwB,CAAgC;IACxD,qBAAqB,CAA6B;IAElE,qBAAqB;IACL,6BAA6B,CAAqC;IAClE,oBAAoB,CAA4B;IAChD,sBAAsB,CAA8B;IACpD,qBAAqB,CAA6B;IAElE,YAAY,GAAG,MAAuD;QAClE,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;QAEjB,IAAI,CAAC,aAAa,GAAG,IAAI,2BAAkB,CAAC,IAAI,CAAC,CAAC;QAClD,IAAI,CAAC,qBAAqB,GAAG,IAAI,iDAA0B,CAAC,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,wBAAwB,GAAG,IAAI,wDAA6B,CAAC,IAAI,CAAC,CAAC;QAExE,IAAI,CAAC,6BAA6B,GAAG,IAAI,iEAAkC,CAAC,IAAI,CAAC,CAAC;QAClF,IAAI,CAAC,oBAAoB,GAAG,IAAI,+CAAyB,CAAC,IAAI,CAAC,CAAC;QAChE,IAAI,CAAC,sBAAsB,GAAG,IAAI,mDAA2B,CAAC,IAAI,CAAC,CAAC;QACpE,IAAI,CAAC,qBAAqB,GAAG,IAAI,mCAA0B,CAAC,IAAI,CAAC,CAAC;IACtE,CAAC;CAEJ;AA1BD,0CA0BC", "sourcesContent": ["import WebcastHttpClient from '@/lib/web/lib/http-client';\nimport { FetchRoomInfoRoute, SendRoomChatFromEulerRoute } from '@/lib/web/routes';\nimport { FetchRoomInfoFromHtmlRoute } from '@/lib/web/routes/fetch-room-info-html';\nimport { FetchSignedWebSocketFromEulerRoute } from '@/lib/web/routes/fetch-signed-websocket-euler';\nimport { FetchRoomIdFromEulerRoute } from '@/lib/web/routes/fetch-room-id-euler';\nimport { FetchRoomInfoFromEulerRoute } from '@/lib/web/routes/fetch-room-info-euler';\nimport { FetchRoomInfoFromApiLiveRoute } from '@/lib/web/routes/fetch-room-info-api-live';\n\n// Export all types and classes\nexport * from './routes';\nexport * from './lib';\n\n// Export a wrapper that brings it all together\nexport class TikTokWebClient extends WebcastHttpClient {\n\n    // TikTok-based routes\n    public readonly fetchRoomInfo: FetchRoomInfoRoute;\n    public readonly fetchRoomInfoFromApiLive: FetchRoomInfoFromApiLiveRoute;\n    public readonly fetchRoomInfoFromHtml: FetchRoomInfoFromHtmlRoute;\n\n    // Euler-based routes\n    public readonly fetchSignedWebSocketFromEuler: FetchSignedWebSocketFromEulerRoute;\n    public readonly fetchRoomIdFromEuler: FetchRoomIdFromEulerRoute;\n    public readonly fetchRoomInfoFromEuler: FetchRoomInfoFromEulerRoute;\n    public readonly sendRoomChatFromEuler: SendRoomChatFromEulerRoute;\n\n    constructor(...params: ConstructorParameters<typeof WebcastHttpClient>) {\n        super(...params);\n\n        this.fetchRoomInfo = new FetchRoomInfoRoute(this);\n        this.fetchRoomInfoFromHtml = new FetchRoomInfoFromHtmlRoute(this);\n        this.fetchRoomInfoFromApiLive = new FetchRoomInfoFromApiLiveRoute(this);\n\n        this.fetchSignedWebSocketFromEuler = new FetchSignedWebSocketFromEulerRoute(this);\n        this.fetchRoomIdFromEuler = new FetchRoomIdFromEulerRoute(this);\n        this.fetchRoomInfoFromEuler = new FetchRoomInfoFromEulerRoute(this);\n        this.sendRoomChatFromEuler = new SendRoomChatFromEulerRoute(this);\n    }\n\n}\n\n\n"]}