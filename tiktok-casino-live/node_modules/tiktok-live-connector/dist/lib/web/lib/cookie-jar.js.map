{"version": 3, "file": "cookie-jar.js", "sourceRoot": "", "sources": ["../../../../src/lib/web/lib/cookie-jar.ts"], "names": [], "mappings": ";;;;;AACA,0DAAkC;AAElC;;;;GAIG;AACH,MAAqB,SAAS;IASN;IACA;IARpB;;;;;OAKG;IACH,YACoB,aAA4B,EAC5B,UAAkC,gBAAM,CAAC,2BAA2B;QADpE,kBAAa,GAAb,aAAa,CAAe;QAC5B,YAAO,GAAP,OAAO,CAA6D;QAEpF,uCAAuC;QACvC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACtD,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAC3B,OAAO,QAAQ,CAAC;QACpB,CAAC,CAAC,CAAC;QAEH,sCAAsC;QACtC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YACpD,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAC5B,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,0DAA0D;QAC1D,OAAO,IAAI,KAAK,CACZ,IAAI,EACJ;YACI,GAAG,CAAC,MAAiB,EAAE,CAAS;gBAC5B,IAAI,CAAC,IAAI,MAAM,EAAE;oBACb,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;iBACpB;qBAAM;oBACH,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBAC5B;YACL,CAAC;YACD,GAAG,CAAC,MAAiB,EAAE,CAAS,EAAE,KAAU;gBACxC,IAAI,KAAK,KAAK,IAAI,EAAE;oBAChB,yCAAyC;oBACzC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;oBACzB,OAAO,IAAI,CAAC;iBACf;gBAED,IAAI,CAAC,IAAI,MAAM,EAAE;oBACb,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;iBACrB;qBAAM;oBACH,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;iBAC7B;gBACD,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,cAAc,CAAC,MAAiB,EAAE,CAAS;gBACvC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACzB,OAAO,IAAI,CAAC;YAChB,CAAC;SACJ,CACJ,CAAC;IACN,CAAC;IAED;;;OAGG;IACH,IAAW,SAAS,CAAC,SAAwB;QACzC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,SAAS,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;IACnI,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,QAAuB;QACtC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QACxD,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;YACjC,8BAA8B;YAC9B,gBAAgB,CAAC,OAAO,CAAC,CAAC,eAAe,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC;SAC/F;aAAM,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;YAC7C,2BAA2B;YAC3B,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;SACjD;IACL,CAAC;IAED;;;OAGG;IACI,aAAa,CAAC,OAA2B;QAC5C,0EAA0E;QAC1E,IAAI,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACtD,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SACpC;QAED,mDAAmD;QACnD,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7E;QAED,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,WAAW,CAAC,GAAW;QAC1B,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,IAAI,CAAC,GAAG;YAAE,OAAO,OAAO,CAAC;QAEzB,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC1B,IAAI,CAAC,CAAC;gBAAE,OAAO;YACf,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACnC,MAAM,UAAU,GAAG,kBAAkB,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YACrD,OAAO,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACnB,CAAC;IAED;;;OAGG;IACI,sBAAsB,CAAC,eAAuB;QACjD,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QACjC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,EAAE,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACxF,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,GAAG,WAAW,CAAC;SAC9D;IACL,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,IAAI,YAAY,GAAG,EAAE,CAAA;QAErB,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,OAAO,EAAE;YAEnC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAA;SACrF;QAED,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;CAEJ;AA7JD,4BA6JC", "sourcesContent": ["import { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\nimport Config from '@/lib/config';\n\n/**\n * Custom cookie jar for axios\n * Because axios-cookiejar-support does not work as expected when using proxy agents\n * https://github.com/zerodytrash/TikTok-Livestream-Chat-Connector/issues/18\n */\nexport default class CookieJar {\n\n    /**\n     * Constructor\n     *\n     * @param axiosInstance The axios instance to attach the cookie jar to\n     * @param cookies The initial cookies to set\n     */\n    constructor(\n        public readonly axiosInstance: AxiosInstance,\n        public readonly cookies: Record<string, string> = Config.DEFAULT_HTTP_CLIENT_COOKIES\n    ) {\n        // Intercept responses to store cookies\n        this.axiosInstance.interceptors.response.use((response) => {\n            this.readCookies(response);\n            return response;\n        });\n\n        // Intercept request to append cookies\n        this.axiosInstance.interceptors.request.use((request) => {\n            this.appendCookies(request);\n            return request;\n        });\n\n        // Return a proxy object to allow direct access to cookies\n        return new Proxy(\n            this,\n            {\n                get(target: CookieJar, p: string): any {\n                    if (p in target) {\n                        return target[p];\n                    } else {\n                        return target.cookies[p];\n                    }\n                },\n                set(target: CookieJar, p: string, value: any): boolean {\n                    if (value === null) {\n                        // Delete the cookie if the value is null\n                        delete target.cookies[p];\n                        return true;\n                    }\n\n                    if (p in target) {\n                        target[p] = value;\n                    } else {\n                        target.cookies[p] = value;\n                    }\n                    return true;\n                },\n                deleteProperty(target: CookieJar, p: string): boolean {\n                    delete target.cookies[p];\n                    return true;\n                }\n            }\n        );\n    }\n\n    /**\n     * Set the session ID\n     * @param sessionId The session ID to set\n     */\n    public set sessionId(sessionId: string | null) {\n        this.cookies['sessionid'] = sessionId;\n        this.cookies['sessionid_ss'] = sessionId;\n        this.cookies['sid_tt'] = sessionId;\n        this.cookies['sid_guard'] = sessionId;\n    }\n\n    /**\n     * Get the session ID\n     */\n    public get sessionId(): string | null {\n        return this.cookies['sessionid'] || this.cookies['sessionid_ss'] || this.cookies['sid_tt'] ||this.cookies['sid_guard'] || null;\n    }\n\n    /**\n     * Read cookies from response headers\n     * @param response The axios response\n     */\n    public readCookies(response: AxiosResponse) {\n        const setCookieHeaders = response.headers['set-cookie'];\n        if (Array.isArray(setCookieHeaders)) {\n            // Multiple set-cookie headers\n            setCookieHeaders.forEach((setCookieHeader) => this.processSetCookieHeader(setCookieHeader));\n        } else if (typeof setCookieHeaders === 'string') {\n            // Single set-cookie header\n            this.processSetCookieHeader(setCookieHeaders);\n        }\n    }\n\n    /**\n     * Append cookies to request headers\n     * @param request The axios request\n     */\n    public appendCookies(request: AxiosRequestConfig) {\n        // We use the capitalized 'Cookie' header, because every browser does that\n        if (request.headers['cookie']) {\n            request.headers['Cookie'] = request.headers['cookie'];\n            delete request.headers['cookie'];\n        }\n\n        // Cookies already set by custom headers? => Append\n        const headerCookie = request.headers['Cookie'];\n        if (typeof headerCookie === 'string') {\n            Object.assign(this.cookies, this.parseCookie(headerCookie), this.cookies);\n        }\n\n        request.headers['Cookie'] = this.getCookieString();\n    }\n\n    /**\n     * Parse cookie string\n     * @param str The cookie string\n     */\n    public parseCookie(str: string): Record<string, string> {\n        const cookies: Record<string, string> = {};\n        if (!str) return cookies;\n\n        str.split('; ').forEach((v) => {\n            if (!v) return;\n            const parts = String(v).split('=');\n            const cookieName = decodeURIComponent(parts.shift());\n            cookies[cookieName] = parts.join('=');\n        });\n\n        return cookies;\n    }\n\n    /**\n     * Process a single set-cookie header\n     * @param setCookieHeader The set-cookie header\n     */\n    public processSetCookieHeader(setCookieHeader: string): void {\n        const nameValuePart = setCookieHeader.split(';')[0];\n        const parts = nameValuePart.split('=');\n        const cookieName = parts.shift();\n        const cookieValue = parts.join('=');\n\n        if (typeof cookieName === 'string' && cookieName !== '' && typeof cookieValue === 'string') {\n            this.cookies[decodeURIComponent(cookieName)] = cookieValue;\n        }\n    }\n\n    /**\n     * Get the cookie string\n     */\n    public getCookieString(): string {\n        let cookieParams = []\n\n        for (const cookieName in this.cookies) {\n\n            cookieParams.push(encodeURIComponent(cookieName) + '=' + this.cookies[cookieName])\n        }\n\n        return cookieParams.join('; ');\n    }\n\n}\n\n"]}