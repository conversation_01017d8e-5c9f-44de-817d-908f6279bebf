{"version": 3, "file": "utilities.js", "sourceRoot": "", "sources": ["../../src/lib/utilities.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oEAAsD;AACtD,yDAA6F;AAO7F,gDAAkC;AAClC,gDAAkC;AAClC,2CAA8E;AAG9E,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzC,MAAM,aAAa,GAA6B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,CAA6B,CAAC;AAE5I,QAAA,wBAAwB,GAA8B;IAC/D,gBAAgB,EAAE,EAAE;CACvB,CAAC;AAEF;;GAEG;AACH,KAAK,UAAU,oBAAoB;IAC/B,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACrC,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,gBAAgB;IAC3B,OAAO,CAAC,MAAM,oBAAoB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;AAC7F,CAAC;AAED,SAAgB,kBAAkB,CAC9B,SAAY,EACZ,aAAqB;IAGrB,MAAM,SAAS,GAA8C,YAAY,CAAC,SAAmB,CAAC,CAAC;IAC/F,IAAI,CAAC,SAAS;QAAE,MAAM,IAAI,+BAAsB,CAAC,wBAAwB,SAAS,EAAE,CAAC,CAAC;IACtF,MAAM,eAAe,GAAsB,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAE3E,yCAAyC;IACzC,IAAI,SAAS,KAAK,iBAAiB,EAAE;QACjC,KAAK,MAAM,OAAO,IAAK,eAAmC,CAAC,QAAQ,IAAI,EAAE,EAAE;YACvE,IAAI,gCAAwB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAClE,SAAS;aACZ;YAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,IAA4B,CAAC,EAAE;gBAC/D,SAAS;aACZ;YAED,MAAM,WAAW,GAAG,OAAO,CAAC,IAAiC,CAAC;YAC9D,OAAO,CAAC,WAAW,GAAG,kBAAkB,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;SACtF;KACJ;IAED,OAAO,eAAe,CAAC;AAC3B,CAAC;AA1BD,gDA0BC;AAGM,KAAK,UAAU,2BAA2B,CAAC,aAAyB;IACvE,uEAAuE;IACvE,yDAAyD;IACzD,MAAM,0BAA0B,GAAG,uCAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACjF,IAAI,eAAe,GAAgC,SAAS,CAAC;IAE7D,IAAI,0BAA0B,CAAC,IAAI,KAAK,KAAK,EAAE;QAC3C,IAAI,MAAM,GAAe,0BAA0B,CAAC,MAAM,CAAC;QAE3D,yCAAyC;QACzC,8CAA8C;QAC9C,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;YAC/F,0BAA0B,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC;SAC3D;QAED,eAAe,GAAG,kBAAkB,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAC;KAC3G;IAED,OAAO;QACH,GAAG,0BAA0B;QAC7B,eAAe;KAClB,CAAC;AAEN,CAAC;AAvBD,kEAuBC;AAED,SAAgB,4BAA4B,CAAC,QAAgB;IACzD,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAC9B,MAAM,IAAI,6BAAoB,CAAC,yFAAyF,CAAC,CAAC;KAC7H;IAED,mBAAmB;IACnB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,yBAAyB,EAAE,EAAE,CAAC,CAAC;IAC3D,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACzC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACrC,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC3B,OAAO,QAAQ,CAAC;AACpB,CAAC;AAXD,oEAWC;AAGD,SAAgB,uBAAuB,CAAC,SAAiB;IACrD,MAAM,UAAU,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1C,MAAM,WAAW,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACvD,MAAM,cAAc,GAAG,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IAE3D,OAAO;QACH,UAAU,EAAE,SAAS;QACrB,YAAY,EAAE,WAAW;QACzB,eAAe,EAAE,cAAc;QAC/B,gBAAgB,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO;QACxE,EAAE,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;KAC1D,CAAC;AACN,CAAC;AAZD,0DAYC;AAED,SAAgB,gBAAgB;IAC5B,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QACzB,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;KAC5C;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAND,4CAMC", "sourcesContent": ["import * as tikTokSchema from '@/types/tiktok-schema';\nimport { MessageFns, WebcastResponse, WebcastWebsocketMessage } from '@/types/tiktok-schema';\nimport {\n    DecodedWebcastWebsocketMessage,\n    IWebcastDeserializeConfig,\n    WebcastEventMessage,\n    WebcastMessage\n} from '@/types/client';\nimport * as zlib from 'node:zlib';\nimport * as util from 'node:util';\nimport { InvalidSchemaNameError, InvalidUniqueIdError } from '@/types/errors';\nimport { DevicePreset } from '@/lib/config';\n\nconst unzip = util.promisify(zlib.unzip);\nconst webcastEvents: (keyof WebcastMessage)[] = Object.keys(tikTokSchema).filter((message) => message.startsWith('Webcast')) as (keyof WebcastMessage)[];\n\nexport const WebcastDeserializeConfig: IWebcastDeserializeConfig = {\n    skipMessageTypes: []\n};\n\n/**\n * Find the messages defined in the TikTok protobuf schema\n */\nasync function getTikTokSchemaNames(): Promise<string[]> {\n    return Object.keys(tikTokSchema);\n}\n\n/**\n * Find the Webcast messages defined in the TikTok protobuf schema\n */\nasync function getWebcastEvents(): Promise<string[]> {\n    return (await getTikTokSchemaNames()).filter((message) => message.startsWith('Webcast'));\n}\n\nexport function deserializeMessage<T extends keyof WebcastMessage>(\n    protoName: T,\n    binaryMessage: Buffer\n): WebcastMessage[T] {\n\n    const messageFn: MessageFns<WebcastMessage[T]> | undefined = tikTokSchema[protoName as string];\n    if (!messageFn) throw new InvalidSchemaNameError(`Invalid schema name: ${protoName}`);\n    const webcastResponse: WebcastMessage[T] = messageFn.decode(binaryMessage);\n\n    // Handle WebcastResponse nested messages\n    if (protoName === 'WebcastResponse') {\n        for (const message of (webcastResponse as WebcastResponse).messages || []) {\n            if (WebcastDeserializeConfig.skipMessageTypes.includes(message.type)) {\n                continue;\n            }\n\n            if (!webcastEvents.includes(message.type as keyof WebcastMessage)) {\n                continue;\n            }\n\n            const messageType = message.type as keyof WebcastEventMessage;\n            message.decodedData = deserializeMessage(messageType, Buffer.from(message.binary));\n        }\n    }\n\n    return webcastResponse;\n}\n\n\nexport async function deserializeWebSocketMessage(binaryMessage: Uint8Array): Promise<DecodedWebcastWebsocketMessage> {\n    // Websocket messages are in a container which contains additional data\n    // Message type 'msg' represents a normal WebcastResponse\n    const rawWebcastWebSocketMessage = WebcastWebsocketMessage.decode(binaryMessage);\n    let webcastResponse: WebcastResponse | undefined = undefined;\n\n    if (rawWebcastWebSocketMessage.type === 'msg') {\n        let binary: Uint8Array = rawWebcastWebSocketMessage.binary;\n\n        // Decompress binary (if gzip compressed)\n        // https://www.rfc-editor.org/rfc/rfc1950.html\n        if (binary && binary.length > 2 && binary[0] === 0x1f && binary[1] === 0x8b && binary[2] === 0x08) {\n            rawWebcastWebSocketMessage.binary = await unzip(binary);\n        }\n\n        webcastResponse = deserializeMessage('WebcastResponse', Buffer.from(rawWebcastWebSocketMessage.binary));\n    }\n\n    return {\n        ...rawWebcastWebSocketMessage,\n        webcastResponse\n    };\n\n}\n\nexport function validateAndNormalizeUniqueId(uniqueId: string) {\n    if (typeof uniqueId !== 'string') {\n        throw new InvalidUniqueIdError('Missing or invalid value for \\'uniqueId\\'. Please provide the username from TikTok URL.');\n    }\n\n    // Support full URI\n    uniqueId = uniqueId.replace('https://www.tiktok.com/', '');\n    uniqueId = uniqueId.replace('/live', '');\n    uniqueId = uniqueId.replace('@', '');\n    uniqueId = uniqueId.trim();\n    return uniqueId;\n}\n\n\nexport function userAgentToDevicePreset(userAgent: string): DevicePreset {\n    const firstSlash = userAgent.indexOf('/');\n    const browserName = userAgent.substring(0, firstSlash);\n    const browserVersion = userAgent.substring(firstSlash + 1);\n\n    return {\n        user_agent: userAgent,\n        browser_name: browserName,\n        browser_version: browserVersion,\n        browser_platform: userAgent.includes('Macintosh') ? 'MacIntel' : 'Win32',\n        os: userAgent.includes('Macintosh') ? 'mac' : 'windows'\n    };\n}\n\nexport function generateDeviceId() {\n    let digits = '';\n    for (let i = 0; i < 19; i++) {\n        digits += Math.floor(Math.random() * 10);\n    }\n    return digits;\n}\n"]}