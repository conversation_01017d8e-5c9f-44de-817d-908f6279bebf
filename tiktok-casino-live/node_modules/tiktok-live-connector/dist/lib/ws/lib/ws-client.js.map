{"version": 3, "file": "ws-client.js", "sourceRoot": "", "sources": ["../../../../src/lib/ws/lib/ws-client.ts"], "names": [], "mappings": ";;;;;AAAA,yCAAgH;AAIhH,yDAA4D;AAC5D,+CAA8D;AAC9D,0DAAkC;AAiBlC,MAAqB,cAAe,SAAS,kBAAkC;IASpD;IAGT;IAXP,UAAU,CAA6B;IACpC,YAAY,CAAwB;IACpC,SAAS,CAAyB;IAClC,eAAe,CAAS;IAElC,YACI,KAAa,EACb,SAAoB,EACD,eAAuC,EAC1D,gBAAwC,EACxC,gBAAqC,EAC3B,0BAAkC,KAAK;QAEjD,KAAK,EAAE,CAAC;QALW,oBAAe,GAAf,eAAe,CAAwB;QAGhD,4BAAuB,GAAvB,uBAAuB,CAAgB;QAIjD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,GAAG,KAAK,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,gBAAM,CAAC,yCAAyC,EAAE,CAAC;QAClI,IAAI,CAAC,SAAS,GAAG,EAAE,MAAM,EAAE,SAAS,CAAC,eAAe,EAAE,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAAC;QACtF,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,EAAE,WAAW,gBAAM,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;IAClH,CAAC;IAES,SAAS,CAAC,YAAiC;QACjD,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC;QAC/B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrF,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,CAAC;IAES,YAAY;QAClB,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACO,KAAK,CAAC,SAAS,CAAC,OAAyB;QAE/C,yDAAyD;QACzD,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;SAChD;QAED,uCAAuC;QACvC,IAAI;YACA,IAAI,gBAAgB,GAAmC,MAAM,IAAA,uCAA2B,EAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAE7G,qCAAqC;YACrC,IAAI,gBAAgB,CAAC,EAAE,IAAI,IAAI,EAAE;gBAC7B,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;aACrC;YAED,+CAA+C;YAC/C,IAAI,gBAAgB,CAAC,eAAe,IAAI,IAAI,EAAE;gBAC1C,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,eAAe,CAAC,CAAC;aAClE;SAEJ;QAAC,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;SAC3C;IAEL,CAAC;IAED;;OAEG;IACO,QAAQ;QACd,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;;OAGG;IACO,OAAO,CAAC,EAAU;QACxB,MAAM,UAAU,GAAiB,mCAAmB,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QACjF,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACI,KAAK;QAER,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;YAEpC,2BAA2B;YAC3B,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aAC/B;YACD,gCAAgC;iBAC3B;gBACD,OAAO,EAAE,CAAC;aACb;QAEL,CAAC,CAAC,CAAC;IAEP,CAAC;CACJ;AA1GD,iCA0GC", "sourcesContent": ["import { client as WebSocket, connection as WebSocketConnection, Message as WebSocketMessage } from 'websocket';\nimport * as http from 'node:http';\nimport { BinaryWriter } from '@bufbuild/protobuf/wire';\nimport { DecodedWebcastWebsocketMessage } from '@/types/client';\nimport { WebcastWebsocketAck } from '@/types/tiktok-schema';\nimport { deserializeWebSocketMessage } from '@/lib/utilities';\nimport Config from '@/lib/config';\nimport TypedEventEmitter from 'typed-emitter';\nimport CookieJar from '@/lib/web/lib/cookie-jar';\n\n\ntype EventMap = {\n    connect: (connection: WebSocketConnection) => void;\n    close: () => void;\n    messageDecodingFailed: (error: Error) => void;\n    unknownResponse: (message: WebSocketMessage) => void;\n    webcastResponse: (response: any) => void;\n};\n\ntype TypedWebSocket = WebSocket & TypedEventEmitter<EventMap>;\ntype WebSocketConstructor = new () => TypedWebSocket;\n\n\nexport default class TikTokWsClient extends (WebSocket as WebSocketConstructor) {\n    public connection: WebSocketConnection | null;\n    protected pingInterval: NodeJS.Timeout | null;\n    protected wsHeaders: Record<string, string>;\n    protected wsUrlWithParams: string;\n\n    constructor(\n        wsUrl: string,\n        cookieJar: CookieJar,\n        protected readonly webSocketParams: Record<string, string>,\n        webSocketHeaders: Record<string, string>,\n        webSocketOptions: http.RequestOptions,\n        protected webSocketPingIntervalMs: number = 10000\n    ) {\n        super();\n\n        this.pingInterval = null;\n        this.connection = null;\n        this.wsUrlWithParams = `${wsUrl}?${new URLSearchParams(this.webSocketParams)}${Config.DEFAULT_WS_CLIENT_PARAMS_APPEND_PARAMETER}`;\n        this.wsHeaders = { Cookie: cookieJar.getCookieString(), ...(webSocketHeaders || {}) };\n        this.on('connect', this.onConnect.bind(this));\n        this.connect(this.wsUrlWithParams, '', `https://${Config.TIKTOK_HOST_WEB}`, this.wsHeaders, webSocketOptions);\n    }\n\n    protected onConnect(wsConnection: WebSocketConnection) {\n        this.connection = wsConnection;\n        this.pingInterval = setInterval(() => this.sendPing(), this.webSocketPingIntervalMs);\n        this.connection.on('message', this.onMessage.bind(this));\n        this.connection.on('close', this.onDisconnect.bind(this));\n    }\n\n    protected onDisconnect() {\n        clearInterval(this.pingInterval);\n        this.pingInterval = null;\n        this.connection = null;\n        this.emit('close');\n    }\n\n    /**\n     * Handle incoming messages\n     * @param message The incoming WebSocket message\n     * @protected\n     */\n    protected async onMessage(message: WebSocketMessage) {\n\n        // If the message is not binary, emit an unknown response\n        if (message.type !== 'binary') {\n            return this.emit('unknownResponse', message);\n        }\n\n        //  If the message is binary, decode it\n        try {\n            let decodedContainer: DecodedWebcastWebsocketMessage = await deserializeWebSocketMessage(message.binaryData);\n\n            // Always send an ACK for the message\n            if (decodedContainer.id != null) {\n                this.sendAck(decodedContainer.id);\n            }\n\n            // If the message is a WebcastResponse, emit it\n            if (decodedContainer.webcastResponse != null) {\n                this.emit('webcastResponse', decodedContainer.webcastResponse);\n            }\n\n        } catch (err) {\n            this.emit('messageDecodingFailed', err);\n        }\n\n    }\n\n    /**\n     * Static Keep-Alive ping\n     */\n    protected sendPing() {\n        this.connection.sendBytes(Buffer.from('3A026862', 'hex'));\n    }\n\n    /**\n     * Message Acknowledgement\n     * @param id The message id to acknowledge\n     */\n    protected sendAck(id: string): void {\n        const ackMessage: BinaryWriter = WebcastWebsocketAck.encode({ type: 'ack', id });\n        this.connection.sendBytes(Buffer.from(ackMessage.finish()));\n    }\n\n    /**\n     * Close the WebSocket connection\n     */\n    public close(): Promise<void> {\n\n        return new Promise((resolve) => {\n            this.once(\"close\", () => resolve());\n\n            // If connected, disconnect\n            if (this.connection) {\n                this.connection.close(1000);\n            }\n            // Otherwise immediately resolve\n            else {\n                resolve();\n            }\n\n        });\n\n    }\n}\n\n"]}