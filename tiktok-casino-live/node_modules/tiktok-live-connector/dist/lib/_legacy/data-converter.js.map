{"version": 3, "file": "data-converter.js", "sourceRoot": "", "sources": ["../../../src/lib/_legacy/data-converter.js"], "names": [], "mappings": ";;;AAAA;;;;GAIG;AACH,SAAgB,cAAc,CAAC,aAAa;IACxC,IAAI,aAAa,CAAC,eAAe,EAAE;QAC/B,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;QAC5D,OAAO,aAAa,CAAC,eAAe,CAAC;KACxC;IAED,IAAI,aAAa,CAAC,IAAI,EAAE;QACpB,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,iBAAiB,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;QACpE,OAAO,aAAa,CAAC,IAAI,CAAC;KAC7B;IAED,IAAI,aAAa,CAAC,KAAK,EAAE;QACrB,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;QACtE,OAAO,aAAa,CAAC,KAAK,CAAC;KAC9B;IAED,IAAI,aAAa,CAAC,YAAY,EAAE;QAC5B,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;QACzD,OAAO,aAAa,CAAC,YAAY,CAAC;KACrC;IAED,IAAI,aAAa,CAAC,UAAU,EAAE;QAC1B,aAAa,CAAC,UAAU,GAAG,sBAAsB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;KAC/E;IAED,IAAI,aAAa,CAAC,WAAW,EAAE;QAC3B,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACvC,IAAI,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE;gBACzB,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;aAC9D;QACL,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC;KAC3C;IAED,IAAI,aAAa,CAAC,WAAW,EAAE;QAC3B,aAAa,CAAC,YAAY,GAAG,EAAE,CAAC;QAChC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YAC7C,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC5C,IAAI,KAAK,GAAG;oBACR,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE;oBAC5C,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC;oBACpC,YAAY,EAAE,EAAE;iBACnB,CAAC;gBAEF,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBAC/B,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;gBACrD,CAAC,CAAC,CAAC;gBAEH,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC,WAAW,CAAC;KACpC;IAED,IAAI,aAAa,CAAC,MAAM,EAAE;QACtB,qBAAqB;QACrB,aAAa,CAAC,SAAS,GAAG,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC;QAEpD,iEAAiE;QACjE,sBAAsB;QACtB,aAAa,CAAC,IAAI,GAAG;YACjB,OAAO,EAAE,aAAa,CAAC,MAAM;YAC7B,YAAY,EAAE,aAAa,CAAC,WAAW;YACvC,UAAU,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,SAAS,EAAE,aAAa,CAAC,WAAW,EAAE,QAAQ;SACjD,CAAC;QAEF,IAAI,aAAa,CAAC,WAAW,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC;YACxD,OAAO,aAAa,CAAC,WAAW,CAAC;SACpC;QAED,IAAI,aAAa,CAAC,SAAS,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;YACtD,OAAO,aAAa,CAAC,SAAS,CAAC;SAClC;QAED,IAAI,aAAa,CAAC,SAAS,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;YACtD,OAAO,aAAa,CAAC,SAAS,CAAC;YAE/B,IAAI,aAAa,CAAC,cAAc,EAAE;gBAC9B,aAAa,CAAC,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;aAC1E;YAED,IAAI,aAAa,CAAC,SAAS,EAAE;gBACzB,aAAa,CAAC,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;aAC/D;SACJ;QAED,IAAI,aAAa,CAAC,OAAO,EAAE;YACvB,aAAa,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;SAC5D;QAED,IAAI,OAAO,aAAa,CAAC,YAAY,KAAK,QAAQ,IAAI,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACjG,IAAI;gBACA,aAAa,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;aACvE;YAAC,OAAO,GAAG,EAAE;aACb;SACJ;KACJ;IAED,IAAI,aAAa,CAAC,KAAK,EAAE;QACrB,aAAa,CAAC,OAAO,GAAG,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC;QACrD,aAAa,CAAC,aAAa,GAAG,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC;QACnE,OAAO,aAAa,CAAC,KAAK,CAAC;KAC9B;IAED,IAAI,aAAa,CAAC,MAAM,EAAE;QACtB,aAAa,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YAClD,OAAO;gBACH,OAAO,EAAE,CAAC,CAAC,KAAK,EAAE,OAAO;gBACzB,aAAa,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ;gBACvC,cAAc,EAAE,CAAC,CAAC,cAAc;aACnC,CAAC;QACN,CAAC,CAAC,CAAC;KACN;IAED,IAAI,aAAa,CAAC,eAAe,EAAE;QAC/B,YAAY;QACZ,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,iBAAiB,CAAC,aAAa,CAAC,eAAe,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;QACnH,OAAO,aAAa,CAAC,eAAe,CAAC;KACxC;IAED,IAAI,aAAa,CAAC,eAAe,EAAE;QAC/B,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,eAAe,CAAC,CAAC;QAC5D,OAAO,aAAa,CAAC,eAAe,CAAC;QACrC,aAAa,CAAC,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KAC/D;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;AAC5C,CAAC;AAtID,wCAsIC;AAED,SAAS,iBAAiB,CAAC,WAAW;IAClC,IAAI,cAAc,GAAG;QACjB,MAAM,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;QACtC,MAAM,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;QACtC,QAAQ,EAAE,WAAW,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;QACxE,QAAQ,EAAE,WAAW,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;QACxE,iBAAiB,EAAE,yBAAyB,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC;QAC9E,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,YAAY;QAChD,UAAU,EAAE,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC;QACzC,cAAc,EAAE,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,cAAc,IAAI,CAAC,CAAC;QACtE,WAAW,EAAE;YACT,UAAU,EAAE,WAAW,CAAC,UAAU,EAAE,QAAQ,EAAE;YAC9C,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,kBAAkB,EAAE,WAAW,CAAC,cAAc,EAAE,IAAI;SACvD;KACJ,CAAC;IAEF,IAAI,WAAW,CAAC,UAAU,EAAE;QACxB,cAAc,CAAC,UAAU,GAAG;YACxB,cAAc,EAAE,WAAW,CAAC,UAAU,CAAC,cAAc;YACrD,aAAa,EAAE,WAAW,CAAC,UAAU,CAAC,aAAa;YACnD,YAAY,EAAE,WAAW,CAAC,UAAU,CAAC,YAAY;YACjD,UAAU,EAAE,WAAW,CAAC,UAAU,CAAC,UAAU;SAChD,CAAC;KACL;IAED,2BAA2B;IAC3B,gCAAgC;IAChC,mCAAmC;IAEnC,cAAc,CAAC,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,cAAc,KAAK,CAAC,CAAC,CAAC;IACrJ,cAAc,CAAC,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;IACxH,cAAc,CAAC,YAAY,GAAG,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,cAAc,KAAK,CAAC,IAAI,CAAC,CAAC,cAAc,KAAK,CAAC,CAAC,CAAC;IAC1K,cAAc,CAAC,aAAa;QACxB,cAAc,CAAC,UAAU;aACpB,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;YAC9D,EAAE,GAAG,CAAC,KAAK,CAAC,yCAAyC,CAAC;YACtD,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;IAEjC,cAAc,CAAC,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,2BAA2B;IACnI,cAAc,CAAC,eAAe,GAAG,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,KAAK,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,sBAAsB;IAEnI,OAAO,cAAc,CAAC;AAC1B,CAAC;AAED,SAAgB,kBAAkB,CAAC,KAAK;IACpC,IAAI,KAAK,CAAC,KAAK;QAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;IACtD,IAAI,KAAK,CAAC,UAAU;QAAE,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;IACrE,OAAO,KAAK,CAAC;AACjB,CAAC;AAJD,gDAIC;AAED,SAAgB,sBAAsB,CAAC,UAAU;IAC7C,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;QAC7B,OAAO;YACH,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;YACzD,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/D,CAAC;IACN,CAAC,CAAC,CAAC;AACP,CAAC;AAPD,wDAOC;AAED,SAAgB,SAAS,CAAC,MAAM;IAC5B,IAAI,gBAAgB,GAAG,EAAE,CAAC;IAE1B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACvB,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;YAC3B,IAAI,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;YAEhD,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;gBACnC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACjC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,cAAc,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;gBACpE,CAAC,CAAC,CAAC;aACN;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;gBACxC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACtC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE;wBACzC,gBAAgB,CAAC,IAAI,CAAC;4BAClB,IAAI,EAAE,OAAO;4BACb,cAAc;4BACd,WAAW,EAAE,KAAK,CAAC,WAAW;4BAC9B,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG;yBACvB,CAAC,CAAC;qBACN;gBACL,CAAC,CAAC,CAAC;aACN;YAED,IAAI,WAAW,CAAC,iBAAiB,EAAE,KAAK,IAAI,WAAW,CAAC,iBAAiB,EAAE,KAAK,KAAK,GAAG,EAAE;gBACtF,gBAAgB,CAAC,IAAI,CAAC;oBAClB,IAAI,EAAE,WAAW;oBACjB,WAAW,EAAE,WAAW,CAAC,iBAAiB,CAAC,WAAW;oBACtD,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC,iBAAiB,CAAC,KAAK,CAAC;oBACpD,cAAc,EAAE,WAAW,CAAC,cAAc;iBAC7C,CAAC,CAAC;aACN;QACL,CAAC,CAAC,CAAC;KACN;IAED,OAAO,gBAAgB,CAAC;AAC5B,CAAC;AAtCD,8BAsCC;AAED,SAAgB,yBAAyB,CAAC,WAAW;IACjD,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;QACpE,OAAO,IAAI,CAAC;KACf;IAED,OAAO,CACH,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACrE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACrE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC9C,WAAW,CAAC,CAAC,CAAC,CACjB,CAAC;AACN,CAAC;AAXD,8DAWC", "sourcesContent": ["/**\n * This ugly function brings the nested protobuf objects to a flat level\n * In addition, attributes in \"Long\" format are converted to strings (e.g. UserIds)\n * This makes it easier to handle the data later, since some libraries have problems to serialize this protobuf specific data.\n */\nexport function simplifyObject(webcastObject) {\n    if (webcastObject.questionDetails) {\n        Object.assign(webcastObject, webcastObject.questionDetails);\n        delete webcastObject.questionDetails;\n    }\n\n    if (webcastObject.user) {\n        Object.assign(webcastObject, getUserAttributes(webcastObject.user));\n        delete webcastObject.user;\n    }\n\n    if (webcastObject.event) {\n        Object.assign(webcastObject, getEventAttributes(webcastObject.event));\n        delete webcastObject.event;\n    }\n\n    if (webcastObject.eventDetails) {\n        Object.assign(webcastObject, webcastObject.eventDetails);\n        delete webcastObject.eventDetails;\n    }\n\n    if (webcastObject.topViewers) {\n        webcastObject.topViewers = getTopViewerAttributes(webcastObject.topViewers);\n    }\n\n    if (webcastObject.battleUsers) {\n        let battleUsers = [];\n        webcastObject.battleUsers.forEach((user) => {\n            if (user?.battleGroup?.user) {\n                battleUsers.push(getUserAttributes(user.battleGroup.user));\n            }\n        });\n\n        webcastObject.battleUsers = battleUsers;\n    }\n\n    if (webcastObject.battleItems) {\n        webcastObject.battleArmies = [];\n        webcastObject.battleItems.forEach((battleItem) => {\n            battleItem.battleGroups.forEach((battleGroup) => {\n                let group = {\n                    hostUserId: battleItem.hostUserId.toString(),\n                    points: parseInt(battleGroup.points),\n                    participants: []\n                };\n\n                battleGroup.users.forEach((user) => {\n                    group.participants.push(getUserAttributes(user));\n                });\n\n                webcastObject.battleArmies.push(group);\n            });\n        });\n\n        delete webcastObject.battleItems;\n    }\n\n    if (webcastObject.giftId) {\n        // Convert to boolean\n        webcastObject.repeatEnd = !!webcastObject.repeatEnd;\n\n        // Add previously used JSON structure (for compatibility reasons)\n        // Can be removed soon\n        webcastObject.gift = {\n            gift_id: webcastObject.giftId,\n            repeat_count: webcastObject.repeatCount,\n            repeat_end: webcastObject.repeatEnd ? 1 : 0,\n            gift_type: webcastObject.giftDetails?.giftType\n        };\n\n        if (webcastObject.giftDetails) {\n            Object.assign(webcastObject, webcastObject.giftDetails);\n            delete webcastObject.giftDetails;\n        }\n\n        if (webcastObject.giftImage) {\n            Object.assign(webcastObject, webcastObject.giftImage);\n            delete webcastObject.giftImage;\n        }\n\n        if (webcastObject.giftExtra) {\n            Object.assign(webcastObject, webcastObject.giftExtra);\n            delete webcastObject.giftExtra;\n\n            if (webcastObject.receiverUserId) {\n                webcastObject.receiverUserId = webcastObject.receiverUserId.toString();\n            }\n\n            if (webcastObject.timestamp) {\n                webcastObject.timestamp = parseInt(webcastObject.timestamp);\n            }\n        }\n\n        if (webcastObject.groupId) {\n            webcastObject.groupId = webcastObject.groupId.toString();\n        }\n\n        if (typeof webcastObject.monitorExtra === 'string' && webcastObject.monitorExtra.indexOf('{') === 0) {\n            try {\n                webcastObject.monitorExtra = JSON.parse(webcastObject.monitorExtra);\n            } catch (err) {\n            }\n        }\n    }\n\n    if (webcastObject.emote) {\n        webcastObject.emoteId = webcastObject.emote?.emoteId;\n        webcastObject.emoteImageUrl = webcastObject.emote?.image?.imageUrl;\n        delete webcastObject.emote;\n    }\n\n    if (webcastObject.emotes) {\n        webcastObject.emotes = webcastObject.emotes.map((x) => {\n            return {\n                emoteId: x.emote?.emoteId,\n                emoteImageUrl: x.emote?.image?.imageUrl,\n                placeInComment: x.placeInComment\n            };\n        });\n    }\n\n    if (webcastObject.treasureBoxUser) {\n        // holy crap\n        Object.assign(webcastObject, getUserAttributes(webcastObject.treasureBoxUser?.user2?.user3[0]?.user4?.user || {}));\n        delete webcastObject.treasureBoxUser;\n    }\n\n    if (webcastObject.treasureBoxData) {\n        Object.assign(webcastObject, webcastObject.treasureBoxData);\n        delete webcastObject.treasureBoxData;\n        webcastObject.timestamp = parseInt(webcastObject.timestamp);\n    }\n\n    return Object.assign({}, webcastObject);\n}\n\nfunction getUserAttributes(webcastUser) {\n    let userAttributes = {\n        userId: webcastUser.userId?.toString(),\n        secUid: webcastUser.secUid?.toString(),\n        uniqueId: webcastUser.uniqueId !== '' ? webcastUser.uniqueId : undefined,\n        nickname: webcastUser.nickname !== '' ? webcastUser.nickname : undefined,\n        profilePictureUrl: getPreferredPictureFormat(webcastUser.profilePicture?.urls),\n        followRole: webcastUser.followInfo?.followStatus,\n        userBadges: mapBadges(webcastUser.badges),\n        userSceneTypes: webcastUser.badges?.map((x) => x?.badgeSceneType || 0),\n        userDetails: {\n            createTime: webcastUser.createTime?.toString(),\n            bioDescription: webcastUser.bioDescription,\n            profilePictureUrls: webcastUser.profilePicture?.urls\n        }\n    };\n\n    if (webcastUser.followInfo) {\n        userAttributes.followInfo = {\n            followingCount: webcastUser.followInfo.followingCount,\n            followerCount: webcastUser.followInfo.followerCount,\n            followStatus: webcastUser.followInfo.followStatus,\n            pushStatus: webcastUser.followInfo.pushStatus\n        };\n    }\n\n    // badgeSceneType:1 = ADMIN\n    // badgeSceneType:4 = SUBSCRIBER\n    // badgeSceneType:7 = NEWSUBSCRIBER\n\n    userAttributes.isModerator = userAttributes.userBadges.some((x) => (x.type && x.type.toLowerCase().includes('moderator')) || x.badgeSceneType === 1);\n    userAttributes.isNewGifter = userAttributes.userBadges.some((x) => x.type && x.type.toLowerCase().includes('live_ng_'));\n    userAttributes.isSubscriber = userAttributes.userBadges.some((x) => (x.url && x.url.toLowerCase().includes('/sub_')) || x.badgeSceneType === 4 || x.badgeSceneType === 7);\n    userAttributes.topGifterRank =\n        userAttributes.userBadges\n            .find((x) => x.url && x.url.includes('/ranklist_top_gifter_'))\n            ?.url.match(/(?<=ranklist_top_gifter_)(\\d+)(?=.png)/g)\n            ?.map(Number)[0] ?? null;\n\n    userAttributes.gifterLevel = userAttributes.userBadges.find((x) => x.badgeSceneType === 8)?.level || 0; // BadgeSceneType_UserGrade\n    userAttributes.teamMemberLevel = userAttributes.userBadges.find((x) => x.badgeSceneType === 10)?.level || 0; // BadgeSceneType_Fans\n\n    return userAttributes;\n}\n\nexport function getEventAttributes(event) {\n    if (event.msgId) event.msgId = event.msgId.toString();\n    if (event.createTime) event.createTime = event.createTime.toString();\n    return event;\n}\n\nexport function getTopViewerAttributes(topViewers) {\n    return topViewers.map((viewer) => {\n        return {\n            user: viewer.user ? getUserAttributes(viewer.user) : null,\n            coinCount: viewer.coinCount ? parseInt(viewer.coinCount) : 0\n        };\n    });\n}\n\nexport function mapBadges(badges) {\n    let simplifiedBadges = [];\n\n    if (Array.isArray(badges)) {\n        badges.forEach((innerBadges) => {\n            let badgeSceneType = innerBadges.badgeSceneType;\n\n            if (Array.isArray(innerBadges.badges)) {\n                innerBadges.badges.forEach((badge) => {\n                    simplifiedBadges.push(Object.assign({ badgeSceneType }, badge));\n                });\n            }\n\n            if (Array.isArray(innerBadges.imageBadges)) {\n                innerBadges.imageBadges.forEach((badge) => {\n                    if (badge && badge.image && badge.image.url) {\n                        simplifiedBadges.push({\n                            type: 'image',\n                            badgeSceneType,\n                            displayType: badge.displayType,\n                            url: badge.image.url\n                        });\n                    }\n                });\n            }\n\n            if (innerBadges.privilegeLogExtra?.level && innerBadges.privilegeLogExtra?.level !== '0') {\n                simplifiedBadges.push({\n                    type: 'privilege',\n                    privilegeId: innerBadges.privilegeLogExtra.privilegeId,\n                    level: parseInt(innerBadges.privilegeLogExtra.level),\n                    badgeSceneType: innerBadges.badgeSceneType\n                });\n            }\n        });\n    }\n\n    return simplifiedBadges;\n}\n\nexport function getPreferredPictureFormat(pictureUrls) {\n    if (!pictureUrls || !Array.isArray(pictureUrls) || !pictureUrls.length) {\n        return null;\n    }\n\n    return (\n        pictureUrls.find((x) => x.includes('100x100') && x.includes('.webp')) ||\n        pictureUrls.find((x) => x.includes('100x100') && x.includes('.jpeg')) ||\n        pictureUrls.find((x) => !x.includes('shrink')) ||\n        pictureUrls[0]\n    );\n}\n\n"]}