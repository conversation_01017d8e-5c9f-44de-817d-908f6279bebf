{"version": 3, "file": "legacy-client.js", "sourceRoot": "", "sources": ["../../../src/lib/_legacy/legacy-client.ts"], "names": [], "mappings": ";;;AAEA,iEAA8D;AAC9D,+BAA6C;AAC7C,2CAA4D;AAE5D;;;GAGG;AACH,MAAa,qBAAsB,SAAS,0BAAoF;IAElH,KAAK,CAAC,sBAAsB,CAAC,eAAgC;QAEnE,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,8DAA8D;QAC9D,eAAe,CAAC,QAAQ;aACnB,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACjB,IAAI,aAAa,GAAG,IAAA,+BAAc,EAAC,OAAO,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC;YAC9D,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,YAAY,EAAE,OAAO,CAAC,IAAI,EAAE,aAAa,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAElF,QAAQ,OAAO,CAAC,IAAI,EAAE;gBAClB,KAAK,uBAAuB;oBACxB,yBAAyB;oBACzB,gCAAgC;oBAChC,oDAAoD;oBACpD,MAAM,MAAM,GAAI,OAAO,CAAC,WAAqC,CAAC,MAAM,CAAC;oBACrE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;wBACzB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;wBAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;qBACrB;oBACD,MAAM;gBACV,KAAK,2BAA2B;oBAC5B,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;oBACjD,MAAM;gBACV,KAAK,oBAAoB;oBACrB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;oBAC5C,MAAM;gBACV,KAAK,sBAAsB;oBACvB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;oBAC9C,MAAM;gBACV,KAAK,oBAAoB;oBACrB,2CAA2C;oBAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,aAAa,CAAC,MAAM,EAAE;wBAC5D,aAAa,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,MAAM,CAAC,CAAC;qBACnG;oBACD,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;oBAC5C,MAAM;gBACV,KAAK,sBAAsB;oBACvB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;oBAC9C,IAAI,aAAa,CAAC,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE;wBAC/C,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;qBACjD;oBACD,IAAI,aAAa,CAAC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;wBAC9C,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;qBAChD;oBACD,MAAM;gBACV,KAAK,oBAAoB;oBACrB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;oBAC5C,MAAM;gBACV,KAAK,2BAA2B;oBAC5B,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;oBACpD,MAAM;gBACV,KAAK,sBAAsB;oBACvB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;oBACvD,MAAM;gBACV,KAAK,sBAAsB;oBACvB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;oBACvD,MAAM;gBACV,KAAK,yBAAyB;oBAC1B,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;oBAClD,MAAM;gBACV,KAAK,yBAAyB;oBAC1B,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;oBAC7C,MAAM;gBACV,KAAK,wBAAwB;oBACzB,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;oBAChD,MAAM;gBACV,KAAK,yBAAyB;oBAC1B,IAAI,CAAC,IAAI,CAAC,qBAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;oBACjD,MAAM;aACb;QACL,CAAC,CAAC,CAAC;IACX,CAAC;CAGJ;AA/ED,sDA+EC", "sourcesContent": ["import { WebcastControlMessage, WebcastResponse } from '@/types/tiktok-schema';\nimport { EventEmitter } from 'node:events';\nimport { simplifyObject } from '@/lib/_legacy/data-converter';\nimport { TikTokLiveConnection } from '@/lib';\nimport { ControlEvent, WebcastEvent } from '@/types/events';\n\n/**\n * The legacy WebcastPushConnection class for backwards compatibility.\n * @deprecated Use TikTokLiveConnection instead.\n */\nexport class WebcastPushConnection extends (TikTokLiveConnection as new (...args: any[]) => EventEmitter & TikTokLiveConnection) {\n\n    protected async processWebcastResponse(webcastResponse: WebcastResponse): Promise<void> {\n\n        webcastResponse.messages.forEach((message) => {\n            this.emit(ControlEvent.RAW_DATA, message.type, message.binary);\n        });\n\n        // Process and emit decoded data depending on the message type\n        webcastResponse.messages\n            .forEach((message) => {\n                let simplifiedObj = simplifyObject(message.decodedData || {});\n                this.emit(ControlEvent.DECODED_DATA, message.type, simplifiedObj, message.binary);\n\n                switch (message.type) {\n                    case 'WebcastControlMessage':\n                        // Known control actions:\n                        // 3 = Stream terminated by user\n                        // 4 = Stream terminated by platform moderator (ban)\n                        const action = (message.decodedData as WebcastControlMessage).action;\n                        if ([3, 4].includes(action)) {\n                            this.emit(WebcastEvent.STREAM_END, { action });\n                            this.disconnect();\n                        }\n                        break;\n                    case 'WebcastRoomUserSeqMessage':\n                        this.emit(WebcastEvent.ROOM_USER, simplifiedObj);\n                        break;\n                    case 'WebcastChatMessage':\n                        this.emit(WebcastEvent.CHAT, simplifiedObj);\n                        break;\n                    case 'WebcastMemberMessage':\n                        this.emit(WebcastEvent.MEMBER, simplifiedObj);\n                        break;\n                    case 'WebcastGiftMessage':\n                        // Add extended gift info if option enabled\n                        if (Array.isArray(this.availableGifts) && simplifiedObj.giftId) {\n                            simplifiedObj.extendedGiftInfo = this.availableGifts.find((x) => x.id === simplifiedObj.giftId);\n                        }\n                        this.emit(WebcastEvent.GIFT, simplifiedObj);\n                        break;\n                    case 'WebcastSocialMessage':\n                        this.emit(WebcastEvent.SOCIAL, simplifiedObj);\n                        if (simplifiedObj.displayType?.includes('follow')) {\n                            this.emit(WebcastEvent.FOLLOW, simplifiedObj);\n                        }\n                        if (simplifiedObj.displayType?.includes('share')) {\n                            this.emit(WebcastEvent.SHARE, simplifiedObj);\n                        }\n                        break;\n                    case 'WebcastLikeMessage':\n                        this.emit(WebcastEvent.LIKE, simplifiedObj);\n                        break;\n                    case 'WebcastQuestionNewMessage':\n                        this.emit(WebcastEvent.QUESTION_NEW, simplifiedObj);\n                        break;\n                    case 'WebcastLinkMicBattle':\n                        this.emit(WebcastEvent.LINK_MIC_BATTLE, simplifiedObj);\n                        break;\n                    case 'WebcastLinkMicArmies':\n                        this.emit(WebcastEvent.LINK_MIC_ARMIES, simplifiedObj);\n                        break;\n                    case 'WebcastLiveIntroMessage':\n                        this.emit(WebcastEvent.LIVE_INTRO, simplifiedObj);\n                        break;\n                    case 'WebcastEmoteChatMessage':\n                        this.emit(WebcastEvent.EMOTE, simplifiedObj);\n                        break;\n                    case 'WebcastEnvelopeMessage':\n                        this.emit(WebcastEvent.ENVELOPE, simplifiedObj);\n                        break;\n                    case 'WebcastSubNotifyMessage':\n                        this.emit(WebcastEvent.SUBSCRIBE, simplifiedObj);\n                        break;\n                }\n            });\n    }\n\n\n}\n\n"]}