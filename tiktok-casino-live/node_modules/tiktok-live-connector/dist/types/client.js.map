{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../src/types/client.ts"], "names": [], "mappings": "", "sourcesContent": ["import * as tikTokSchema from './tiktok-schema';\nimport { MessageFns, WebcastResponse, WebcastWebsocketMessage } from './tiktok-schema';\nimport { AxiosRequestConfig } from 'axios';\nimport * as http from 'node:http';\n\nexport type TikTokLiveConnectionOptions = {\n    processInitialData: boolean;\n    fetchRoomInfoOnConnect: boolean;\n    enableExtendedGiftInfo: boolean;\n    enableRequestPolling: boolean;\n    requestPollingIntervalMs: number;\n    sessionId: string | null;\n    signApiKey: string | null;\n    authenticateWs: boolean;\n    preferredAgentIds: string[];\n    connectWithUniqueId: boolean;\n    logFetchFallbackErrors: boolean;\n\n    webClientParams: Record<string, string>;\n    webClientHeaders: Record<string, string>;\n    webClientOptions: AxiosRequestConfig;\n\n    wsClientHeaders: Record<string, string>;\n    wsClientParams: Record<string, string>;\n    wsClientOptions: http.RequestOptions;\n\n    // Override the default websocket provider\n    signedWebSocketProvider?: (props: FetchSignedWebSocketParams) => Promise<WebcastResponse>\n}\n\n\nexport type RoomInfo = Record<string, any> & { status: number }\nexport type RoomGiftInfo = any;\n\nexport type FetchSignedWebSocketParams = {\n    roomId?: string;\n    uniqueId?: string;\n    preferredAgentIds?: string[];\n    sessionId?: string;\n}\n\n\nexport type WebcastHttpClientConfig = {\n    customHeaders: Record<string, string>;\n    axiosOptions: AxiosRequestConfig;\n    clientParams: Record<string, string>;\n    authenticateWs?: boolean;\n    signApiKey?: string;\n}\n\nexport interface TikTokLiveConnectionWebSocketParams extends Record<string, any> {\n    compress?: string;\n}\n\n\nexport type DecodedWebcastWebsocketMessage = WebcastWebsocketMessage & {\n    webcastResponse?: any;\n}\n\n\nexport interface IWebcastConfig {\n    TIKTOK_HOST_WEB: string;\n    TIKTOK_HOST_WEBCAST: string;\n    TIKTOK_HTTP_ORIGIN: string;\n\n    // HTTP Client Options\n    DEFAULT_HTTP_CLIENT_COOKIES: Record<string, string>;\n    DEFAULT_HTTP_CLIENT_PARAMS: Record<string, string>;\n    DEFAULT_HTTP_CLIENT_OPTIONS: AxiosRequestConfig;\n    DEFAULT_WS_CLIENT_PARAMS_APPEND_PARAMETER: string;\n    DEFAULT_HTTP_CLIENT_HEADERS: Record<string, string> & {\n        'User-Agent': string;\n    };\n\n    // WS Client Options\n    DEFAULT_WS_CLIENT_PARAMS: Record<string, string>;\n    DEFAULT_WS_CLIENT_HEADERS: Record<string, string> & {\n        'User-Agent': string;\n    };\n\n}\n\nexport interface IWebcastDeserializeConfig {\n    skipMessageTypes: string[];\n}\n\ntype ExtractMessageType<T> = T extends MessageFns<infer U> ? U : never;\n\n// Messages\nexport type WebcastMessage = {\n    [K in keyof typeof tikTokSchema as ExtractMessageType<typeof tikTokSchema[K]> extends never ? never : K]:\n    ExtractMessageType<typeof tikTokSchema[K]>;\n};\n\n// Top-Level Messages\nexport type WebcastEventMessage = {\n    [K in keyof WebcastMessage as K extends `Webcast${string}` ? K : never]: WebcastMessage[K];\n};\n\ndeclare module '@/types/tiktok-schema' {\n    export interface Message {\n        decodedData?: WebcastEventMessage[keyof WebcastEventMessage];\n    }\n}\n\n\nexport type WebcastWsEvent =\n    string\n    | 'webcastResponse'\n    | 'unknownResponse'\n    | 'messageDecodingFailed'\n    | 'connect'\n    | 'connectFailed'\n    | 'httpResponse';\n\n\nexport type WebcastHttpClientRequestParams = Omit<Omit<AxiosRequestConfig, 'url'>, 'baseURL'> & {\n    host: string;\n    path: string;\n    params?: Record<string, string>;\n    signRequest: boolean;\n};\n\n\n"]}