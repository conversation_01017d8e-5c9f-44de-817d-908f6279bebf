"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebcastEventMap = exports.ConnectState = exports.WebcastEvent = exports.ControlEvent = void 0;
var ControlEvent;
(function (ControlEvent) {
    ControlEvent["CONNECTED"] = "connected";
    ControlEvent["DISCONNECTED"] = "disconnected";
    ControlEvent["ERROR"] = "error";
    ControlEvent["RAW_DATA"] = "rawData";
    ControlEvent["DECODED_DATA"] = "decodedData";
    ControlEvent["WEBSOCKET_CONNECTED"] = "websocketConnected";
})(ControlEvent = exports.ControlEvent || (exports.ControlEvent = {}));
var WebcastEvent;
(function (WebcastEvent) {
    WebcastEvent["CHAT"] = "chat";
    WebcastEvent["MEMBER"] = "member";
    WebcastEvent["GIFT"] = "gift";
    WebcastEvent["ROOM_USER"] = "roomUser";
    WebcastEvent["SOCIAL"] = "social";
    WebcastEvent["LIKE"] = "like";
    WebcastEvent["QUESTION_NEW"] = "questionNew";
    WebcastEvent["LINK_MIC_BATTLE"] = "linkMicBattle";
    WebcastEvent["LINK_MIC_ARMIES"] = "linkMicArmies";
    WebcastEvent["LIVE_INTRO"] = "liveIntro";
    WebcastEvent["EMOTE"] = "emote";
    WebcastEvent["ENVELOPE"] = "envelope";
    WebcastEvent["SUBSCRIBE"] = "subscribe";
    WebcastEvent["FOLLOW"] = "follow";
    WebcastEvent["SHARE"] = "share";
    WebcastEvent["STREAM_END"] = "streamEnd";
})(WebcastEvent = exports.WebcastEvent || (exports.WebcastEvent = {}));
var ConnectState;
(function (ConnectState) {
    ConnectState["DISCONNECTED"] = "DISCONNECTED";
    ConnectState["CONNECTING"] = "CONNECTING";
    ConnectState["CONNECTED"] = "CONNECTED";
})(ConnectState = exports.ConnectState || (exports.ConnectState = {}));
exports.WebcastEventMap = {
    'WebcastChatMessage': WebcastEvent.CHAT,
    'WebcastMemberMessage': WebcastEvent.MEMBER,
    'WebcastRoomUserSeqMessage': WebcastEvent.ROOM_USER,
    'WebcastSocialMessage': WebcastEvent.SOCIAL,
    'WebcastLikeMessage': WebcastEvent.LIKE,
    'WebcastQuestionNewMessage': WebcastEvent.QUESTION_NEW,
    'WebcastLinkMicBattle': WebcastEvent.LINK_MIC_BATTLE,
    'WebcastLinkMicArmies': WebcastEvent.LINK_MIC_ARMIES,
    'WebcastLiveIntroMessage': WebcastEvent.LIVE_INTRO,
    'WebcastEmoteChatMessage': WebcastEvent.EMOTE,
    'WebcastEnvelopeMessage': WebcastEvent.ENVELOPE,
    'WebcastSubNotifyMessage': WebcastEvent.SUBSCRIBE
};
//# sourceMappingURL=events.js.map