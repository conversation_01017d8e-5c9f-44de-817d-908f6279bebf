{"version": 3, "file": "route.js", "sourceRoot": "", "sources": ["../../src/types/route.ts"], "names": [], "mappings": ";;;;;;AAAA,0EAAiD;AAGjD,MAAsB,KAAsB,SAAQ,2BAA2C;IAGpE;IADvB,YACuB,SAA0B;QAE7C,KAAK,CAAC,MAAM,CAAC,CAAC;QAFK,cAAS,GAAT,SAAS,CAAiB;IAGjD,CAAC;CAIJ;AAVD,sBAUC", "sourcesContent": ["import CallableInstance from 'callable-instance';\nimport { TikTokWebClient } from '@/lib';\n\nexport abstract class Route<Args, Response> extends CallableInstance<[Args], Promise<Response>> {\n\n    constructor(\n        protected readonly webClient: TikTokWebClient\n    ) {\n        super('call');\n    }\n\n    abstract call(args: Args): Promise<Response>;\n\n}\n\n"]}