{"version": 3, "file": "events.js", "sourceRoot": "", "sources": ["../../src/types/events.ts"], "names": [], "mappings": ";;;AAoBA,IAAY,YAOX;AAPD,WAAY,YAAY;IACpB,uCAAuB,CAAA;IACvB,6CAA6B,CAAA;IAC7B,+BAAe,CAAA;IACf,oCAAoB,CAAA;IACpB,4CAA4B,CAAA;IAC5B,0DAA0C,CAAA;AAC9C,CAAC,EAPW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAOvB;AAGD,IAAY,YAiBX;AAjBD,WAAY,YAAY;IACpB,6BAAa,CAAA;IACb,iCAAiB,CAAA;IACjB,6BAAa,CAAA;IACb,sCAAsB,CAAA;IACtB,iCAAiB,CAAA;IACjB,6BAAa,CAAA;IACb,4CAA4B,CAAA;IAC5B,iDAAiC,CAAA;IACjC,iDAAiC,CAAA;IACjC,wCAAwB,CAAA;IACxB,+BAAe,CAAA;IACf,qCAAqB,CAAA;IACrB,uCAAuB,CAAA;IACvB,iCAAiB,CAAA;IACjB,+BAAe,CAAA;IACf,wCAAwB,CAAA;AAC5B,CAAC,EAjBW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAiBvB;AAED,IAAY,YAIX;AAJD,WAAY,YAAY;IACpB,6CAA6B,CAAA;IAC7B,yCAAyB,CAAA;IACzB,uCAAuB,CAAA;AAC3B,CAAC,EAJW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAIvB;AAoCY,QAAA,eAAe,GAAkD;IAC1E,oBAAoB,EAAE,YAAY,CAAC,IAAI;IACvC,sBAAsB,EAAE,YAAY,CAAC,MAAM;IAC3C,2BAA2B,EAAE,YAAY,CAAC,SAAS;IACnD,sBAAsB,EAAE,YAAY,CAAC,MAAM;IAC3C,oBAAoB,EAAE,YAAY,CAAC,IAAI;IACvC,2BAA2B,EAAE,YAAY,CAAC,YAAY;IACtD,sBAAsB,EAAE,YAAY,CAAC,eAAe;IACpD,sBAAsB,EAAE,YAAY,CAAC,eAAe;IACpD,yBAAyB,EAAE,YAAY,CAAC,UAAU;IAClD,yBAAyB,EAAE,YAAY,CAAC,KAAK;IAC7C,wBAAwB,EAAE,YAAY,CAAC,QAAQ;IAC/C,yBAAyB,EAAE,YAAY,CAAC,SAAS;CACpD,CAAC", "sourcesContent": ["import {\n    ControlAction,\n    WebcastChatMessage,\n    WebcastControlMessage,\n    WebcastEmoteChatMessage,\n    WebcastEnvelopeMessage,\n    WebcastGiftMessage,\n    WebcastLikeMessage,\n    WebcastLinkMicArmies,\n    WebcastLinkMicBattle,\n    WebcastLiveIntroMessage,\n    WebcastMemberMessage,\n    WebcastQuestionNewMessage,\n    WebcastRoomUserSeqMessage,\n    WebcastSocialMessage,\n    WebcastSubNotifyMessage\n} from '@/types/tiktok-schema';\nimport { RoomGiftInfo, RoomInfo, WebcastMessage } from '@/types/client';\nimport TikTokWsClient from '@/lib/ws/lib/ws-client';\n\nexport enum ControlEvent {\n    CONNECTED = 'connected',\n    DISCONNECTED = 'disconnected',\n    ERROR = 'error',\n    RAW_DATA = 'rawData',\n    DECODED_DATA = 'decodedData',\n    WEBSOCKET_CONNECTED = 'websocketConnected'\n}\n\n\nexport enum WebcastEvent {\n    CHAT = 'chat',\n    MEMBER = 'member',\n    GIFT = 'gift',\n    ROOM_USER = 'roomUser',\n    SOCIAL = 'social',\n    LIKE = 'like',\n    QUESTION_NEW = 'questionNew',\n    LINK_MIC_BATTLE = 'linkMicBattle',\n    LINK_MIC_ARMIES = 'linkMicArmies',\n    LIVE_INTRO = 'liveIntro',\n    EMOTE = 'emote',\n    ENVELOPE = 'envelope',\n    SUBSCRIBE = 'subscribe',\n    FOLLOW = 'follow',\n    SHARE = 'share',\n    STREAM_END = 'streamEnd',\n}\n\nexport enum ConnectState {\n    DISCONNECTED = 'DISCONNECTED',\n    CONNECTING = 'CONNECTING',\n    CONNECTED = 'CONNECTED'\n}\n\nexport type EventHandler<T> = (event: T) => void | Promise<void>;\n\n\nexport type EventMap = {\n    // Message Events\n    [WebcastEvent.CHAT]: EventHandler<WebcastChatMessage>\n    [WebcastEvent.MEMBER]: EventHandler<WebcastMemberMessage>\n    [WebcastEvent.GIFT]: EventHandler<WebcastGiftMessage>,\n    [WebcastEvent.ROOM_USER]: EventHandler<WebcastRoomUserSeqMessage>,\n    [WebcastEvent.SOCIAL]: EventHandler<WebcastSocialMessage>,\n    [WebcastEvent.LIKE]: EventHandler<WebcastLikeMessage>,\n    [WebcastEvent.QUESTION_NEW]: EventHandler<WebcastQuestionNewMessage>,\n    [WebcastEvent.LINK_MIC_BATTLE]: EventHandler<WebcastLinkMicBattle>,\n    [WebcastEvent.LINK_MIC_ARMIES]: EventHandler<WebcastLinkMicArmies>,\n    [WebcastEvent.LIVE_INTRO]: EventHandler<WebcastLiveIntroMessage>,\n    [WebcastEvent.EMOTE]: EventHandler<WebcastEmoteChatMessage>,\n    [WebcastEvent.ENVELOPE]: EventHandler<WebcastEnvelopeMessage>,\n    [WebcastEvent.SUBSCRIBE]: EventHandler<WebcastSubNotifyMessage>,\n    [WebcastEvent.STREAM_END]: (event: {action: ControlAction}) => void | Promise<void>,\n\n    // Custom Events\n    [WebcastEvent.FOLLOW]: EventHandler<WebcastSocialMessage>,\n    [WebcastEvent.SHARE]: EventHandler<WebcastSocialMessage>,\n\n    // Control Events\n    [ControlEvent.CONNECTED]: EventHandler<TikTokLiveConnectionState>,\n    [ControlEvent.DISCONNECTED]: EventHandler<void>,\n    [ControlEvent.ERROR]: EventHandler<any>,\n    [ControlEvent.RAW_DATA]: (type: string, data: Uint8Array) => void | Promise<void>;\n    [ControlEvent.DECODED_DATA]: (type: string, event: any, binary: Uint8Array) => void | Promise<void>;\n    [ControlEvent.WEBSOCKET_CONNECTED]: EventHandler<TikTokWsClient>\n\n};\n\nexport const WebcastEventMap: Partial<Record<keyof WebcastMessage, string>> = {\n    'WebcastChatMessage': WebcastEvent.CHAT,\n    'WebcastMemberMessage': WebcastEvent.MEMBER,\n    'WebcastRoomUserSeqMessage': WebcastEvent.ROOM_USER,\n    'WebcastSocialMessage': WebcastEvent.SOCIAL,\n    'WebcastLikeMessage': WebcastEvent.LIKE,\n    'WebcastQuestionNewMessage': WebcastEvent.QUESTION_NEW,\n    'WebcastLinkMicBattle': WebcastEvent.LINK_MIC_BATTLE,\n    'WebcastLinkMicArmies': WebcastEvent.LINK_MIC_ARMIES,\n    'WebcastLiveIntroMessage': WebcastEvent.LIVE_INTRO,\n    'WebcastEmoteChatMessage': WebcastEvent.EMOTE,\n    'WebcastEnvelopeMessage': WebcastEvent.ENVELOPE,\n    'WebcastSubNotifyMessage': WebcastEvent.SUBSCRIBE\n};\n\n\nexport type TikTokLiveConnectionState = {\n    isConnected: boolean,\n    roomId: string,\n    roomInfo: RoomInfo | null,\n    availableGifts: RoomGiftInfo | null\n};\n"]}