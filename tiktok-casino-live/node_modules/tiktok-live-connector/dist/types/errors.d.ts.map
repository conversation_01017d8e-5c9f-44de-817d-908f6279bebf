{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src/types/errors.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,MAAM,OAAO,CAAC;AAEtC,cAAM,YAAa,SAAQ,KAAK;gBAChB,OAAO,EAAE,MAAM;CAG9B;AAED,qBAAa,oBAAqB,SAAQ,KAAK;CAC9C;AAED,qBAAa,kBAAmB,SAAQ,KAAK;aACb,MAAM,EAAE,KAAK,EAAE;gBAAf,MAAM,EAAE,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE;CAG9D;AAED,qBAAa,gBAAiB,SAAQ,KAAK;aACX,MAAM,EAAE,KAAK,EAAE;gBAAf,MAAM,EAAE,KAAK,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE;CAG9D;AAGD,qBAAa,oBAAqB,SAAQ,KAAK;aAGvB,UAAU,EAAE,KAAK;gBADjC,OAAO,EAAE,MAAM,EACC,UAAU,GAAE,KAAiB;CAKpD;AAED,qBAAa,kBAAmB,SAAQ,KAAK;CAC5C;AAGD,qBAAa,sBAAuB,SAAQ,YAAY;CACvD;AAED,qBAAa,qBAAsB,SAAQ,YAAY;CACtD;AAED,qBAAa,gBAAiB,SAAQ,YAAY;CACjD;AAED,qBAAa,sBAAuB,SAAQ,KAAK;CAChD;AAED,qBAAa,eAAgB,SAAQ,KAAK;gBAC1B,OAAO,EAAE,MAAM;CAI9B;AAED,oBAAY,WAAW;IACnB,UAAU,iBAAiB;IAC3B,aAAa,kBAAkB;IAC/B,aAAa,kBAAkB;IAC/B,YAAY,eAAe;IAC3B,aAAa,kBAAkB;IAC/B,eAAe,oBAAoB;IACnC,gBAAgB,qBAAqB;CACxC;AAED,qBAAa,0CAA2C,SAAQ,KAAK;CACpE;AAED,qBAAa,YAAa,SAAQ,eAAe;IACtC,MAAM,EAAE,WAAW,CAAC;IAC3B,SAAgB,KAAK,CAAC,EAAE,MAAM,CAAC;IAC/B,SAAgB,OAAO,CAAC,EAAE,MAAM,CAAC;gBAG7B,MAAM,EAAE,WAAW,EACnB,KAAK,CAAC,EAAE,MAAM,EACd,OAAO,CAAC,EAAE,MAAM,EAChB,GAAG,IAAI,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE;WAQrB,uBAAuB,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM;CAcjE;AAED,qBAAa,uBAAwB,SAAQ,YAAY;IACrD,SAAgB,UAAU,EAAE,MAAM,CAAC;IACnC,SAAgB,SAAS,CAAC,EAAE,MAAM,CAAC;gBAEvB,UAAU,EAAE,MAAM,GAAG,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa;IAoBtF,OAAO,CAAC,MAAM,CAAC,iBAAiB;IAIhC,OAAO,CAAC,MAAM,CAAC,mBAAmB;IAKlC,OAAO,CAAC,MAAM,CAAC,kBAAkB;CAKpC;AAED,qBAAa,2BAA4B,SAAQ,YAAY;gBAC7C,GAAG,IAAI,EAAE,MAAM,EAAE;CAGhC;AAED,qBAAa,mBAAoB,SAAQ,YAAY;gBACrC,UAAU,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,MAAM,EAAE;CAIpD;AAED,qBAAa,qCAAsC,SAAQ,YAAY;gBACvD,GAAG,IAAI,EAAE,MAAM,EAAE;CAGhC"}