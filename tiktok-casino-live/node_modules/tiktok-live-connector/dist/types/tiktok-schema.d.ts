import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
export declare const protobufPackage = "TikTok";
export declare enum ControlAction {
    CONTROL_ACTION_FALLBACK_UNKNOWN = 0,
    CONTROL_ACTION_STREAM_PAUSED = 1,
    CONTROL_ACTION_STREAM_UNPAUSED = 2,
    CONTROL_ACTION_STREAM_ENDED = 3,
    CONTROL_ACTION_STREAM_SUSPENDED = 4,
    UNRECOGNIZED = -1
}
export declare function controlActionFromJSON(object: any): ControlAction;
export declare function controlActionToJSON(object: ControlAction): string;
/** Data structure from im/fetch/ response */
export interface WebcastResponse {
    messages: Message[];
    cursor: string;
    fetchInterval: number;
    serverTimestamp: string;
    internalExt: string;
    /** ws (1) or polling (2) */
    fetchType: number;
    wsParams: WebsocketParam[];
    heartbeatDuration: number;
    needAck: boolean;
    wsUrl: string;
}
export interface Message {
    type: string;
    binary: Uint8Array;
}
export interface WebsocketParam {
    name: string;
    value: string;
}
/** Message types depending on Message.tyoe */
export interface WebcastControlMessage {
    action: ControlAction;
}
/** Statistics like viewer count */
export interface WebcastRoomUserSeqMessage {
    topViewers: TopUser[];
    viewerCount: number;
}
export interface TopUser {
    coinCount: string;
    user: User | undefined;
}
export interface WebcastChatMessage {
    event: WebcastMessageEvent | undefined;
    user: User | undefined;
    comment: string;
    emotes: WebcastSubEmote[];
}
/** Chat Emotes (Subscriber) */
export interface WebcastEmoteChatMessage {
    user: User | undefined;
    emote: EmoteDetails | undefined;
}
export interface WebcastSubEmote {
    /** starting at 0, you insert the emote itself into the comment at that place */
    placeInComment: number;
    emote: EmoteDetails | undefined;
}
export interface WebcastMemberMessage {
    event: WebcastMessageEvent | undefined;
    user: User | undefined;
    actionId: number;
}
export interface WebcastGiftMessage {
    event: WebcastMessageEvent | undefined;
    giftId: number;
    repeatCount: number;
    user: User | undefined;
    repeatEnd: number;
    groupId: string;
    giftDetails: WebcastGiftMessageGiftDetails | undefined;
    monitorExtra: string;
    giftExtra: WebcastGiftMessageGiftExtra | undefined;
}
export interface WebcastGiftMessageGiftDetails {
    giftImage: WebcastGiftMessageGiftImage | undefined;
    giftName: string;
    describe: string;
    giftType: number;
    diamondCount: number;
}
/** Taken from https://github.com/Davincible/gotiktoklive/blob/da4630622bc586629a53faae64e8c53509af29de/proto/tiktok.proto#L57 */
export interface WebcastGiftMessageGiftExtra {
    timestamp: string;
    receiverUserId: string;
}
export interface WebcastGiftMessageGiftImage {
    giftPictureUrl: string;
}
/** Battle start */
export interface WebcastLinkMicBattle {
    battleUsers: WebcastLinkMicBattleItems[];
}
export interface WebcastLinkMicBattleItems {
    battleGroup: WebcastLinkMicBattleGroup | undefined;
}
export interface WebcastLinkMicBattleGroup {
    user: LinkUser | undefined;
}
/** Battle status */
export interface WebcastLinkMicArmies {
    battleItems: WebcastLinkMicArmiesItems[];
    battleStatus: number;
}
export interface WebcastLinkMicArmiesItems {
    hostUserId: string;
    battleGroups: WebcastLinkMicArmiesGroup[];
}
export interface WebcastLinkMicArmiesGroup {
    users: User[];
    points: number;
}
/** Follow & share event */
export interface WebcastSocialMessage {
    event: WebcastMessageEvent | undefined;
    user: User | undefined;
}
/** Like event (is only sent from time to time, not with every like) */
export interface WebcastLikeMessage {
    event: WebcastMessageEvent | undefined;
    user: User | undefined;
    likeCount: number;
    totalLikeCount: number;
}
/** New question event */
export interface WebcastQuestionNewMessage {
    questionDetails: QuestionDetails | undefined;
}
export interface QuestionDetails {
    questionText: string;
    user: User | undefined;
}
export interface WebcastMessageEvent {
    msgId: string;
    createTime: string;
    eventDetails: WebcastMessageEventDetails | undefined;
}
/** Contains UI information */
export interface WebcastMessageEventDetails {
    displayType: string;
    label: string;
}
/** Source: Co-opted https://github.com/zerodytrash/TikTok-Livestream-Chat-Connector/issues/19#issuecomment-1074150342 */
export interface WebcastLiveIntroMessage {
    id: string;
    description: string;
    user: User | undefined;
}
export interface SystemMessage {
    description: string;
}
export interface WebcastInRoomBannerMessage {
    data: string;
}
export interface RankItem {
    colour: string;
    id: string;
}
export interface WeeklyRanking {
    type: string;
    label: string;
    rank: RankItem | undefined;
}
export interface RankContainer {
    rankings: WeeklyRanking | undefined;
}
export interface WebcastHourlyRankMessage {
    data: RankContainer | undefined;
}
export interface EmoteDetails {
    emoteId: string;
    image: EmoteImage | undefined;
}
export interface EmoteImage {
    imageUrl: string;
}
/**
 * Envelope (treasure boxes)
 * Taken from https://github.com/ThanoFish/TikTok-Live-Connector/blob/9b215b96792adfddfb638344b152fa9efa581b4c/src/proto/tiktokSchema.proto
 */
export interface WebcastEnvelopeMessage {
    treasureBoxData: TreasureBoxData | undefined;
    treasureBoxUser: TreasureBoxUser | undefined;
}
export interface TreasureBoxUser {
    user2: TreasureBoxUser2 | undefined;
}
export interface TreasureBoxUser2 {
    user3: TreasureBoxUser3[];
}
export interface TreasureBoxUser3 {
    user4: TreasureBoxUser4 | undefined;
}
export interface TreasureBoxUser4 {
    user: User | undefined;
}
export interface TreasureBoxData {
    coins: number;
    canOpen: number;
    timestamp: string;
}
/** New Subscriber message */
export interface WebcastSubNotifyMessage {
    event: WebcastMessageEvent | undefined;
    user: User | undefined;
    exhibitionType: number;
    subMonth: number;
    subscribeType: number;
    oldSubscribeStatus: number;
    subscribingStatus: number;
}
export interface User {
    userId: string;
    nickname: string;
    profilePicture: ProfilePicture | undefined;
    uniqueId: string;
    secUid: string;
    badges: UserBadgesAttributes[];
    createTime: string;
    bioDescription: string;
    followInfo: FollowInfo | undefined;
}
export interface FollowInfo {
    followingCount: number;
    followerCount: number;
    followStatus: number;
    pushStatus: number;
}
export interface LinkUser {
    userId: string;
    nickname: string;
    profilePicture: ProfilePicture | undefined;
    uniqueId: string;
}
export interface ProfilePicture {
    urls: string[];
}
export interface UserBadgesAttributes {
    badgeSceneType: number;
    imageBadges: UserImageBadge[];
    badges: UserBadge[];
    privilegeLogExtra: PrivilegeLogExtra | undefined;
}
export interface PrivilegeLogExtra {
    privilegeId: string;
    level: string;
}
export interface UserBadge {
    type: string;
    name: string;
}
export interface UserImageBadge {
    displayType: number;
    image: UserImageBadgeImage | undefined;
}
export interface UserImageBadgeImage {
    url: string;
}
/** Websocket incoming message structure */
export interface WebcastWebsocketMessage {
    id: string;
    type: string;
    binary: Uint8Array;
}
/** Websocket acknowledgment message */
export interface WebcastWebsocketAck {
    id: string;
    type: string;
}
export declare const WebcastResponse: MessageFns<WebcastResponse>;
export declare const Message: MessageFns<Message>;
export declare const WebsocketParam: MessageFns<WebsocketParam>;
export declare const WebcastControlMessage: MessageFns<WebcastControlMessage>;
export declare const WebcastRoomUserSeqMessage: MessageFns<WebcastRoomUserSeqMessage>;
export declare const TopUser: MessageFns<TopUser>;
export declare const WebcastChatMessage: MessageFns<WebcastChatMessage>;
export declare const WebcastEmoteChatMessage: MessageFns<WebcastEmoteChatMessage>;
export declare const WebcastSubEmote: MessageFns<WebcastSubEmote>;
export declare const WebcastMemberMessage: MessageFns<WebcastMemberMessage>;
export declare const WebcastGiftMessage: MessageFns<WebcastGiftMessage>;
export declare const WebcastGiftMessageGiftDetails: MessageFns<WebcastGiftMessageGiftDetails>;
export declare const WebcastGiftMessageGiftExtra: MessageFns<WebcastGiftMessageGiftExtra>;
export declare const WebcastGiftMessageGiftImage: MessageFns<WebcastGiftMessageGiftImage>;
export declare const WebcastLinkMicBattle: MessageFns<WebcastLinkMicBattle>;
export declare const WebcastLinkMicBattleItems: MessageFns<WebcastLinkMicBattleItems>;
export declare const WebcastLinkMicBattleGroup: MessageFns<WebcastLinkMicBattleGroup>;
export declare const WebcastLinkMicArmies: MessageFns<WebcastLinkMicArmies>;
export declare const WebcastLinkMicArmiesItems: MessageFns<WebcastLinkMicArmiesItems>;
export declare const WebcastLinkMicArmiesGroup: MessageFns<WebcastLinkMicArmiesGroup>;
export declare const WebcastSocialMessage: MessageFns<WebcastSocialMessage>;
export declare const WebcastLikeMessage: MessageFns<WebcastLikeMessage>;
export declare const WebcastQuestionNewMessage: MessageFns<WebcastQuestionNewMessage>;
export declare const QuestionDetails: MessageFns<QuestionDetails>;
export declare const WebcastMessageEvent: MessageFns<WebcastMessageEvent>;
export declare const WebcastMessageEventDetails: MessageFns<WebcastMessageEventDetails>;
export declare const WebcastLiveIntroMessage: MessageFns<WebcastLiveIntroMessage>;
export declare const SystemMessage: MessageFns<SystemMessage>;
export declare const WebcastInRoomBannerMessage: MessageFns<WebcastInRoomBannerMessage>;
export declare const RankItem: MessageFns<RankItem>;
export declare const WeeklyRanking: MessageFns<WeeklyRanking>;
export declare const RankContainer: MessageFns<RankContainer>;
export declare const WebcastHourlyRankMessage: MessageFns<WebcastHourlyRankMessage>;
export declare const EmoteDetails: MessageFns<EmoteDetails>;
export declare const EmoteImage: MessageFns<EmoteImage>;
export declare const WebcastEnvelopeMessage: MessageFns<WebcastEnvelopeMessage>;
export declare const TreasureBoxUser: MessageFns<TreasureBoxUser>;
export declare const TreasureBoxUser2: MessageFns<TreasureBoxUser2>;
export declare const TreasureBoxUser3: MessageFns<TreasureBoxUser3>;
export declare const TreasureBoxUser4: MessageFns<TreasureBoxUser4>;
export declare const TreasureBoxData: MessageFns<TreasureBoxData>;
export declare const WebcastSubNotifyMessage: MessageFns<WebcastSubNotifyMessage>;
export declare const User: MessageFns<User>;
export declare const FollowInfo: MessageFns<FollowInfo>;
export declare const LinkUser: MessageFns<LinkUser>;
export declare const ProfilePicture: MessageFns<ProfilePicture>;
export declare const UserBadgesAttributes: MessageFns<UserBadgesAttributes>;
export declare const PrivilegeLogExtra: MessageFns<PrivilegeLogExtra>;
export declare const UserBadge: MessageFns<UserBadge>;
export declare const UserImageBadge: MessageFns<UserImageBadge>;
export declare const UserImageBadgeImage: MessageFns<UserImageBadgeImage>;
export declare const WebcastWebsocketMessage: MessageFns<WebcastWebsocketMessage>;
export declare const WebcastWebsocketAck: MessageFns<WebcastWebsocketAck>;
type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;
export type DeepPartial<T> = T extends Builtin ? T : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>> : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>> : T extends {} ? {
    [K in keyof T]?: DeepPartial<T[K]>;
} : Partial<T>;
type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P : P & {
    [K in keyof P]: Exact<P[K], I[K]>;
} & {
    [K in Exclude<keyof I, KeysOfUnion<P>>]: never;
};
export interface MessageFns<T> {
    encode(message: T, writer?: BinaryWriter): BinaryWriter;
    decode(input: BinaryReader | Uint8Array, length?: number): T;
    fromJSON(object: any): T;
    toJSON(message: T): unknown;
    create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
    fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
export {};
//# sourceMappingURL=tiktok-schema.d.ts.map