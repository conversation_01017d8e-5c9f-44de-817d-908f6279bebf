{"version": 3, "file": "tiktok-schema.js", "sourceRoot": "", "sources": ["../../src/types/tiktok-schema.ts"], "names": [], "mappings": ";AAAA,sDAAsD;AACtD,YAAY;AACZ,gCAAgC;AAChC,iCAAiC;AACjC,8BAA8B;;;;AAE9B,oBAAoB;AACpB,kDAAqE;AAExD,QAAA,eAAe,GAAG,QAAQ,CAAC;AAExC,IAAY,aAOX;AAPD,WAAY,aAAa;IACvB,uGAAmC,CAAA;IACnC,iGAAgC,CAAA;IAChC,qGAAkC,CAAA;IAClC,+FAA+B,CAAA;IAC/B,uGAAmC,CAAA;IACnC,kEAAiB,CAAA;AACnB,CAAC,EAPW,aAAa,GAAb,qBAAa,KAAb,qBAAa,QAOxB;AAED,SAAgB,qBAAqB,CAAC,MAAW;IAC/C,QAAQ,MAAM,EAAE;QACd,KAAK,CAAC,CAAC;QACP,KAAK,iCAAiC;YACpC,OAAO,aAAa,CAAC,+BAA+B,CAAC;QACvD,KAAK,CAAC,CAAC;QACP,KAAK,8BAA8B;YACjC,OAAO,aAAa,CAAC,4BAA4B,CAAC;QACpD,KAAK,CAAC,CAAC;QACP,KAAK,gCAAgC;YACnC,OAAO,aAAa,CAAC,8BAA8B,CAAC;QACtD,KAAK,CAAC,CAAC;QACP,KAAK,6BAA6B;YAChC,OAAO,aAAa,CAAC,2BAA2B,CAAC;QACnD,KAAK,CAAC,CAAC;QACP,KAAK,iCAAiC;YACpC,OAAO,aAAa,CAAC,+BAA+B,CAAC;QACvD,KAAK,CAAC,CAAC,CAAC;QACR,KAAK,cAAc,CAAC;QACpB;YACE,OAAO,aAAa,CAAC,YAAY,CAAC;KACrC;AACH,CAAC;AAtBD,sDAsBC;AAED,SAAgB,mBAAmB,CAAC,MAAqB;IACvD,QAAQ,MAAM,EAAE;QACd,KAAK,aAAa,CAAC,+BAA+B;YAChD,OAAO,iCAAiC,CAAC;QAC3C,KAAK,aAAa,CAAC,4BAA4B;YAC7C,OAAO,8BAA8B,CAAC;QACxC,KAAK,aAAa,CAAC,8BAA8B;YAC/C,OAAO,gCAAgC,CAAC;QAC1C,KAAK,aAAa,CAAC,2BAA2B;YAC5C,OAAO,6BAA6B,CAAC;QACvC,KAAK,aAAa,CAAC,+BAA+B;YAChD,OAAO,iCAAiC,CAAC;QAC3C,KAAK,aAAa,CAAC,YAAY,CAAC;QAChC;YACE,OAAO,cAAc,CAAC;KACzB;AACH,CAAC;AAhBD,kDAgBC;AA6TD,SAAS,yBAAyB;IAChC,OAAO;QACL,QAAQ,EAAE,EAAE;QACZ,MAAM,EAAE,EAAE;QACV,aAAa,EAAE,CAAC;QAChB,eAAe,EAAE,GAAG;QACpB,WAAW,EAAE,EAAE;QACf,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,EAAE;QACZ,iBAAiB,EAAE,CAAC;QACpB,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,EAAE;KACV,CAAC;AACJ,CAAC;AAEY,QAAA,eAAe,GAAgC;IAC1D,MAAM,CAAC,OAAwB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACxE,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;YAChC,eAAO,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACrD;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAC1C;QACD,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SAChD;QACD,IAAI,OAAO,CAAC,eAAe,KAAK,GAAG,EAAE;YACnC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;SAClD;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC5C;QACD,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,QAAQ,EAAE;YAChC,sBAAc,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,IAAI,OAAO,CAAC,iBAAiB,KAAK,CAAC,EAAE;YACnC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;SACpD;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAO,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBAC/D,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACjC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACvC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACpD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACtC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACnC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAc,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACtE,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,iBAAiB,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAC3C,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;oBAChC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,eAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChH,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;YACpE,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,GAAG;YAChG,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;YACnF,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5E,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC;gBAClD,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,sBAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC7D,CAAC,CAAC,EAAE;YACN,iBAAiB,EAAE,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;YACpG,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK;YAC3E,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;SAClE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAwB;QAC7B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE;YAC5B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/D;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACzB,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;SAC7B;QACD,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,EAAE;YAC/B,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,eAAe,KAAK,GAAG,EAAE;YACnC,GAAG,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;SACvC;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE;YAC3B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE;YAC5B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,sBAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACtE;QACD,IAAI,OAAO,CAAC,iBAAiB,KAAK,CAAC,EAAE;YACnC,GAAG,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;SAC/D;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;YAC7B,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SAC/B;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;SAC3B;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAmD,IAAQ;QAC/D,OAAO,uBAAe,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC1D,CAAC;IACD,WAAW,CAAmD,MAAS;QACrE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7E,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;QACrC,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC;QAClD,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe,IAAI,GAAG,CAAC;QACxD,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;QAC/C,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;QAC1C,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,sBAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACpF,OAAO,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAC;QAC1D,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC;QAC1C,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,iBAAiB;IACxB,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AACjD,CAAC;AAEY,QAAA,OAAO,GAAwB;IAC1C,MAAM,CAAC,OAAgB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAChE,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxC;QACD,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,iBAAiB,EAAE,CAAC;QACpC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/B,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAChC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9D,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;SAClF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAgB;QACrB,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACzB;QACD,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,GAAG,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAC9C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA2C,IAAQ;QACvD,OAAO,eAAO,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAClD,CAAC;IACD,WAAW,CAA2C,MAAS;QAC7D,MAAM,OAAO,GAAG,iBAAiB,EAAE,CAAC;QACpC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,wBAAwB;IAC/B,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AACjC,CAAC;AAEY,QAAA,cAAc,GAA+B;IACxD,MAAM,CAAC,OAAuB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACvE,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxC;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,wBAAwB,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/B,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9D,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;SAClE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAuB;QAC5B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACzB;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;SAC3B;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAkD,IAAQ;QAC9D,OAAO,sBAAc,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACzD,CAAC;IACD,WAAW,CAAkD,MAAS;QACpE,MAAM,OAAO,GAAG,wBAAwB,EAAE,CAAC;QAC3C,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,+BAA+B;IACtC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AACvB,CAAC;AAEY,QAAA,qBAAqB,GAAsC;IACtE,MAAM,CAAC,OAA8B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC9E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,+BAA+B,EAAE,CAAC;QAClD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,EAAS,CAAC;oBACvC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACrF,CAAC;IAED,MAAM,CAAC,OAA8B;QACnC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,GAAG,CAAC,MAAM,GAAG,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAClD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAyD,IAAQ;QACrE,OAAO,6BAAqB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAChE,CAAC;IACD,WAAW,CAAyD,MAAS;QAC3E,MAAM,OAAO,GAAG,+BAA+B,EAAE,CAAC;QAClD,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,mCAAmC;IAC1C,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC;AAC5C,CAAC;AAEY,QAAA,yBAAyB,GAA0C;IAC9E,MAAM,CAAC,OAAkC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAClF,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE;YAClC,eAAO,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACrD;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC9C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,eAAO,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACjE,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACrC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,UAAU,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC;gBACtD,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,eAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACxD,CAAC,CAAC,EAAE;YACN,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;SACnF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAkC;QACvC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,EAAE;YAC9B,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACnE;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE;YAC7B,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SACnD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA6D,IAAQ;QACzE,OAAO,iCAAyB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACpE,CAAC;IACD,WAAW,CAA6D,MAAS;QAC/E,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACjF,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC;QAC9C,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,iBAAiB;IACxB,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAC7C,CAAC;AAEY,QAAA,OAAO,GAAwB;IAC1C,MAAM,CAAC,OAAgB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAChE,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC5C;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,iBAAiB,EAAE,CAAC;QACpC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,CAAC,EAAE;wBACb,MAAM;qBACP;oBAED,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC/C,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG;YAC9E,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;SAClE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAgB;QACrB,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,EAAE;YAC7B,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;SACnC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA2C,IAAQ;QACvD,OAAO,eAAO,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAClD,CAAC;IACD,WAAW,CAA2C,MAAS;QAC7D,MAAM,OAAO,GAAG,iBAAiB,EAAE,CAAC;QACpC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,GAAG,CAAC;QAC5C,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,4BAA4B;IACnC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;AACxE,CAAC;AAEY,QAAA,kBAAkB,GAAmC;IAChE,MAAM,CAAC,OAA2B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC3E,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5E;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,EAAE,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3C;QACD,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE;YAC9B,uBAAe,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC9D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,4BAA4B,EAAE,CAAC;QAC/C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpE,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACrE,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,2BAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;YACnF,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YACjE,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;YACvE,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC9C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,uBAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5D,CAAC,CAAC,EAAE;SACP,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA2B;QAChC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,EAAE,EAAE;YAC1B,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SAC/B;QACD,IAAI,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE;YAC1B,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,uBAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACnE;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAsD,IAAQ;QAClE,OAAO,0BAAkB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC7D,CAAC;IACD,WAAW,CAAsD,MAAS;QACxE,MAAM,OAAO,GAAG,4BAA4B,EAAE,CAAC;QAC/C,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,2BAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;QACvC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,uBAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACjF,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,iCAAiC;IACxC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC/C,CAAC;AAEY,QAAA,uBAAuB,GAAwC;IAC1E,MAAM,CAAC,OAAgC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAChF,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,oBAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACrE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,iCAAiC,EAAE,CAAC;QACpD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,oBAAY,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC7D,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YACjE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SAC7E,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAgC;QACrC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,oBAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAChD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA2D,IAAQ;QACvE,OAAO,+BAAuB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAClE,CAAC;IACD,WAAW,CAA2D,MAAS;QAC7E,MAAM,OAAO,GAAG,iCAAiC,EAAE,CAAC;QACpD,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,oBAAY,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YACxC,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,yBAAyB;IAChC,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACjD,CAAC;AAEY,QAAA,eAAe,GAAgC;IAC1D,MAAM,CAAC,OAAwB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACxE,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SAChD;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,oBAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACrE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,CAAC,EAAE;wBACb,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACxC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,oBAAY,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC7D,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SAC7E,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAwB;QAC7B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACzD;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,oBAAY,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAChD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAmD,IAAQ;QAC/D,OAAO,uBAAe,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC1D,CAAC;IACD,WAAW,CAAmD,MAAS;QACrE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,oBAAY,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YACxC,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,8BAA8B;IACrC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC5D,CAAC;AAEY,QAAA,oBAAoB,GAAqC;IACpE,MAAM,CAAC,OAA6B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC7E,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5E;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC3C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpE,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAClC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,2BAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;YACnF,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YACjE,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1E,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA6B;QAClC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC7C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAwD,IAAQ;QACpE,OAAO,4BAAoB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC/D,CAAC;IACD,WAAW,CAAwD,MAAS;QAC1E,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,2BAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC;QACxC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,4BAA4B;IACnC,OAAO;QACL,KAAK,EAAE,SAAS;QAChB,MAAM,EAAE,CAAC;QACT,WAAW,EAAE,CAAC;QACd,IAAI,EAAE,SAAS;QACf,SAAS,EAAE,CAAC;QACZ,OAAO,EAAE,GAAG;QACZ,WAAW,EAAE,SAAS;QACtB,YAAY,EAAE,EAAE;QAChB,SAAS,EAAE,SAAS;KACrB,CAAC;AACJ,CAAC;AAEY,QAAA,kBAAkB,GAAmC;IAChE,MAAM,CAAC,OAA2B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC3E,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5E;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC9C;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC5C;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,GAAG,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3C;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YACrC,qCAA6B,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC7F;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,EAAE,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SACjD;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;YACnC,mCAA2B,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACzF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,4BAA4B,EAAE,CAAC;QAC/C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpE,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAChC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACrC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACnC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC7C,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,qCAA6B,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpF,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACvC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,SAAS,GAAG,mCAA2B,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAChF,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,2BAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;YACnF,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACnE,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAClF,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YACjE,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5E,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG;YACxE,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,qCAA6B,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;YAC/G,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;YACtF,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,mCAA2B,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;SACxG,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA2B;QAChC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE;YAC7B,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SACnD;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE;YAC3B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,GAAG,EAAE;YAC3B,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SAC/B;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YACrC,GAAG,CAAC,WAAW,GAAG,qCAA6B,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC7E;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,EAAE,EAAE;YAC/B,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;YACnC,GAAG,CAAC,SAAS,GAAG,mCAA2B,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACvE;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAsD,IAAQ;QAClE,OAAO,0BAAkB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC7D,CAAC;IACD,WAAW,CAAsD,MAAS;QACxE,MAAM,OAAO,GAAG,4BAA4B,EAAE,CAAC;QAC/C,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,2BAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;QACpC,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC;QAC9C,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;QAC1C,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,GAAG,CAAC;QACxC,OAAO,CAAC,WAAW,GAAG,CAAC,MAAM,CAAC,WAAW,KAAK,SAAS,IAAI,MAAM,CAAC,WAAW,KAAK,IAAI,CAAC;YACrF,CAAC,CAAC,qCAA6B,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC;YAC/D,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;QACjD,OAAO,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC;YAC/E,CAAC,CAAC,mCAA2B,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;YAC3D,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,uCAAuC;IAC9C,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;AAC5F,CAAC;AAEY,QAAA,6BAA6B,GAA8C;IACtF,MAAM,CAAC,OAAsC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACtF,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;YACnC,mCAA2B,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACxF;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC7C;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC5C;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC3C;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,CAAC,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SAC/C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,uCAAuC,EAAE,CAAC;QAC1D,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,SAAS,GAAG,mCAA2B,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAChF,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAClC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACtC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,mCAA2B,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS;YACvG,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1E,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1E,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACzE,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;SACtF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAsC;QAC3C,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;YACnC,GAAG,CAAC,SAAS,GAAG,mCAA2B,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACvE;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACjC;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACjC;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC7C;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,CAAC,EAAE;YAC9B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SACrD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAiE,IAAQ;QAC7E,OAAO,qCAA6B,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACxE,CAAC;IACD,WAAW,CACT,MAAS;QAET,MAAM,OAAO,GAAG,uCAAuC,EAAE,CAAC;QAC1D,OAAO,CAAC,SAAS,GAAG,CAAC,MAAM,CAAC,SAAS,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,IAAI,CAAC;YAC/E,CAAC,CAAC,mCAA2B,CAAC,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC;YAC3D,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACzC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACzC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,CAAC,CAAC;QAChD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,qCAAqC;IAC5C,OAAO,EAAE,SAAS,EAAE,GAAG,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC;AACjD,CAAC;AAEY,QAAA,2BAA2B,GAA4C;IAClF,MAAM,CAAC,OAAoC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACpF,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC7C;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,GAAG,EAAE;YAClC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SAClD;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,qCAAqC,EAAE,CAAC;QACxD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC/C,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACpD,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG;YAC9E,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG;SAC9F,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAoC;QACzC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,EAAE;YAC7B,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;SACnC;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,GAAG,EAAE;YAClC,GAAG,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;SAC7C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA+D,IAAQ;QAC3E,OAAO,mCAA2B,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACtE,CAAC;IACD,WAAW,CAA+D,MAAS;QACjF,MAAM,OAAO,GAAG,qCAAqC,EAAE,CAAC;QACxD,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,GAAG,CAAC;QAC5C,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,GAAG,CAAC;QACtD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,qCAAqC;IAC5C,OAAO,EAAE,cAAc,EAAE,EAAE,EAAE,CAAC;AAChC,CAAC;AAEY,QAAA,2BAA2B,GAA4C;IAClF,MAAM,CAAC,OAAoC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACpF,IAAI,OAAO,CAAC,cAAc,KAAK,EAAE,EAAE;YACjC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SAClD;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,qCAAqC,EAAE,CAAC;QACxD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACzC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC1G,CAAC;IAED,MAAM,CAAC,OAAoC;QACzC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,cAAc,KAAK,EAAE,EAAE;YACjC,GAAG,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;SAC7C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA+D,IAAQ;QAC3E,OAAO,mCAA2B,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACtE,CAAC;IACD,WAAW,CAA+D,MAAS;QACjF,MAAM,OAAO,GAAG,qCAAqC,EAAE,CAAC;QACxD,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC;QACrD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,8BAA8B;IACrC,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;AAC7B,CAAC;AAEY,QAAA,oBAAoB,GAAqC;IACpE,MAAM,CAAC,OAA6B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC7E,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE;YACnC,iCAAyB,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACvE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,iCAAyB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACpF,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC;gBACxD,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,iCAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC3E,CAAC,CAAC,EAAE;SACP,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA6B;QAClC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE;YAC/B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iCAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACvF;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAwD,IAAQ;QACpE,OAAO,4BAAoB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC/D,CAAC;IACD,WAAW,CAAwD,MAAS;QAC1E,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iCAAyB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACrG,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,mCAAmC;IAC1C,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;AACpC,CAAC;AAEY,QAAA,yBAAyB,GAA0C;IAC9E,MAAM,CAAC,OAAkC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAClF,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YACrC,iCAAyB,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACxF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,iCAAyB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAChF,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,iCAAyB,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS;SAC5G,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAkC;QACvC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;YACrC,GAAG,CAAC,WAAW,GAAG,iCAAyB,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SACzE;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA6D,IAAQ;QACzE,OAAO,iCAAyB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACpE,CAAC;IACD,WAAW,CAA6D,MAAS;QAC/E,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,CAAC,WAAW,GAAG,CAAC,MAAM,CAAC,WAAW,KAAK,SAAS,IAAI,MAAM,CAAC,WAAW,KAAK,IAAI,CAAC;YACrF,CAAC,CAAC,iCAAyB,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC;YAC3D,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,mCAAmC;IAC1C,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAC7B,CAAC;AAEY,QAAA,yBAAyB,GAA0C;IAC9E,MAAM,CAAC,OAAkC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAClF,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,gBAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAChE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,gBAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACxD,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;IACnF,CAAC;IAED,MAAM,CAAC,OAAkC;QACvC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,gBAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC1C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA6D,IAAQ;QACzE,OAAO,iCAAyB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACpE,CAAC;IACD,WAAW,CAA6D,MAAS;QAC/E,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACnH,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,8BAA8B;IACrC,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;AAC9C,CAAC;AAEY,QAAA,oBAAoB,GAAqC;IACpE,MAAM,CAAC,OAA6B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC7E,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE;YACnC,iCAAyB,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACvE;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,CAAC,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SAC/C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,iCAAyB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACpF,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACtC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC;gBACxD,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,iCAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC3E,CAAC,CAAC,EAAE;YACN,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;SACtF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA6B;QAClC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE;YAC/B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iCAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACvF;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,CAAC,EAAE;YAC9B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SACrD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAwD,IAAQ;QACpE,OAAO,4BAAoB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC/D,CAAC;IACD,WAAW,CAAwD,MAAS;QAC1E,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iCAAyB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACrG,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,CAAC,CAAC;QAChD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,mCAAmC;IAC1C,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;AAC/C,CAAC;AAEY,QAAA,yBAAyB,GAA0C;IAC9E,MAAM,CAAC,OAAkC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAClF,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SAC7C;QACD,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,YAAY,EAAE;YACpC,iCAAyB,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACvE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,CAAC,EAAE;wBACb,MAAM;qBACP;oBAED,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAChD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,iCAAyB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACrF,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG;YACjF,YAAY,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC;gBAC1D,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,iCAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC5E,CAAC,CAAC,EAAE;SACP,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAkC;QACvC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG,EAAE;YAC9B,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;SACrC;QACD,IAAI,OAAO,CAAC,YAAY,EAAE,MAAM,EAAE;YAChC,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iCAAyB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACzF;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA6D,IAAQ;QACzE,OAAO,iCAAyB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACpE,CAAC;IACD,WAAW,CAA6D,MAAS;QAC/E,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,GAAG,CAAC;QAC9C,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iCAAyB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACvG,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,mCAAmC;IAC1C,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AAClC,CAAC;AAEY,QAAA,yBAAyB,GAA0C;IAC9E,MAAM,CAAC,OAAkC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAClF,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE;YAC7B,YAAI,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAClD;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACzD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAChC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,YAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpG,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACpE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAkC;QACvC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE;YACzB,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACtD;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA6D,IAAQ;QACzE,OAAO,iCAAyB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACpE,CAAC;IACD,WAAW,CAA6D,MAAS;QAC/E,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,YAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACpE,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC;QACpC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,8BAA8B;IACrC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAC/C,CAAC;AAEY,QAAA,oBAAoB,GAAqC;IACpE,MAAM,CAAC,OAA6B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC7E,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5E;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpE,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,2BAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;YACnF,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;SAClE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA6B;QAClC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAwD,IAAQ;QACpE,OAAO,4BAAoB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC/D,CAAC;IACD,WAAW,CAAwD,MAAS;QAC1E,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,2BAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,4BAA4B;IACnC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC;AAChF,CAAC;AAEY,QAAA,kBAAkB,GAAmC;IAChE,MAAM,CAAC,OAA2B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC3E,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5E;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC5C;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACjD;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,4BAA4B,EAAE,CAAC;QAC/C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpE,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACnC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACxC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,2BAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;YACnF,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YACjE,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5E,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5F,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA2B;QAChC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE;YAC3B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACzD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAsD,IAAQ;QAClE,OAAO,0BAAkB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC7D,CAAC;IACD,WAAW,CAAsD,MAAS;QACxE,MAAM,OAAO,GAAG,4BAA4B,EAAE,CAAC;QAC/C,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,2BAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;QAC1C,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,CAAC,CAAC;QACpD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,mCAAmC;IAC1C,OAAO,EAAE,eAAe,EAAE,SAAS,EAAE,CAAC;AACxC,CAAC;AAEY,QAAA,yBAAyB,GAA0C;IAC9E,MAAM,CAAC,OAAkC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAClF,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;YACzC,uBAAe,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAClF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,eAAe,GAAG,uBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC1E,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,uBAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS;SAC9G,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAkC;QACvC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;YACzC,GAAG,CAAC,eAAe,GAAG,uBAAe,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;SACvE;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA6D,IAAQ;QACzE,OAAO,iCAAyB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACpE,CAAC;IACD,WAAW,CAA6D,MAAS;QAC/E,MAAM,OAAO,GAAG,mCAAmC,EAAE,CAAC;QACtD,OAAO,CAAC,eAAe,GAAG,CAAC,MAAM,CAAC,eAAe,KAAK,SAAS,IAAI,MAAM,CAAC,eAAe,KAAK,IAAI,CAAC;YACjG,CAAC,CAAC,uBAAe,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC;YACrD,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,yBAAyB;IAChC,OAAO,EAAE,YAAY,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAC/C,CAAC;AAEY,QAAA,eAAe,GAAgC;IAC1D,MAAM,CAAC,OAAwB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACxE,IAAI,OAAO,CAAC,YAAY,KAAK,EAAE,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SAChD;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACvC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;YACtF,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;SAClE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAwB;QAC7B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,YAAY,KAAK,EAAE,EAAE;YAC/B,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAmD,IAAQ;QAC/D,OAAO,uBAAe,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC1D,CAAC;IACD,WAAW,CAAmD,MAAS;QACrE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;QACjD,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,6BAA6B;IACpC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,YAAY,EAAE,SAAS,EAAE,CAAC;AAClE,CAAC;AAEY,QAAA,mBAAmB,GAAoC;IAClE,MAAM,CAAC,OAA4B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC5E,IAAI,OAAO,CAAC,KAAK,KAAK,GAAG,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SAC9C;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;YACtC,kCAA0B,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC1F;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,6BAA6B,EAAE,CAAC;QAChD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC3C,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAChD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,YAAY,GAAG,kCAA0B,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAClF,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG;YAClE,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG;YACjF,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,kCAA0B,CAAC,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS;SAChH,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA4B;QACjC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,GAAG,EAAE;YACzB,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;SAC3B;QACD,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG,EAAE;YAC9B,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;SACrC;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,SAAS,EAAE;YACtC,GAAG,CAAC,YAAY,GAAG,kCAA0B,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SAC5E;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAuD,IAAQ;QACnE,OAAO,2BAAmB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC9D,CAAC;IACD,WAAW,CAAuD,MAAS;QACzE,MAAM,OAAO,GAAG,6BAA6B,EAAE,CAAC;QAChD,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,GAAG,CAAC;QACpC,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,GAAG,CAAC;QAC9C,OAAO,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC,YAAY,KAAK,SAAS,IAAI,MAAM,CAAC,YAAY,KAAK,IAAI,CAAC;YACxF,CAAC,CAAC,kCAA0B,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC;YAC7D,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,oCAAoC;IAC3C,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AACxC,CAAC;AAEY,QAAA,0BAA0B,GAA2C;IAChF,MAAM,CAAC,OAAmC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACnF,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,oCAAoC,EAAE,CAAC;QACvD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACtC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;YACnF,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;SAClE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAmC;QACxC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;SACvC;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;SAC3B;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA8D,IAAQ;QAC1E,OAAO,kCAA0B,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACrE,CAAC;IACD,WAAW,CAA8D,MAAS;QAChF,MAAM,OAAO,GAAG,oCAAoC,EAAE,CAAC;QACvD,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;QAC/C,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,iCAAiC;IACxC,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AACvD,CAAC;AAEY,QAAA,uBAAuB,GAAwC;IAC1E,MAAM,CAAC,OAAgC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAChF,IAAI,OAAO,CAAC,EAAE,KAAK,GAAG,EAAE;YACtB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SACtC;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,iCAAiC,EAAE,CAAC;QACpD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACxC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACtC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG;YACzD,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;YACnF,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;SAClE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAgC;QACrC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,EAAE,KAAK,GAAG,EAAE;YACtB,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;SACrB;QACD,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;SACvC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA2D,IAAQ;QACvE,OAAO,+BAAuB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAClE,CAAC;IACD,WAAW,CAA2D,MAAS;QAC7E,MAAM,OAAO,GAAG,iCAAiC,EAAE,CAAC;QACpD,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,GAAG,CAAC;QAC9B,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;QAC/C,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,uBAAuB;IAC9B,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,CAAC;AAC7B,CAAC;AAEY,QAAA,aAAa,GAA8B;IACtD,MAAM,CAAC,OAAsB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACtE,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC/C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,uBAAuB,EAAE,CAAC;QAC1C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACtC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACjG,CAAC;IAED,MAAM,CAAC,OAAsB;QAC3B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;SACvC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAiD,IAAQ;QAC7D,OAAO,qBAAa,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACxD,CAAC;IACD,WAAW,CAAiD,MAAS;QACnE,MAAM,OAAO,GAAG,uBAAuB,EAAE,CAAC;QAC1C,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;QAC/C,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,oCAAoC;IAC3C,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AACtB,CAAC;AAEY,QAAA,0BAA0B,GAA2C;IAChF,MAAM,CAAC,OAAmC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACnF,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,oCAAoC,EAAE,CAAC;QACvD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/B,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5E,CAAC;IAED,MAAM,CAAC,OAAmC;QACxC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACzB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA8D,IAAQ;QAC1E,OAAO,kCAA0B,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACrE,CAAC;IACD,WAAW,CAA8D,MAAS;QAChF,MAAM,OAAO,GAAG,oCAAoC,EAAE,CAAC;QACvD,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,kBAAkB;IACzB,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC;AACjC,CAAC;AAEY,QAAA,QAAQ,GAAyB;IAC5C,MAAM,CAAC,OAAiB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACjE,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAC1C;QACD,IAAI,OAAO,CAAC,EAAE,KAAK,GAAG,EAAE;YACtB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SACtC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,kBAAkB,EAAE,CAAC;QACrC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACjC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACxC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;YACpE,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG;SAC1D,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAiB;QACtB,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACzB,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;SAC7B;QACD,IAAI,OAAO,CAAC,EAAE,KAAK,GAAG,EAAE;YACtB,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;SACrB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA4C,IAAQ;QACxD,OAAO,gBAAQ,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACnD,CAAC;IACD,WAAW,CAA4C,MAAS;QAC9D,MAAM,OAAO,GAAG,kBAAkB,EAAE,CAAC;QACrC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;QACrC,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,GAAG,CAAC;QAC9B,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,uBAAuB;IAC9B,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAClD,CAAC;AAEY,QAAA,aAAa,GAA8B;IACtD,MAAM,CAAC,OAAsB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACtE,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxC;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,gBAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAChE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,uBAAuB,EAAE,CAAC;QAC1C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/B,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,gBAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACxD,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9D,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;YACjE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;SACtE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAsB;QAC3B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACzB;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;SAC3B;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,gBAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC1C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAiD,IAAQ;QAC7D,OAAO,qBAAa,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACxD,CAAC;IACD,WAAW,CAAiD,MAAS;QACnE,MAAM,OAAO,GAAG,uBAAuB,EAAE,CAAC;QAC1C,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACnC,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,gBAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACnH,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,uBAAuB;IAC9B,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;AACjC,CAAC;AAEY,QAAA,aAAa,GAA8B;IACtD,MAAM,CAAC,OAAsB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACtE,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;YAClC,qBAAa,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACzE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,uBAAuB,EAAE,CAAC;QAC1C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,qBAAa,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACjE,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;IACpG,CAAC;IAED,MAAM,CAAC,OAAsB;QAC3B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,QAAQ,KAAK,SAAS,EAAE;YAClC,GAAG,CAAC,QAAQ,GAAG,qBAAa,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SACvD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAiD,IAAQ;QAC7D,OAAO,qBAAa,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACxD,CAAC;IACD,WAAW,CAAiD,MAAS;QACnE,MAAM,OAAO,GAAG,uBAAuB,EAAE,CAAC;QAC1C,OAAO,CAAC,QAAQ,GAAG,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAS,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,CAAC;YAC5E,CAAC,CAAC,qBAAa,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC5C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,kCAAkC;IACzC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAC7B,CAAC;AAEY,QAAA,wBAAwB,GAAyC;IAC5E,MAAM,CAAC,OAAiC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACjF,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,qBAAa,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACrE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,kCAAkC,EAAE,CAAC;QACrD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,qBAAa,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC7D,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,qBAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;IACxF,CAAC;IAED,MAAM,CAAC,OAAiC;QACtC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,qBAAa,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SAC/C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA4D,IAAQ;QACxE,OAAO,gCAAwB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACnE,CAAC;IACD,WAAW,CAA4D,MAAS;QAC9E,MAAM,OAAO,GAAG,kCAAkC,EAAE,CAAC;QACrD,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;YAChE,CAAC,CAAC,qBAAa,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC;YACxC,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,sBAAsB;IAC7B,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC3C,CAAC;AAEY,QAAA,YAAY,GAA6B;IACpD,MAAM,CAAC,OAAqB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACrE,IAAI,OAAO,CAAC,OAAO,KAAK,EAAE,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3C;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,kBAAU,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACnE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,sBAAsB,EAAE,CAAC;QACzC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,kBAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC3D,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;YACvE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,kBAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SAC3E,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAqB;QAC1B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,OAAO,KAAK,EAAE,EAAE;YAC1B,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SAC/B;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,kBAAU,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC9C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAgD,IAAQ;QAC5D,OAAO,oBAAY,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACvD,CAAC;IACD,WAAW,CAAgD,MAAS;QAClE,MAAM,OAAO,GAAG,sBAAsB,EAAE,CAAC;QACzC,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;QACvC,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,kBAAU,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YACtC,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,oBAAoB;IAC3B,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AAC1B,CAAC;AAEY,QAAA,UAAU,GAA2B;IAChD,MAAM,CAAC,OAAmB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACnE,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC5C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,oBAAoB,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACxF,CAAC;IAED,MAAM,CAAC,OAAmB;QACxB,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACjC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA8C,IAAQ;QAC1D,OAAO,kBAAU,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACrD,CAAC;IACD,WAAW,CAA8C,MAAS;QAChE,MAAM,OAAO,GAAG,oBAAoB,EAAE,CAAC;QACvC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACzC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,gCAAgC;IACvC,OAAO,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,SAAS,EAAE,CAAC;AACpE,CAAC;AAEY,QAAA,sBAAsB,GAAuC;IACxE,MAAM,CAAC,OAA+B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC/E,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;YACzC,uBAAe,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAClF;QACD,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;YACzC,uBAAe,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAClF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,gCAAgC,EAAE,CAAC;QACnD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,eAAe,GAAG,uBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC1E,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,eAAe,GAAG,uBAAe,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC1E,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,uBAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7G,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,uBAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS;SAC9G,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA+B;QACpC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;YACzC,GAAG,CAAC,eAAe,GAAG,uBAAe,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;SACvE;QACD,IAAI,OAAO,CAAC,eAAe,KAAK,SAAS,EAAE;YACzC,GAAG,CAAC,eAAe,GAAG,uBAAe,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;SACvE;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA0D,IAAQ;QACtE,OAAO,8BAAsB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACjE,CAAC;IACD,WAAW,CAA0D,MAAS;QAC5E,MAAM,OAAO,GAAG,gCAAgC,EAAE,CAAC;QACnD,OAAO,CAAC,eAAe,GAAG,CAAC,MAAM,CAAC,eAAe,KAAK,SAAS,IAAI,MAAM,CAAC,eAAe,KAAK,IAAI,CAAC;YACjG,CAAC,CAAC,uBAAe,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC;YACrD,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,eAAe,GAAG,CAAC,MAAM,CAAC,eAAe,KAAK,SAAS,IAAI,MAAM,CAAC,eAAe,KAAK,IAAI,CAAC;YACjG,CAAC,CAAC,uBAAe,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC;YACrD,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,yBAAyB;IAChC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC9B,CAAC;AAEY,QAAA,eAAe,GAAgC;IAC1D,MAAM,CAAC,OAAwB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACxE,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,wBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACzE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,wBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACjE,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,wBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;IAC9F,CAAC;IAED,MAAM,CAAC,OAAwB;QAC7B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,wBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACpD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAmD,IAAQ;QAC/D,OAAO,uBAAe,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC1D,CAAC;IACD,WAAW,CAAmD,MAAS;QACrE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,wBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YAC5C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,0BAA0B;IACjC,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AACvB,CAAC;AAEY,QAAA,gBAAgB,GAAiC;IAC5D,MAAM,CAAC,OAAyB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACzE,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE;YAC7B,wBAAgB,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC9D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,0BAA0B,EAAE,CAAC;QAC7C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,wBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACrE,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,wBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SACjH,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAyB;QAC9B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE;YACzB,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,wBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SAClE;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAoD,IAAQ;QAChE,OAAO,wBAAgB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC3D,CAAC;IACD,WAAW,CAAoD,MAAS;QACtE,MAAM,OAAO,GAAG,0BAA0B,EAAE,CAAC;QAC7C,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,wBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAChF,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,0BAA0B;IACjC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC9B,CAAC;AAEY,QAAA,gBAAgB,GAAiC;IAC5D,MAAM,CAAC,OAAyB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACzE,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,wBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC1E;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,0BAA0B,EAAE,CAAC;QAC7C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,wBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACjE,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,wBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;IAC9F,CAAC;IAED,MAAM,CAAC,OAAyB;QAC9B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,wBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACpD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAoD,IAAQ;QAChE,OAAO,wBAAgB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC3D,CAAC;IACD,WAAW,CAAoD,MAAS;QACtE,MAAM,OAAO,GAAG,0BAA0B,EAAE,CAAC;QAC7C,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,wBAAgB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YAC5C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,0BAA0B;IACjC,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAC7B,CAAC;AAEY,QAAA,gBAAgB,GAAiC;IAC5D,MAAM,CAAC,OAAyB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACzE,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,0BAA0B,EAAE,CAAC;QAC7C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;IAC/E,CAAC;IAED,MAAM,CAAC,OAAyB;QAC9B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAoD,IAAQ;QAChE,OAAO,wBAAgB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC3D,CAAC;IACD,WAAW,CAAoD,MAAS;QACtE,MAAM,OAAO,GAAG,0BAA0B,EAAE,CAAC;QAC7C,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,yBAAyB;IAChC,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC;AAClD,CAAC;AAEY,QAAA,eAAe,GAAgC;IAC1D,MAAM,CAAC,OAAwB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACxE,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,CAAC,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3C;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SAC7C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC/C,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG;SAC/E,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAwB;QAC7B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE;YACvB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvC;QACD,IAAI,OAAO,CAAC,OAAO,KAAK,CAAC,EAAE;YACzB,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAC3C;QACD,IAAI,OAAO,CAAC,SAAS,KAAK,GAAG,EAAE;YAC7B,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;SACnC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAmD,IAAQ;QAC/D,OAAO,uBAAe,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC1D,CAAC;IACD,WAAW,CAAmD,MAAS;QACrE,MAAM,OAAO,GAAG,yBAAyB,EAAE,CAAC;QAC5C,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;QAClC,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,CAAC,CAAC;QACtC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,GAAG,CAAC;QAC5C,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,iCAAiC;IACxC,OAAO;QACL,KAAK,EAAE,SAAS;QAChB,IAAI,EAAE,SAAS;QACf,cAAc,EAAE,CAAC;QACjB,QAAQ,EAAE,CAAC;QACX,aAAa,EAAE,CAAC;QAChB,kBAAkB,EAAE,CAAC;QACrB,iBAAiB,EAAE,CAAC;KACrB,CAAC;AACJ,CAAC;AAEY,QAAA,uBAAuB,GAAwC;IAC1E,MAAM,CAAC,OAAgC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAChF,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5E;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5D;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACjD;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC3C;QACD,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SAChD;QACD,IAAI,OAAO,CAAC,kBAAkB,KAAK,CAAC,EAAE;YACpC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;SACrD;QACD,IAAI,OAAO,CAAC,iBAAiB,KAAK,CAAC,EAAE;YACnC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;SACpD;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,iCAAiC,EAAE,CAAC;QACpD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpE,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACxC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAClC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACvC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,kBAAkB,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAC5C,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,iBAAiB,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAC3C,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,2BAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;YACnF,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YACjE,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;YACzE,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF,kBAAkB,EAAE,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;YACvG,iBAAiB,EAAE,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;SACrG,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAgC;QACrC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;YAC9B,GAAG,CAAC,IAAI,GAAG,YAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACtC;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACzD;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC7C;QACD,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,EAAE;YAC/B,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,kBAAkB,KAAK,CAAC,EAAE;YACpC,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;SACjE;QACD,IAAI,OAAO,CAAC,iBAAiB,KAAK,CAAC,EAAE;YACnC,GAAG,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;SAC/D;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA2D,IAAQ;QACvE,OAAO,+BAAuB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAClE,CAAC;IACD,WAAW,CAA2D,MAAS;QAC7E,MAAM,OAAO,GAAG,iCAAiC,EAAE,CAAC;QACpD,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,2BAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,YAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/G,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC;QACxC,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC;QAClD,OAAO,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,IAAI,CAAC,CAAC;QAC5D,OAAO,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAC;QAC1D,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,cAAc;IACrB,OAAO;QACL,MAAM,EAAE,GAAG;QACX,QAAQ,EAAE,EAAE;QACZ,cAAc,EAAE,SAAS;QACzB,QAAQ,EAAE,EAAE;QACZ,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;QACV,UAAU,EAAE,GAAG;QACf,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,SAAS;KACtB,CAAC;AACJ,CAAC;AAEY,QAAA,IAAI,GAAqB;IACpC,MAAM,CAAC,OAAa,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC7D,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC5C;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;YACxC,sBAAc,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAChF;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC7C;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACzB,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAC3C;QACD,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE;YAC9B,4BAAoB,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACnE;QACD,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,EAAE,EAAE;YACjC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SAClD;QACD,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE;YACpC,kBAAU,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACzE;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,cAAc,EAAE,CAAC;QACjC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,CAAC,EAAE;wBACb,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC5C,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,sBAAc,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACxE,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACjC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,4BAAoB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBAC1E,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAChD,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACzC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,UAAU,GAAG,kBAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAChE,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG;YACrE,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1E,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,sBAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS;YACzG,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1E,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;YACpE,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC9C,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,4BAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACjE,CAAC,CAAC,EAAE;YACN,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG;YACjF,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5F,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,kBAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS;SAC1F,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAa;QAClB,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE;YAC1B,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;SAC7B;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACjC;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;YACxC,GAAG,CAAC,cAAc,GAAG,sBAAc,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACpE;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACjC;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACzB,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;SAC7B;QACD,IAAI,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE;YAC1B,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,4BAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACxE;QACD,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG,EAAE;YAC9B,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;SACrC;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,EAAE,EAAE;YACjC,GAAG,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;SAC7C;QACD,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE;YACpC,GAAG,CAAC,UAAU,GAAG,kBAAU,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SACxD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAwC,IAAQ;QACpD,OAAO,YAAI,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC/C,CAAC;IACD,WAAW,CAAwC,MAAS;QAC1D,MAAM,OAAO,GAAG,cAAc,EAAE,CAAC;QACjC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC;QACtC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACzC,OAAO,CAAC,cAAc,GAAG,CAAC,MAAM,CAAC,cAAc,KAAK,SAAS,IAAI,MAAM,CAAC,cAAc,KAAK,IAAI,CAAC;YAC9F,CAAC,CAAC,sBAAc,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC;YACnD,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACzC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;QACrC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,4BAAoB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACtF,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,GAAG,CAAC;QAC9C,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,EAAE,CAAC;QACrD,OAAO,CAAC,UAAU,GAAG,CAAC,MAAM,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI,CAAC;YAClF,CAAC,CAAC,kBAAU,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC;YAC3C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,oBAAoB;IAC3B,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;AACjF,CAAC;AAEY,QAAA,UAAU,GAA2B;IAChD,MAAM,CAAC,OAAmB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACnE,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SAChD;QACD,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SAChD;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,CAAC,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,UAAU,KAAK,CAAC,EAAE;YAC5B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SAC7C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,oBAAoB,EAAE,CAAC;QACvC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,CAAC,EAAE;wBACb,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACxC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACvC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACtC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACpC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;YACxF,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;YACrF,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SAChF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAmB;QACxB,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACzD;QACD,IAAI,OAAO,CAAC,aAAa,KAAK,CAAC,EAAE;YAC/B,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SACvD;QACD,IAAI,OAAO,CAAC,YAAY,KAAK,CAAC,EAAE;YAC9B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;SACrD;QACD,IAAI,OAAO,CAAC,UAAU,KAAK,CAAC,EAAE;YAC5B,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SACjD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA8C,IAAQ;QAC1D,OAAO,kBAAU,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACrD,CAAC;IACD,WAAW,CAA8C,MAAS;QAChE,MAAM,OAAO,GAAG,oBAAoB,EAAE,CAAC;QACvC,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC;QAClD,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,CAAC,CAAC;QAChD,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC;QAC5C,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,kBAAkB;IACzB,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AAChF,CAAC;AAEY,QAAA,QAAQ,GAAyB;IAC5C,MAAM,CAAC,OAAiB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACjE,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE;YAC1B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC5C;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;YACxC,sBAAc,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAChF;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC5C;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,kBAAkB,EAAE,CAAC;QACrC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,CAAC,EAAE;wBACb,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC5C,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,sBAAc,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACxE,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG;YACrE,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;YAC1E,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,sBAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS;YACzG,QAAQ,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;SAC3E,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAiB;QACtB,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE;YAC1B,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;SAC7B;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACjC;QACD,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;YACxC,GAAG,CAAC,cAAc,GAAG,sBAAc,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACpE;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,EAAE,EAAE;YAC3B,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACjC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA4C,IAAQ;QACxD,OAAO,gBAAQ,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACnD,CAAC;IACD,WAAW,CAA4C,MAAS;QAC9D,MAAM,OAAO,GAAG,kBAAkB,EAAE,CAAC;QACrC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC;QACtC,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACzC,OAAO,CAAC,cAAc,GAAG,CAAC,MAAM,CAAC,cAAc,KAAK,SAAS,IAAI,MAAM,CAAC,cAAc,KAAK,IAAI,CAAC;YAC9F,CAAC,CAAC,sBAAc,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC;YACnD,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QACzC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,wBAAwB;IAC/B,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AACtB,CAAC;AAEY,QAAA,cAAc,GAA+B;IACxD,MAAM,CAAC,OAAuB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACvE,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE;YAC5B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAE,CAAC,CAAC;SAC9B;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,wBAAwB,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACnC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACnH,CAAC;IAED,MAAM,CAAC,OAAuB;QAC5B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;YACxB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACzB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAkD,IAAQ;QAC9D,OAAO,sBAAc,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACzD,CAAC;IACD,WAAW,CAAkD,MAAS;QACpE,MAAM,OAAO,GAAG,wBAAwB,EAAE,CAAC;QAC3C,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAChD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,8BAA8B;IACrC,OAAO,EAAE,cAAc,EAAE,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,iBAAiB,EAAE,SAAS,EAAE,CAAC;AAC1F,CAAC;AAEY,QAAA,oBAAoB,GAAqC;IACpE,MAAM,CAAC,OAA6B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC7E,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACjD;QACD,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,WAAW,EAAE;YACnC,sBAAc,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC7D;QACD,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE;YAC9B,iBAAS,CAAC,MAAM,CAAC,CAAE,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACxD;QACD,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE;YAC3C,yBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SACtF;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACxC,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAc,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBACzE,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,GAAG,EAAE;wBACf,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAS,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;oBAC/D,SAAS;iBACV;gBACD,KAAK,EAAE,CAAC,CAAC;oBACP,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,iBAAiB,GAAG,yBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC9E,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,cAAc,EAAE,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC;gBACxD,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,sBAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAChE,CAAC,CAAC,EAAE;YACN,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,iBAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5G,iBAAiB,EAAE,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBAChD,CAAC,CAAC,yBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC;gBACtD,CAAC,CAAC,SAAS;SACd,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA6B;QAClC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,cAAc,KAAK,CAAC,EAAE;YAChC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACzD;QACD,IAAI,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE;YAC/B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,sBAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SAC5E;QACD,IAAI,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE;YAC1B,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iBAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7D;QACD,IAAI,OAAO,CAAC,iBAAiB,KAAK,SAAS,EAAE;YAC3C,GAAG,CAAC,iBAAiB,GAAG,yBAAiB,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;SAC7E;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAwD,IAAQ;QACpE,OAAO,4BAAoB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC/D,CAAC;IACD,WAAW,CAAwD,MAAS;QAC1E,MAAM,OAAO,GAAG,8BAA8B,EAAE,CAAC;QACjD,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,CAAC,CAAC;QACpD,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,sBAAc,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAC1F,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,iBAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAC3E,OAAO,CAAC,iBAAiB,GAAG,CAAC,MAAM,CAAC,iBAAiB,KAAK,SAAS,IAAI,MAAM,CAAC,iBAAiB,KAAK,IAAI,CAAC;YACvG,CAAC,CAAC,yBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,CAAC;YACzD,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,2BAA2B;IAClC,OAAO,EAAE,WAAW,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AACxC,CAAC;AAEY,QAAA,iBAAiB,GAAkC;IAC9D,MAAM,CAAC,OAA0B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC1E,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC/C;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACzC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,2BAA2B,EAAE,CAAC;QAC9C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBACtC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;YACnF,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;SAClE,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA0B;QAC/B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,WAAW,KAAK,EAAE,EAAE;YAC9B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;SACvC;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,EAAE,EAAE;YACxB,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;SAC3B;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAqD,IAAQ;QACjE,OAAO,yBAAiB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC5D,CAAC;IACD,WAAW,CAAqD,MAAS;QACvE,MAAM,OAAO,GAAG,2BAA2B,EAAE,CAAC;QAC9C,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;QAC/C,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,mBAAmB;IAC1B,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AAChC,CAAC;AAEY,QAAA,SAAS,GAA0B;IAC9C,MAAM,CAAC,OAAkB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAClE,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC;QACtC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/B,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/B,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9D,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;SAC/D,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAkB;QACvB,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACzB;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACzB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA6C,IAAQ;QACzD,OAAO,iBAAS,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACpD,CAAC;IACD,WAAW,CAA6C,MAAS;QAC/D,MAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC;QACtC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,wBAAwB;IAC/B,OAAO,EAAE,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC9C,CAAC;AAEY,QAAA,cAAc,GAA+B;IACxD,MAAM,CAAC,OAAuB,EAAE,SAAuB,IAAI,mBAAY,EAAE;QACvE,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE;YAC7B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC7C;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;SAC5E;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,wBAAwB,EAAE,CAAC;QAC3C,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,CAAC,EAAE;wBACb,MAAM;qBACP;oBAED,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBACrC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;oBACpE,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YAClF,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,2BAAmB,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;SACpF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAuB;QAC5B,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE;YAC7B,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SACnD;QACD,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE;YAC/B,GAAG,CAAC,KAAK,GAAG,2BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SACvD;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAkD,IAAQ;QAC9D,OAAO,sBAAc,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IACzD,CAAC;IACD,WAAW,CAAkD,MAAS;QACpE,MAAM,OAAO,GAAG,wBAAwB,EAAE,CAAC;QAC3C,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC;QAC9C,OAAO,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC;YACnE,CAAC,CAAC,2BAAmB,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,CAAC,CAAC,SAAS,CAAC;QACd,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,6BAA6B;IACpC,OAAO,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC;AACrB,CAAC;AAEY,QAAA,mBAAmB,GAAoC;IAClE,MAAM,CAAC,OAA4B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC5E,IAAI,OAAO,CAAC,GAAG,KAAK,EAAE,EAAE;YACtB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SACvC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,6BAA6B,EAAE,CAAC;QAChD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC9B,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACzE,CAAC;IAED,MAAM,CAAC,OAA4B;QACjC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,GAAG,KAAK,EAAE,EAAE;YACtB,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;SACvB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAuD,IAAQ;QACnE,OAAO,2BAAmB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC9D,CAAC;IACD,WAAW,CAAuD,MAAS;QACzE,MAAM,OAAO,GAAG,6BAA6B,EAAE,CAAC;QAChD,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,EAAE,CAAC;QAC/B,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,iCAAiC;IACxC,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AAC1D,CAAC;AAEY,QAAA,uBAAuB,GAAwC;IAC1E,MAAM,CAAC,OAAgC,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAChF,IAAI,OAAO,CAAC,EAAE,KAAK,GAAG,EAAE;YACtB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SACtC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxC;QACD,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SACzC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,iCAAiC,EAAE,CAAC;QACpD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACxC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/B,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;oBAChC,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG;YACzD,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9D,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;SAClF,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAAgC;QACrC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,EAAE,KAAK,GAAG,EAAE;YACtB,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;SACrB;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACzB;QACD,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,GAAG,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;SAC9C;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAA2D,IAAQ;QACvE,OAAO,+BAAuB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAClE,CAAC;IACD,WAAW,CAA2D,MAAS;QAC7E,MAAM,OAAO,GAAG,iCAAiC,EAAE,CAAC;QACpD,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,GAAG,CAAC;QAC9B,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,6BAA6B;IACpC,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;AAC/B,CAAC;AAEY,QAAA,mBAAmB,GAAoC;IAClE,MAAM,CAAC,OAA4B,EAAE,SAAuB,IAAI,mBAAY,EAAE;QAC5E,IAAI,OAAO,CAAC,EAAE,KAAK,GAAG,EAAE;YACtB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SACtC;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACxC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,KAAgC,EAAE,MAAe;QACtD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAY,CAAC,KAAK,CAAC,CAAC;QAC/E,IAAI,GAAG,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAClE,MAAM,OAAO,GAAG,6BAA6B,EAAE,CAAC;QAChD,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,EAAE;YACvB,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YAC5B,QAAQ,GAAG,KAAK,CAAC,EAAE;gBACjB,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACxC,SAAS;iBACV;gBACD,KAAK,CAAC,CAAC,CAAC;oBACN,IAAI,GAAG,KAAK,EAAE,EAAE;wBACd,MAAM;qBACP;oBAED,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/B,SAAS;iBACV;aACF;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;gBAChC,MAAM;aACP;YACD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,QAAQ,CAAC,MAAW;QAClB,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG;YACzD,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;SAC/D,CAAC;IACJ,CAAC;IAED,MAAM,CAAC,OAA4B;QACjC,MAAM,GAAG,GAAQ,EAAE,CAAC;QACpB,IAAI,OAAO,CAAC,EAAE,KAAK,GAAG,EAAE;YACtB,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;SACrB;QACD,IAAI,OAAO,CAAC,IAAI,KAAK,EAAE,EAAE;YACvB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;SACzB;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAuD,IAAQ;QACnE,OAAO,2BAAmB,CAAC,WAAW,CAAC,IAAI,IAAK,EAAU,CAAC,CAAC;IAC9D,CAAC;IACD,WAAW,CAAuD,MAAS;QACzE,MAAM,OAAO,GAAG,6BAA6B,EAAE,CAAC;QAChD,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,GAAG,CAAC;QAC9B,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;QACjC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAC;AAEF,SAAS,eAAe,CAAC,GAAW;IAClC,IAAK,UAAkB,CAAC,MAAM,EAAE;QAC9B,OAAO,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC/D;SAAM;QACL,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACnC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SAC5B;QACD,OAAO,GAAG,CAAC;KACZ;AACH,CAAC;AAED,SAAS,eAAe,CAAC,GAAe;IACtC,IAAK,UAAkB,CAAC,MAAM,EAAE;QAC9B,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;KACvD;SAAM;QACL,MAAM,GAAG,GAAa,EAAE,CAAC;QACzB,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACnB,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QACH,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;KACtC;AACH,CAAC;AAcD,SAAS,KAAK,CAAC,KAAU;IACvB,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC/C,CAAC", "sourcesContent": ["// Code generated by protoc-gen-ts_proto. DO NOT EDIT.\n// versions:\n//   protoc-gen-ts_proto  v2.7.0\n//   protoc               v5.27.1\n// source: tiktok-schema.proto\n\n/* eslint-disable */\nimport { BinaryReader, BinaryWriter } from \"@bufbuild/protobuf/wire\";\n\nexport const protobufPackage = \"TikTok\";\n\nexport enum ControlAction {\n  CONTROL_ACTION_FALLBACK_UNKNOWN = 0,\n  CONTROL_ACTION_STREAM_PAUSED = 1,\n  CONTROL_ACTION_STREAM_UNPAUSED = 2,\n  CONTROL_ACTION_STREAM_ENDED = 3,\n  CONTROL_ACTION_STREAM_SUSPENDED = 4,\n  UNRECOGNIZED = -1,\n}\n\nexport function controlActionFromJSON(object: any): ControlAction {\n  switch (object) {\n    case 0:\n    case \"CONTROL_ACTION_FALLBACK_UNKNOWN\":\n      return ControlAction.CONTROL_ACTION_FALLBACK_UNKNOWN;\n    case 1:\n    case \"CONTROL_ACTION_STREAM_PAUSED\":\n      return ControlAction.CONTROL_ACTION_STREAM_PAUSED;\n    case 2:\n    case \"CONTROL_ACTION_STREAM_UNPAUSED\":\n      return ControlAction.CONTROL_ACTION_STREAM_UNPAUSED;\n    case 3:\n    case \"CONTROL_ACTION_STREAM_ENDED\":\n      return ControlAction.CONTROL_ACTION_STREAM_ENDED;\n    case 4:\n    case \"CONTROL_ACTION_STREAM_SUSPENDED\":\n      return ControlAction.CONTROL_ACTION_STREAM_SUSPENDED;\n    case -1:\n    case \"UNRECOGNIZED\":\n    default:\n      return ControlAction.UNRECOGNIZED;\n  }\n}\n\nexport function controlActionToJSON(object: ControlAction): string {\n  switch (object) {\n    case ControlAction.CONTROL_ACTION_FALLBACK_UNKNOWN:\n      return \"CONTROL_ACTION_FALLBACK_UNKNOWN\";\n    case ControlAction.CONTROL_ACTION_STREAM_PAUSED:\n      return \"CONTROL_ACTION_STREAM_PAUSED\";\n    case ControlAction.CONTROL_ACTION_STREAM_UNPAUSED:\n      return \"CONTROL_ACTION_STREAM_UNPAUSED\";\n    case ControlAction.CONTROL_ACTION_STREAM_ENDED:\n      return \"CONTROL_ACTION_STREAM_ENDED\";\n    case ControlAction.CONTROL_ACTION_STREAM_SUSPENDED:\n      return \"CONTROL_ACTION_STREAM_SUSPENDED\";\n    case ControlAction.UNRECOGNIZED:\n    default:\n      return \"UNRECOGNIZED\";\n  }\n}\n\n/** Data structure from im/fetch/ response */\nexport interface WebcastResponse {\n  messages: Message[];\n  cursor: string;\n  fetchInterval: number;\n  serverTimestamp: string;\n  internalExt: string;\n  /** ws (1) or polling (2) */\n  fetchType: number;\n  wsParams: WebsocketParam[];\n  heartbeatDuration: number;\n  needAck: boolean;\n  wsUrl: string;\n}\n\nexport interface Message {\n  type: string;\n  binary: Uint8Array;\n}\n\nexport interface WebsocketParam {\n  name: string;\n  value: string;\n}\n\n/** Message types depending on Message.tyoe */\nexport interface WebcastControlMessage {\n  action: ControlAction;\n}\n\n/** Statistics like viewer count */\nexport interface WebcastRoomUserSeqMessage {\n  topViewers: TopUser[];\n  viewerCount: number;\n}\n\nexport interface TopUser {\n  coinCount: string;\n  user: User | undefined;\n}\n\nexport interface WebcastChatMessage {\n  event: WebcastMessageEvent | undefined;\n  user: User | undefined;\n  comment: string;\n  emotes: WebcastSubEmote[];\n}\n\n/** Chat Emotes (Subscriber) */\nexport interface WebcastEmoteChatMessage {\n  user: User | undefined;\n  emote: EmoteDetails | undefined;\n}\n\nexport interface WebcastSubEmote {\n  /** starting at 0, you insert the emote itself into the comment at that place */\n  placeInComment: number;\n  emote: EmoteDetails | undefined;\n}\n\nexport interface WebcastMemberMessage {\n  event: WebcastMessageEvent | undefined;\n  user: User | undefined;\n  actionId: number;\n}\n\nexport interface WebcastGiftMessage {\n  event: WebcastMessageEvent | undefined;\n  giftId: number;\n  repeatCount: number;\n  user: User | undefined;\n  repeatEnd: number;\n  groupId: string;\n  giftDetails: WebcastGiftMessageGiftDetails | undefined;\n  monitorExtra: string;\n  giftExtra: WebcastGiftMessageGiftExtra | undefined;\n}\n\nexport interface WebcastGiftMessageGiftDetails {\n  giftImage: WebcastGiftMessageGiftImage | undefined;\n  giftName: string;\n  describe: string;\n  giftType: number;\n  diamondCount: number;\n}\n\n/** Taken from https://github.com/Davincible/gotiktoklive/blob/da4630622bc586629a53faae64e8c53509af29de/proto/tiktok.proto#L57 */\nexport interface WebcastGiftMessageGiftExtra {\n  timestamp: string;\n  receiverUserId: string;\n}\n\nexport interface WebcastGiftMessageGiftImage {\n  giftPictureUrl: string;\n}\n\n/** Battle start */\nexport interface WebcastLinkMicBattle {\n  battleUsers: WebcastLinkMicBattleItems[];\n}\n\nexport interface WebcastLinkMicBattleItems {\n  battleGroup: WebcastLinkMicBattleGroup | undefined;\n}\n\nexport interface WebcastLinkMicBattleGroup {\n  user: LinkUser | undefined;\n}\n\n/** Battle status */\nexport interface WebcastLinkMicArmies {\n  battleItems: WebcastLinkMicArmiesItems[];\n  battleStatus: number;\n}\n\nexport interface WebcastLinkMicArmiesItems {\n  hostUserId: string;\n  battleGroups: WebcastLinkMicArmiesGroup[];\n}\n\nexport interface WebcastLinkMicArmiesGroup {\n  users: User[];\n  points: number;\n}\n\n/** Follow & share event */\nexport interface WebcastSocialMessage {\n  event: WebcastMessageEvent | undefined;\n  user: User | undefined;\n}\n\n/** Like event (is only sent from time to time, not with every like) */\nexport interface WebcastLikeMessage {\n  event: WebcastMessageEvent | undefined;\n  user: User | undefined;\n  likeCount: number;\n  totalLikeCount: number;\n}\n\n/** New question event */\nexport interface WebcastQuestionNewMessage {\n  questionDetails: QuestionDetails | undefined;\n}\n\nexport interface QuestionDetails {\n  questionText: string;\n  user: User | undefined;\n}\n\nexport interface WebcastMessageEvent {\n  msgId: string;\n  createTime: string;\n  eventDetails: WebcastMessageEventDetails | undefined;\n}\n\n/** Contains UI information */\nexport interface WebcastMessageEventDetails {\n  displayType: string;\n  label: string;\n}\n\n/** Source: Co-opted https://github.com/zerodytrash/TikTok-Livestream-Chat-Connector/issues/19#issuecomment-1074150342 */\nexport interface WebcastLiveIntroMessage {\n  id: string;\n  description: string;\n  user: User | undefined;\n}\n\nexport interface SystemMessage {\n  description: string;\n}\n\nexport interface WebcastInRoomBannerMessage {\n  data: string;\n}\n\nexport interface RankItem {\n  colour: string;\n  id: string;\n}\n\nexport interface WeeklyRanking {\n  type: string;\n  label: string;\n  rank: RankItem | undefined;\n}\n\nexport interface RankContainer {\n  rankings: WeeklyRanking | undefined;\n}\n\nexport interface WebcastHourlyRankMessage {\n  data: RankContainer | undefined;\n}\n\nexport interface EmoteDetails {\n  emoteId: string;\n  image: EmoteImage | undefined;\n}\n\nexport interface EmoteImage {\n  imageUrl: string;\n}\n\n/**\n * Envelope (treasure boxes)\n * Taken from https://github.com/ThanoFish/TikTok-Live-Connector/blob/9b215b96792adfddfb638344b152fa9efa581b4c/src/proto/tiktokSchema.proto\n */\nexport interface WebcastEnvelopeMessage {\n  treasureBoxData: TreasureBoxData | undefined;\n  treasureBoxUser: TreasureBoxUser | undefined;\n}\n\nexport interface TreasureBoxUser {\n  user2: TreasureBoxUser2 | undefined;\n}\n\nexport interface TreasureBoxUser2 {\n  user3: TreasureBoxUser3[];\n}\n\nexport interface TreasureBoxUser3 {\n  user4: TreasureBoxUser4 | undefined;\n}\n\nexport interface TreasureBoxUser4 {\n  user: User | undefined;\n}\n\nexport interface TreasureBoxData {\n  coins: number;\n  canOpen: number;\n  timestamp: string;\n}\n\n/** New Subscriber message */\nexport interface WebcastSubNotifyMessage {\n  event: WebcastMessageEvent | undefined;\n  user: User | undefined;\n  exhibitionType: number;\n  subMonth: number;\n  subscribeType: number;\n  oldSubscribeStatus: number;\n  subscribingStatus: number;\n}\n\nexport interface User {\n  userId: string;\n  nickname: string;\n  profilePicture: ProfilePicture | undefined;\n  uniqueId: string;\n  secUid: string;\n  badges: UserBadgesAttributes[];\n  createTime: string;\n  bioDescription: string;\n  followInfo: FollowInfo | undefined;\n}\n\nexport interface FollowInfo {\n  followingCount: number;\n  followerCount: number;\n  followStatus: number;\n  pushStatus: number;\n}\n\nexport interface LinkUser {\n  userId: string;\n  nickname: string;\n  profilePicture: ProfilePicture | undefined;\n  uniqueId: string;\n}\n\nexport interface ProfilePicture {\n  urls: string[];\n}\n\nexport interface UserBadgesAttributes {\n  badgeSceneType: number;\n  imageBadges: UserImageBadge[];\n  badges: UserBadge[];\n  privilegeLogExtra: PrivilegeLogExtra | undefined;\n}\n\nexport interface PrivilegeLogExtra {\n  privilegeId: string;\n  level: string;\n}\n\nexport interface UserBadge {\n  type: string;\n  name: string;\n}\n\nexport interface UserImageBadge {\n  displayType: number;\n  image: UserImageBadgeImage | undefined;\n}\n\nexport interface UserImageBadgeImage {\n  url: string;\n}\n\n/** Websocket incoming message structure */\nexport interface WebcastWebsocketMessage {\n  id: string;\n  type: string;\n  binary: Uint8Array;\n}\n\n/** Websocket acknowledgment message */\nexport interface WebcastWebsocketAck {\n  id: string;\n  type: string;\n}\n\nfunction createBaseWebcastResponse(): WebcastResponse {\n  return {\n    messages: [],\n    cursor: \"\",\n    fetchInterval: 0,\n    serverTimestamp: \"0\",\n    internalExt: \"\",\n    fetchType: 0,\n    wsParams: [],\n    heartbeatDuration: 0,\n    needAck: false,\n    wsUrl: \"\",\n  };\n}\n\nexport const WebcastResponse: MessageFns<WebcastResponse> = {\n  encode(message: WebcastResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    for (const v of message.messages) {\n      Message.encode(v!, writer.uint32(10).fork()).join();\n    }\n    if (message.cursor !== \"\") {\n      writer.uint32(18).string(message.cursor);\n    }\n    if (message.fetchInterval !== 0) {\n      writer.uint32(24).int32(message.fetchInterval);\n    }\n    if (message.serverTimestamp !== \"0\") {\n      writer.uint32(32).int64(message.serverTimestamp);\n    }\n    if (message.internalExt !== \"\") {\n      writer.uint32(42).string(message.internalExt);\n    }\n    if (message.fetchType !== 0) {\n      writer.uint32(48).int32(message.fetchType);\n    }\n    for (const v of message.wsParams) {\n      WebsocketParam.encode(v!, writer.uint32(58).fork()).join();\n    }\n    if (message.heartbeatDuration !== 0) {\n      writer.uint32(64).int32(message.heartbeatDuration);\n    }\n    if (message.needAck !== false) {\n      writer.uint32(72).bool(message.needAck);\n    }\n    if (message.wsUrl !== \"\") {\n      writer.uint32(82).string(message.wsUrl);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastResponse {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastResponse();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.messages.push(Message.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.cursor = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 24) {\n            break;\n          }\n\n          message.fetchInterval = reader.int32();\n          continue;\n        }\n        case 4: {\n          if (tag !== 32) {\n            break;\n          }\n\n          message.serverTimestamp = reader.int64().toString();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.internalExt = reader.string();\n          continue;\n        }\n        case 6: {\n          if (tag !== 48) {\n            break;\n          }\n\n          message.fetchType = reader.int32();\n          continue;\n        }\n        case 7: {\n          if (tag !== 58) {\n            break;\n          }\n\n          message.wsParams.push(WebsocketParam.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 8: {\n          if (tag !== 64) {\n            break;\n          }\n\n          message.heartbeatDuration = reader.int32();\n          continue;\n        }\n        case 9: {\n          if (tag !== 72) {\n            break;\n          }\n\n          message.needAck = reader.bool();\n          continue;\n        }\n        case 10: {\n          if (tag !== 82) {\n            break;\n          }\n\n          message.wsUrl = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastResponse {\n    return {\n      messages: globalThis.Array.isArray(object?.messages) ? object.messages.map((e: any) => Message.fromJSON(e)) : [],\n      cursor: isSet(object.cursor) ? globalThis.String(object.cursor) : \"\",\n      fetchInterval: isSet(object.fetchInterval) ? globalThis.Number(object.fetchInterval) : 0,\n      serverTimestamp: isSet(object.serverTimestamp) ? globalThis.String(object.serverTimestamp) : \"0\",\n      internalExt: isSet(object.internalExt) ? globalThis.String(object.internalExt) : \"\",\n      fetchType: isSet(object.fetchType) ? globalThis.Number(object.fetchType) : 0,\n      wsParams: globalThis.Array.isArray(object?.wsParams)\n        ? object.wsParams.map((e: any) => WebsocketParam.fromJSON(e))\n        : [],\n      heartbeatDuration: isSet(object.heartbeatDuration) ? globalThis.Number(object.heartbeatDuration) : 0,\n      needAck: isSet(object.needAck) ? globalThis.Boolean(object.needAck) : false,\n      wsUrl: isSet(object.wsUrl) ? globalThis.String(object.wsUrl) : \"\",\n    };\n  },\n\n  toJSON(message: WebcastResponse): unknown {\n    const obj: any = {};\n    if (message.messages?.length) {\n      obj.messages = message.messages.map((e) => Message.toJSON(e));\n    }\n    if (message.cursor !== \"\") {\n      obj.cursor = message.cursor;\n    }\n    if (message.fetchInterval !== 0) {\n      obj.fetchInterval = Math.round(message.fetchInterval);\n    }\n    if (message.serverTimestamp !== \"0\") {\n      obj.serverTimestamp = message.serverTimestamp;\n    }\n    if (message.internalExt !== \"\") {\n      obj.internalExt = message.internalExt;\n    }\n    if (message.fetchType !== 0) {\n      obj.fetchType = Math.round(message.fetchType);\n    }\n    if (message.wsParams?.length) {\n      obj.wsParams = message.wsParams.map((e) => WebsocketParam.toJSON(e));\n    }\n    if (message.heartbeatDuration !== 0) {\n      obj.heartbeatDuration = Math.round(message.heartbeatDuration);\n    }\n    if (message.needAck !== false) {\n      obj.needAck = message.needAck;\n    }\n    if (message.wsUrl !== \"\") {\n      obj.wsUrl = message.wsUrl;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastResponse>, I>>(base?: I): WebcastResponse {\n    return WebcastResponse.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastResponse>, I>>(object: I): WebcastResponse {\n    const message = createBaseWebcastResponse();\n    message.messages = object.messages?.map((e) => Message.fromPartial(e)) || [];\n    message.cursor = object.cursor ?? \"\";\n    message.fetchInterval = object.fetchInterval ?? 0;\n    message.serverTimestamp = object.serverTimestamp ?? \"0\";\n    message.internalExt = object.internalExt ?? \"\";\n    message.fetchType = object.fetchType ?? 0;\n    message.wsParams = object.wsParams?.map((e) => WebsocketParam.fromPartial(e)) || [];\n    message.heartbeatDuration = object.heartbeatDuration ?? 0;\n    message.needAck = object.needAck ?? false;\n    message.wsUrl = object.wsUrl ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseMessage(): Message {\n  return { type: \"\", binary: new Uint8Array(0) };\n}\n\nexport const Message: MessageFns<Message> = {\n  encode(message: Message, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.type !== \"\") {\n      writer.uint32(10).string(message.type);\n    }\n    if (message.binary.length !== 0) {\n      writer.uint32(18).bytes(message.binary);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): Message {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.type = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.binary = reader.bytes();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): Message {\n    return {\n      type: isSet(object.type) ? globalThis.String(object.type) : \"\",\n      binary: isSet(object.binary) ? bytesFromBase64(object.binary) : new Uint8Array(0),\n    };\n  },\n\n  toJSON(message: Message): unknown {\n    const obj: any = {};\n    if (message.type !== \"\") {\n      obj.type = message.type;\n    }\n    if (message.binary.length !== 0) {\n      obj.binary = base64FromBytes(message.binary);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<Message>, I>>(base?: I): Message {\n    return Message.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<Message>, I>>(object: I): Message {\n    const message = createBaseMessage();\n    message.type = object.type ?? \"\";\n    message.binary = object.binary ?? new Uint8Array(0);\n    return message;\n  },\n};\n\nfunction createBaseWebsocketParam(): WebsocketParam {\n  return { name: \"\", value: \"\" };\n}\n\nexport const WebsocketParam: MessageFns<WebsocketParam> = {\n  encode(message: WebsocketParam, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.name !== \"\") {\n      writer.uint32(10).string(message.name);\n    }\n    if (message.value !== \"\") {\n      writer.uint32(18).string(message.value);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebsocketParam {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebsocketParam();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.name = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.value = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebsocketParam {\n    return {\n      name: isSet(object.name) ? globalThis.String(object.name) : \"\",\n      value: isSet(object.value) ? globalThis.String(object.value) : \"\",\n    };\n  },\n\n  toJSON(message: WebsocketParam): unknown {\n    const obj: any = {};\n    if (message.name !== \"\") {\n      obj.name = message.name;\n    }\n    if (message.value !== \"\") {\n      obj.value = message.value;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebsocketParam>, I>>(base?: I): WebsocketParam {\n    return WebsocketParam.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebsocketParam>, I>>(object: I): WebsocketParam {\n    const message = createBaseWebsocketParam();\n    message.name = object.name ?? \"\";\n    message.value = object.value ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseWebcastControlMessage(): WebcastControlMessage {\n  return { action: 0 };\n}\n\nexport const WebcastControlMessage: MessageFns<WebcastControlMessage> = {\n  encode(message: WebcastControlMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.action !== 0) {\n      writer.uint32(16).int32(message.action);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastControlMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastControlMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.action = reader.int32() as any;\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastControlMessage {\n    return { action: isSet(object.action) ? controlActionFromJSON(object.action) : 0 };\n  },\n\n  toJSON(message: WebcastControlMessage): unknown {\n    const obj: any = {};\n    if (message.action !== 0) {\n      obj.action = controlActionToJSON(message.action);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastControlMessage>, I>>(base?: I): WebcastControlMessage {\n    return WebcastControlMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastControlMessage>, I>>(object: I): WebcastControlMessage {\n    const message = createBaseWebcastControlMessage();\n    message.action = object.action ?? 0;\n    return message;\n  },\n};\n\nfunction createBaseWebcastRoomUserSeqMessage(): WebcastRoomUserSeqMessage {\n  return { topViewers: [], viewerCount: 0 };\n}\n\nexport const WebcastRoomUserSeqMessage: MessageFns<WebcastRoomUserSeqMessage> = {\n  encode(message: WebcastRoomUserSeqMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    for (const v of message.topViewers) {\n      TopUser.encode(v!, writer.uint32(18).fork()).join();\n    }\n    if (message.viewerCount !== 0) {\n      writer.uint32(24).int32(message.viewerCount);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastRoomUserSeqMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastRoomUserSeqMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.topViewers.push(TopUser.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 3: {\n          if (tag !== 24) {\n            break;\n          }\n\n          message.viewerCount = reader.int32();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastRoomUserSeqMessage {\n    return {\n      topViewers: globalThis.Array.isArray(object?.topViewers)\n        ? object.topViewers.map((e: any) => TopUser.fromJSON(e))\n        : [],\n      viewerCount: isSet(object.viewerCount) ? globalThis.Number(object.viewerCount) : 0,\n    };\n  },\n\n  toJSON(message: WebcastRoomUserSeqMessage): unknown {\n    const obj: any = {};\n    if (message.topViewers?.length) {\n      obj.topViewers = message.topViewers.map((e) => TopUser.toJSON(e));\n    }\n    if (message.viewerCount !== 0) {\n      obj.viewerCount = Math.round(message.viewerCount);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastRoomUserSeqMessage>, I>>(base?: I): WebcastRoomUserSeqMessage {\n    return WebcastRoomUserSeqMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastRoomUserSeqMessage>, I>>(object: I): WebcastRoomUserSeqMessage {\n    const message = createBaseWebcastRoomUserSeqMessage();\n    message.topViewers = object.topViewers?.map((e) => TopUser.fromPartial(e)) || [];\n    message.viewerCount = object.viewerCount ?? 0;\n    return message;\n  },\n};\n\nfunction createBaseTopUser(): TopUser {\n  return { coinCount: \"0\", user: undefined };\n}\n\nexport const TopUser: MessageFns<TopUser> = {\n  encode(message: TopUser, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.coinCount !== \"0\") {\n      writer.uint32(8).uint64(message.coinCount);\n    }\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TopUser {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTopUser();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.coinCount = reader.uint64().toString();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): TopUser {\n    return {\n      coinCount: isSet(object.coinCount) ? globalThis.String(object.coinCount) : \"0\",\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n    };\n  },\n\n  toJSON(message: TopUser): unknown {\n    const obj: any = {};\n    if (message.coinCount !== \"0\") {\n      obj.coinCount = message.coinCount;\n    }\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<TopUser>, I>>(base?: I): TopUser {\n    return TopUser.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TopUser>, I>>(object: I): TopUser {\n    const message = createBaseTopUser();\n    message.coinCount = object.coinCount ?? \"0\";\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastChatMessage(): WebcastChatMessage {\n  return { event: undefined, user: undefined, comment: \"\", emotes: [] };\n}\n\nexport const WebcastChatMessage: MessageFns<WebcastChatMessage> = {\n  encode(message: WebcastChatMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.event !== undefined) {\n      WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();\n    }\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(18).fork()).join();\n    }\n    if (message.comment !== \"\") {\n      writer.uint32(26).string(message.comment);\n    }\n    for (const v of message.emotes) {\n      WebcastSubEmote.encode(v!, writer.uint32(106).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastChatMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastChatMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.event = WebcastMessageEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.comment = reader.string();\n          continue;\n        }\n        case 13: {\n          if (tag !== 106) {\n            break;\n          }\n\n          message.emotes.push(WebcastSubEmote.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastChatMessage {\n    return {\n      event: isSet(object.event) ? WebcastMessageEvent.fromJSON(object.event) : undefined,\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n      comment: isSet(object.comment) ? globalThis.String(object.comment) : \"\",\n      emotes: globalThis.Array.isArray(object?.emotes)\n        ? object.emotes.map((e: any) => WebcastSubEmote.fromJSON(e))\n        : [],\n    };\n  },\n\n  toJSON(message: WebcastChatMessage): unknown {\n    const obj: any = {};\n    if (message.event !== undefined) {\n      obj.event = WebcastMessageEvent.toJSON(message.event);\n    }\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    if (message.comment !== \"\") {\n      obj.comment = message.comment;\n    }\n    if (message.emotes?.length) {\n      obj.emotes = message.emotes.map((e) => WebcastSubEmote.toJSON(e));\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastChatMessage>, I>>(base?: I): WebcastChatMessage {\n    return WebcastChatMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastChatMessage>, I>>(object: I): WebcastChatMessage {\n    const message = createBaseWebcastChatMessage();\n    message.event = (object.event !== undefined && object.event !== null)\n      ? WebcastMessageEvent.fromPartial(object.event)\n      : undefined;\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    message.comment = object.comment ?? \"\";\n    message.emotes = object.emotes?.map((e) => WebcastSubEmote.fromPartial(e)) || [];\n    return message;\n  },\n};\n\nfunction createBaseWebcastEmoteChatMessage(): WebcastEmoteChatMessage {\n  return { user: undefined, emote: undefined };\n}\n\nexport const WebcastEmoteChatMessage: MessageFns<WebcastEmoteChatMessage> = {\n  encode(message: WebcastEmoteChatMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(18).fork()).join();\n    }\n    if (message.emote !== undefined) {\n      EmoteDetails.encode(message.emote, writer.uint32(26).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastEmoteChatMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastEmoteChatMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.emote = EmoteDetails.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastEmoteChatMessage {\n    return {\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n      emote: isSet(object.emote) ? EmoteDetails.fromJSON(object.emote) : undefined,\n    };\n  },\n\n  toJSON(message: WebcastEmoteChatMessage): unknown {\n    const obj: any = {};\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    if (message.emote !== undefined) {\n      obj.emote = EmoteDetails.toJSON(message.emote);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastEmoteChatMessage>, I>>(base?: I): WebcastEmoteChatMessage {\n    return WebcastEmoteChatMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastEmoteChatMessage>, I>>(object: I): WebcastEmoteChatMessage {\n    const message = createBaseWebcastEmoteChatMessage();\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    message.emote = (object.emote !== undefined && object.emote !== null)\n      ? EmoteDetails.fromPartial(object.emote)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastSubEmote(): WebcastSubEmote {\n  return { placeInComment: 0, emote: undefined };\n}\n\nexport const WebcastSubEmote: MessageFns<WebcastSubEmote> = {\n  encode(message: WebcastSubEmote, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.placeInComment !== 0) {\n      writer.uint32(8).int32(message.placeInComment);\n    }\n    if (message.emote !== undefined) {\n      EmoteDetails.encode(message.emote, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastSubEmote {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastSubEmote();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.placeInComment = reader.int32();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.emote = EmoteDetails.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastSubEmote {\n    return {\n      placeInComment: isSet(object.placeInComment) ? globalThis.Number(object.placeInComment) : 0,\n      emote: isSet(object.emote) ? EmoteDetails.fromJSON(object.emote) : undefined,\n    };\n  },\n\n  toJSON(message: WebcastSubEmote): unknown {\n    const obj: any = {};\n    if (message.placeInComment !== 0) {\n      obj.placeInComment = Math.round(message.placeInComment);\n    }\n    if (message.emote !== undefined) {\n      obj.emote = EmoteDetails.toJSON(message.emote);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastSubEmote>, I>>(base?: I): WebcastSubEmote {\n    return WebcastSubEmote.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastSubEmote>, I>>(object: I): WebcastSubEmote {\n    const message = createBaseWebcastSubEmote();\n    message.placeInComment = object.placeInComment ?? 0;\n    message.emote = (object.emote !== undefined && object.emote !== null)\n      ? EmoteDetails.fromPartial(object.emote)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastMemberMessage(): WebcastMemberMessage {\n  return { event: undefined, user: undefined, actionId: 0 };\n}\n\nexport const WebcastMemberMessage: MessageFns<WebcastMemberMessage> = {\n  encode(message: WebcastMemberMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.event !== undefined) {\n      WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();\n    }\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(18).fork()).join();\n    }\n    if (message.actionId !== 0) {\n      writer.uint32(80).int32(message.actionId);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastMemberMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastMemberMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.event = WebcastMessageEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n        case 10: {\n          if (tag !== 80) {\n            break;\n          }\n\n          message.actionId = reader.int32();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastMemberMessage {\n    return {\n      event: isSet(object.event) ? WebcastMessageEvent.fromJSON(object.event) : undefined,\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n      actionId: isSet(object.actionId) ? globalThis.Number(object.actionId) : 0,\n    };\n  },\n\n  toJSON(message: WebcastMemberMessage): unknown {\n    const obj: any = {};\n    if (message.event !== undefined) {\n      obj.event = WebcastMessageEvent.toJSON(message.event);\n    }\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    if (message.actionId !== 0) {\n      obj.actionId = Math.round(message.actionId);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastMemberMessage>, I>>(base?: I): WebcastMemberMessage {\n    return WebcastMemberMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastMemberMessage>, I>>(object: I): WebcastMemberMessage {\n    const message = createBaseWebcastMemberMessage();\n    message.event = (object.event !== undefined && object.event !== null)\n      ? WebcastMessageEvent.fromPartial(object.event)\n      : undefined;\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    message.actionId = object.actionId ?? 0;\n    return message;\n  },\n};\n\nfunction createBaseWebcastGiftMessage(): WebcastGiftMessage {\n  return {\n    event: undefined,\n    giftId: 0,\n    repeatCount: 0,\n    user: undefined,\n    repeatEnd: 0,\n    groupId: \"0\",\n    giftDetails: undefined,\n    monitorExtra: \"\",\n    giftExtra: undefined,\n  };\n}\n\nexport const WebcastGiftMessage: MessageFns<WebcastGiftMessage> = {\n  encode(message: WebcastGiftMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.event !== undefined) {\n      WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();\n    }\n    if (message.giftId !== 0) {\n      writer.uint32(16).int32(message.giftId);\n    }\n    if (message.repeatCount !== 0) {\n      writer.uint32(40).int32(message.repeatCount);\n    }\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(58).fork()).join();\n    }\n    if (message.repeatEnd !== 0) {\n      writer.uint32(72).int32(message.repeatEnd);\n    }\n    if (message.groupId !== \"0\") {\n      writer.uint32(88).uint64(message.groupId);\n    }\n    if (message.giftDetails !== undefined) {\n      WebcastGiftMessageGiftDetails.encode(message.giftDetails, writer.uint32(122).fork()).join();\n    }\n    if (message.monitorExtra !== \"\") {\n      writer.uint32(178).string(message.monitorExtra);\n    }\n    if (message.giftExtra !== undefined) {\n      WebcastGiftMessageGiftExtra.encode(message.giftExtra, writer.uint32(186).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastGiftMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastGiftMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.event = WebcastMessageEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.giftId = reader.int32();\n          continue;\n        }\n        case 5: {\n          if (tag !== 40) {\n            break;\n          }\n\n          message.repeatCount = reader.int32();\n          continue;\n        }\n        case 7: {\n          if (tag !== 58) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n        case 9: {\n          if (tag !== 72) {\n            break;\n          }\n\n          message.repeatEnd = reader.int32();\n          continue;\n        }\n        case 11: {\n          if (tag !== 88) {\n            break;\n          }\n\n          message.groupId = reader.uint64().toString();\n          continue;\n        }\n        case 15: {\n          if (tag !== 122) {\n            break;\n          }\n\n          message.giftDetails = WebcastGiftMessageGiftDetails.decode(reader, reader.uint32());\n          continue;\n        }\n        case 22: {\n          if (tag !== 178) {\n            break;\n          }\n\n          message.monitorExtra = reader.string();\n          continue;\n        }\n        case 23: {\n          if (tag !== 186) {\n            break;\n          }\n\n          message.giftExtra = WebcastGiftMessageGiftExtra.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastGiftMessage {\n    return {\n      event: isSet(object.event) ? WebcastMessageEvent.fromJSON(object.event) : undefined,\n      giftId: isSet(object.giftId) ? globalThis.Number(object.giftId) : 0,\n      repeatCount: isSet(object.repeatCount) ? globalThis.Number(object.repeatCount) : 0,\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n      repeatEnd: isSet(object.repeatEnd) ? globalThis.Number(object.repeatEnd) : 0,\n      groupId: isSet(object.groupId) ? globalThis.String(object.groupId) : \"0\",\n      giftDetails: isSet(object.giftDetails) ? WebcastGiftMessageGiftDetails.fromJSON(object.giftDetails) : undefined,\n      monitorExtra: isSet(object.monitorExtra) ? globalThis.String(object.monitorExtra) : \"\",\n      giftExtra: isSet(object.giftExtra) ? WebcastGiftMessageGiftExtra.fromJSON(object.giftExtra) : undefined,\n    };\n  },\n\n  toJSON(message: WebcastGiftMessage): unknown {\n    const obj: any = {};\n    if (message.event !== undefined) {\n      obj.event = WebcastMessageEvent.toJSON(message.event);\n    }\n    if (message.giftId !== 0) {\n      obj.giftId = Math.round(message.giftId);\n    }\n    if (message.repeatCount !== 0) {\n      obj.repeatCount = Math.round(message.repeatCount);\n    }\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    if (message.repeatEnd !== 0) {\n      obj.repeatEnd = Math.round(message.repeatEnd);\n    }\n    if (message.groupId !== \"0\") {\n      obj.groupId = message.groupId;\n    }\n    if (message.giftDetails !== undefined) {\n      obj.giftDetails = WebcastGiftMessageGiftDetails.toJSON(message.giftDetails);\n    }\n    if (message.monitorExtra !== \"\") {\n      obj.monitorExtra = message.monitorExtra;\n    }\n    if (message.giftExtra !== undefined) {\n      obj.giftExtra = WebcastGiftMessageGiftExtra.toJSON(message.giftExtra);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastGiftMessage>, I>>(base?: I): WebcastGiftMessage {\n    return WebcastGiftMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastGiftMessage>, I>>(object: I): WebcastGiftMessage {\n    const message = createBaseWebcastGiftMessage();\n    message.event = (object.event !== undefined && object.event !== null)\n      ? WebcastMessageEvent.fromPartial(object.event)\n      : undefined;\n    message.giftId = object.giftId ?? 0;\n    message.repeatCount = object.repeatCount ?? 0;\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    message.repeatEnd = object.repeatEnd ?? 0;\n    message.groupId = object.groupId ?? \"0\";\n    message.giftDetails = (object.giftDetails !== undefined && object.giftDetails !== null)\n      ? WebcastGiftMessageGiftDetails.fromPartial(object.giftDetails)\n      : undefined;\n    message.monitorExtra = object.monitorExtra ?? \"\";\n    message.giftExtra = (object.giftExtra !== undefined && object.giftExtra !== null)\n      ? WebcastGiftMessageGiftExtra.fromPartial(object.giftExtra)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastGiftMessageGiftDetails(): WebcastGiftMessageGiftDetails {\n  return { giftImage: undefined, giftName: \"\", describe: \"\", giftType: 0, diamondCount: 0 };\n}\n\nexport const WebcastGiftMessageGiftDetails: MessageFns<WebcastGiftMessageGiftDetails> = {\n  encode(message: WebcastGiftMessageGiftDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.giftImage !== undefined) {\n      WebcastGiftMessageGiftImage.encode(message.giftImage, writer.uint32(10).fork()).join();\n    }\n    if (message.giftName !== \"\") {\n      writer.uint32(130).string(message.giftName);\n    }\n    if (message.describe !== \"\") {\n      writer.uint32(18).string(message.describe);\n    }\n    if (message.giftType !== 0) {\n      writer.uint32(88).int32(message.giftType);\n    }\n    if (message.diamondCount !== 0) {\n      writer.uint32(96).int32(message.diamondCount);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastGiftMessageGiftDetails {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastGiftMessageGiftDetails();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.giftImage = WebcastGiftMessageGiftImage.decode(reader, reader.uint32());\n          continue;\n        }\n        case 16: {\n          if (tag !== 130) {\n            break;\n          }\n\n          message.giftName = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.describe = reader.string();\n          continue;\n        }\n        case 11: {\n          if (tag !== 88) {\n            break;\n          }\n\n          message.giftType = reader.int32();\n          continue;\n        }\n        case 12: {\n          if (tag !== 96) {\n            break;\n          }\n\n          message.diamondCount = reader.int32();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastGiftMessageGiftDetails {\n    return {\n      giftImage: isSet(object.giftImage) ? WebcastGiftMessageGiftImage.fromJSON(object.giftImage) : undefined,\n      giftName: isSet(object.giftName) ? globalThis.String(object.giftName) : \"\",\n      describe: isSet(object.describe) ? globalThis.String(object.describe) : \"\",\n      giftType: isSet(object.giftType) ? globalThis.Number(object.giftType) : 0,\n      diamondCount: isSet(object.diamondCount) ? globalThis.Number(object.diamondCount) : 0,\n    };\n  },\n\n  toJSON(message: WebcastGiftMessageGiftDetails): unknown {\n    const obj: any = {};\n    if (message.giftImage !== undefined) {\n      obj.giftImage = WebcastGiftMessageGiftImage.toJSON(message.giftImage);\n    }\n    if (message.giftName !== \"\") {\n      obj.giftName = message.giftName;\n    }\n    if (message.describe !== \"\") {\n      obj.describe = message.describe;\n    }\n    if (message.giftType !== 0) {\n      obj.giftType = Math.round(message.giftType);\n    }\n    if (message.diamondCount !== 0) {\n      obj.diamondCount = Math.round(message.diamondCount);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastGiftMessageGiftDetails>, I>>(base?: I): WebcastGiftMessageGiftDetails {\n    return WebcastGiftMessageGiftDetails.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastGiftMessageGiftDetails>, I>>(\n    object: I,\n  ): WebcastGiftMessageGiftDetails {\n    const message = createBaseWebcastGiftMessageGiftDetails();\n    message.giftImage = (object.giftImage !== undefined && object.giftImage !== null)\n      ? WebcastGiftMessageGiftImage.fromPartial(object.giftImage)\n      : undefined;\n    message.giftName = object.giftName ?? \"\";\n    message.describe = object.describe ?? \"\";\n    message.giftType = object.giftType ?? 0;\n    message.diamondCount = object.diamondCount ?? 0;\n    return message;\n  },\n};\n\nfunction createBaseWebcastGiftMessageGiftExtra(): WebcastGiftMessageGiftExtra {\n  return { timestamp: \"0\", receiverUserId: \"0\" };\n}\n\nexport const WebcastGiftMessageGiftExtra: MessageFns<WebcastGiftMessageGiftExtra> = {\n  encode(message: WebcastGiftMessageGiftExtra, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.timestamp !== \"0\") {\n      writer.uint32(48).uint64(message.timestamp);\n    }\n    if (message.receiverUserId !== \"0\") {\n      writer.uint32(64).uint64(message.receiverUserId);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastGiftMessageGiftExtra {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastGiftMessageGiftExtra();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 6: {\n          if (tag !== 48) {\n            break;\n          }\n\n          message.timestamp = reader.uint64().toString();\n          continue;\n        }\n        case 8: {\n          if (tag !== 64) {\n            break;\n          }\n\n          message.receiverUserId = reader.uint64().toString();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastGiftMessageGiftExtra {\n    return {\n      timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : \"0\",\n      receiverUserId: isSet(object.receiverUserId) ? globalThis.String(object.receiverUserId) : \"0\",\n    };\n  },\n\n  toJSON(message: WebcastGiftMessageGiftExtra): unknown {\n    const obj: any = {};\n    if (message.timestamp !== \"0\") {\n      obj.timestamp = message.timestamp;\n    }\n    if (message.receiverUserId !== \"0\") {\n      obj.receiverUserId = message.receiverUserId;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastGiftMessageGiftExtra>, I>>(base?: I): WebcastGiftMessageGiftExtra {\n    return WebcastGiftMessageGiftExtra.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastGiftMessageGiftExtra>, I>>(object: I): WebcastGiftMessageGiftExtra {\n    const message = createBaseWebcastGiftMessageGiftExtra();\n    message.timestamp = object.timestamp ?? \"0\";\n    message.receiverUserId = object.receiverUserId ?? \"0\";\n    return message;\n  },\n};\n\nfunction createBaseWebcastGiftMessageGiftImage(): WebcastGiftMessageGiftImage {\n  return { giftPictureUrl: \"\" };\n}\n\nexport const WebcastGiftMessageGiftImage: MessageFns<WebcastGiftMessageGiftImage> = {\n  encode(message: WebcastGiftMessageGiftImage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.giftPictureUrl !== \"\") {\n      writer.uint32(10).string(message.giftPictureUrl);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastGiftMessageGiftImage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastGiftMessageGiftImage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.giftPictureUrl = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastGiftMessageGiftImage {\n    return { giftPictureUrl: isSet(object.giftPictureUrl) ? globalThis.String(object.giftPictureUrl) : \"\" };\n  },\n\n  toJSON(message: WebcastGiftMessageGiftImage): unknown {\n    const obj: any = {};\n    if (message.giftPictureUrl !== \"\") {\n      obj.giftPictureUrl = message.giftPictureUrl;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastGiftMessageGiftImage>, I>>(base?: I): WebcastGiftMessageGiftImage {\n    return WebcastGiftMessageGiftImage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastGiftMessageGiftImage>, I>>(object: I): WebcastGiftMessageGiftImage {\n    const message = createBaseWebcastGiftMessageGiftImage();\n    message.giftPictureUrl = object.giftPictureUrl ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseWebcastLinkMicBattle(): WebcastLinkMicBattle {\n  return { battleUsers: [] };\n}\n\nexport const WebcastLinkMicBattle: MessageFns<WebcastLinkMicBattle> = {\n  encode(message: WebcastLinkMicBattle, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    for (const v of message.battleUsers) {\n      WebcastLinkMicBattleItems.encode(v!, writer.uint32(82).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastLinkMicBattle {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastLinkMicBattle();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 10: {\n          if (tag !== 82) {\n            break;\n          }\n\n          message.battleUsers.push(WebcastLinkMicBattleItems.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastLinkMicBattle {\n    return {\n      battleUsers: globalThis.Array.isArray(object?.battleUsers)\n        ? object.battleUsers.map((e: any) => WebcastLinkMicBattleItems.fromJSON(e))\n        : [],\n    };\n  },\n\n  toJSON(message: WebcastLinkMicBattle): unknown {\n    const obj: any = {};\n    if (message.battleUsers?.length) {\n      obj.battleUsers = message.battleUsers.map((e) => WebcastLinkMicBattleItems.toJSON(e));\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastLinkMicBattle>, I>>(base?: I): WebcastLinkMicBattle {\n    return WebcastLinkMicBattle.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastLinkMicBattle>, I>>(object: I): WebcastLinkMicBattle {\n    const message = createBaseWebcastLinkMicBattle();\n    message.battleUsers = object.battleUsers?.map((e) => WebcastLinkMicBattleItems.fromPartial(e)) || [];\n    return message;\n  },\n};\n\nfunction createBaseWebcastLinkMicBattleItems(): WebcastLinkMicBattleItems {\n  return { battleGroup: undefined };\n}\n\nexport const WebcastLinkMicBattleItems: MessageFns<WebcastLinkMicBattleItems> = {\n  encode(message: WebcastLinkMicBattleItems, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.battleGroup !== undefined) {\n      WebcastLinkMicBattleGroup.encode(message.battleGroup, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastLinkMicBattleItems {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastLinkMicBattleItems();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.battleGroup = WebcastLinkMicBattleGroup.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastLinkMicBattleItems {\n    return {\n      battleGroup: isSet(object.battleGroup) ? WebcastLinkMicBattleGroup.fromJSON(object.battleGroup) : undefined,\n    };\n  },\n\n  toJSON(message: WebcastLinkMicBattleItems): unknown {\n    const obj: any = {};\n    if (message.battleGroup !== undefined) {\n      obj.battleGroup = WebcastLinkMicBattleGroup.toJSON(message.battleGroup);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastLinkMicBattleItems>, I>>(base?: I): WebcastLinkMicBattleItems {\n    return WebcastLinkMicBattleItems.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastLinkMicBattleItems>, I>>(object: I): WebcastLinkMicBattleItems {\n    const message = createBaseWebcastLinkMicBattleItems();\n    message.battleGroup = (object.battleGroup !== undefined && object.battleGroup !== null)\n      ? WebcastLinkMicBattleGroup.fromPartial(object.battleGroup)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastLinkMicBattleGroup(): WebcastLinkMicBattleGroup {\n  return { user: undefined };\n}\n\nexport const WebcastLinkMicBattleGroup: MessageFns<WebcastLinkMicBattleGroup> = {\n  encode(message: WebcastLinkMicBattleGroup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.user !== undefined) {\n      LinkUser.encode(message.user, writer.uint32(10).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastLinkMicBattleGroup {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastLinkMicBattleGroup();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.user = LinkUser.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastLinkMicBattleGroup {\n    return { user: isSet(object.user) ? LinkUser.fromJSON(object.user) : undefined };\n  },\n\n  toJSON(message: WebcastLinkMicBattleGroup): unknown {\n    const obj: any = {};\n    if (message.user !== undefined) {\n      obj.user = LinkUser.toJSON(message.user);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastLinkMicBattleGroup>, I>>(base?: I): WebcastLinkMicBattleGroup {\n    return WebcastLinkMicBattleGroup.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastLinkMicBattleGroup>, I>>(object: I): WebcastLinkMicBattleGroup {\n    const message = createBaseWebcastLinkMicBattleGroup();\n    message.user = (object.user !== undefined && object.user !== null) ? LinkUser.fromPartial(object.user) : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastLinkMicArmies(): WebcastLinkMicArmies {\n  return { battleItems: [], battleStatus: 0 };\n}\n\nexport const WebcastLinkMicArmies: MessageFns<WebcastLinkMicArmies> = {\n  encode(message: WebcastLinkMicArmies, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    for (const v of message.battleItems) {\n      WebcastLinkMicArmiesItems.encode(v!, writer.uint32(26).fork()).join();\n    }\n    if (message.battleStatus !== 0) {\n      writer.uint32(56).int32(message.battleStatus);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastLinkMicArmies {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastLinkMicArmies();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.battleItems.push(WebcastLinkMicArmiesItems.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 7: {\n          if (tag !== 56) {\n            break;\n          }\n\n          message.battleStatus = reader.int32();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastLinkMicArmies {\n    return {\n      battleItems: globalThis.Array.isArray(object?.battleItems)\n        ? object.battleItems.map((e: any) => WebcastLinkMicArmiesItems.fromJSON(e))\n        : [],\n      battleStatus: isSet(object.battleStatus) ? globalThis.Number(object.battleStatus) : 0,\n    };\n  },\n\n  toJSON(message: WebcastLinkMicArmies): unknown {\n    const obj: any = {};\n    if (message.battleItems?.length) {\n      obj.battleItems = message.battleItems.map((e) => WebcastLinkMicArmiesItems.toJSON(e));\n    }\n    if (message.battleStatus !== 0) {\n      obj.battleStatus = Math.round(message.battleStatus);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastLinkMicArmies>, I>>(base?: I): WebcastLinkMicArmies {\n    return WebcastLinkMicArmies.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastLinkMicArmies>, I>>(object: I): WebcastLinkMicArmies {\n    const message = createBaseWebcastLinkMicArmies();\n    message.battleItems = object.battleItems?.map((e) => WebcastLinkMicArmiesItems.fromPartial(e)) || [];\n    message.battleStatus = object.battleStatus ?? 0;\n    return message;\n  },\n};\n\nfunction createBaseWebcastLinkMicArmiesItems(): WebcastLinkMicArmiesItems {\n  return { hostUserId: \"0\", battleGroups: [] };\n}\n\nexport const WebcastLinkMicArmiesItems: MessageFns<WebcastLinkMicArmiesItems> = {\n  encode(message: WebcastLinkMicArmiesItems, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.hostUserId !== \"0\") {\n      writer.uint32(8).uint64(message.hostUserId);\n    }\n    for (const v of message.battleGroups) {\n      WebcastLinkMicArmiesGroup.encode(v!, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastLinkMicArmiesItems {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastLinkMicArmiesItems();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.hostUserId = reader.uint64().toString();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.battleGroups.push(WebcastLinkMicArmiesGroup.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastLinkMicArmiesItems {\n    return {\n      hostUserId: isSet(object.hostUserId) ? globalThis.String(object.hostUserId) : \"0\",\n      battleGroups: globalThis.Array.isArray(object?.battleGroups)\n        ? object.battleGroups.map((e: any) => WebcastLinkMicArmiesGroup.fromJSON(e))\n        : [],\n    };\n  },\n\n  toJSON(message: WebcastLinkMicArmiesItems): unknown {\n    const obj: any = {};\n    if (message.hostUserId !== \"0\") {\n      obj.hostUserId = message.hostUserId;\n    }\n    if (message.battleGroups?.length) {\n      obj.battleGroups = message.battleGroups.map((e) => WebcastLinkMicArmiesGroup.toJSON(e));\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastLinkMicArmiesItems>, I>>(base?: I): WebcastLinkMicArmiesItems {\n    return WebcastLinkMicArmiesItems.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastLinkMicArmiesItems>, I>>(object: I): WebcastLinkMicArmiesItems {\n    const message = createBaseWebcastLinkMicArmiesItems();\n    message.hostUserId = object.hostUserId ?? \"0\";\n    message.battleGroups = object.battleGroups?.map((e) => WebcastLinkMicArmiesGroup.fromPartial(e)) || [];\n    return message;\n  },\n};\n\nfunction createBaseWebcastLinkMicArmiesGroup(): WebcastLinkMicArmiesGroup {\n  return { users: [], points: 0 };\n}\n\nexport const WebcastLinkMicArmiesGroup: MessageFns<WebcastLinkMicArmiesGroup> = {\n  encode(message: WebcastLinkMicArmiesGroup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    for (const v of message.users) {\n      User.encode(v!, writer.uint32(10).fork()).join();\n    }\n    if (message.points !== 0) {\n      writer.uint32(16).int32(message.points);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastLinkMicArmiesGroup {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastLinkMicArmiesGroup();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.users.push(User.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.points = reader.int32();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastLinkMicArmiesGroup {\n    return {\n      users: globalThis.Array.isArray(object?.users) ? object.users.map((e: any) => User.fromJSON(e)) : [],\n      points: isSet(object.points) ? globalThis.Number(object.points) : 0,\n    };\n  },\n\n  toJSON(message: WebcastLinkMicArmiesGroup): unknown {\n    const obj: any = {};\n    if (message.users?.length) {\n      obj.users = message.users.map((e) => User.toJSON(e));\n    }\n    if (message.points !== 0) {\n      obj.points = Math.round(message.points);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastLinkMicArmiesGroup>, I>>(base?: I): WebcastLinkMicArmiesGroup {\n    return WebcastLinkMicArmiesGroup.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastLinkMicArmiesGroup>, I>>(object: I): WebcastLinkMicArmiesGroup {\n    const message = createBaseWebcastLinkMicArmiesGroup();\n    message.users = object.users?.map((e) => User.fromPartial(e)) || [];\n    message.points = object.points ?? 0;\n    return message;\n  },\n};\n\nfunction createBaseWebcastSocialMessage(): WebcastSocialMessage {\n  return { event: undefined, user: undefined };\n}\n\nexport const WebcastSocialMessage: MessageFns<WebcastSocialMessage> = {\n  encode(message: WebcastSocialMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.event !== undefined) {\n      WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();\n    }\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastSocialMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastSocialMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.event = WebcastMessageEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastSocialMessage {\n    return {\n      event: isSet(object.event) ? WebcastMessageEvent.fromJSON(object.event) : undefined,\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n    };\n  },\n\n  toJSON(message: WebcastSocialMessage): unknown {\n    const obj: any = {};\n    if (message.event !== undefined) {\n      obj.event = WebcastMessageEvent.toJSON(message.event);\n    }\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastSocialMessage>, I>>(base?: I): WebcastSocialMessage {\n    return WebcastSocialMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastSocialMessage>, I>>(object: I): WebcastSocialMessage {\n    const message = createBaseWebcastSocialMessage();\n    message.event = (object.event !== undefined && object.event !== null)\n      ? WebcastMessageEvent.fromPartial(object.event)\n      : undefined;\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastLikeMessage(): WebcastLikeMessage {\n  return { event: undefined, user: undefined, likeCount: 0, totalLikeCount: 0 };\n}\n\nexport const WebcastLikeMessage: MessageFns<WebcastLikeMessage> = {\n  encode(message: WebcastLikeMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.event !== undefined) {\n      WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();\n    }\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(42).fork()).join();\n    }\n    if (message.likeCount !== 0) {\n      writer.uint32(16).int32(message.likeCount);\n    }\n    if (message.totalLikeCount !== 0) {\n      writer.uint32(24).int32(message.totalLikeCount);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastLikeMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastLikeMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.event = WebcastMessageEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.likeCount = reader.int32();\n          continue;\n        }\n        case 3: {\n          if (tag !== 24) {\n            break;\n          }\n\n          message.totalLikeCount = reader.int32();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastLikeMessage {\n    return {\n      event: isSet(object.event) ? WebcastMessageEvent.fromJSON(object.event) : undefined,\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n      likeCount: isSet(object.likeCount) ? globalThis.Number(object.likeCount) : 0,\n      totalLikeCount: isSet(object.totalLikeCount) ? globalThis.Number(object.totalLikeCount) : 0,\n    };\n  },\n\n  toJSON(message: WebcastLikeMessage): unknown {\n    const obj: any = {};\n    if (message.event !== undefined) {\n      obj.event = WebcastMessageEvent.toJSON(message.event);\n    }\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    if (message.likeCount !== 0) {\n      obj.likeCount = Math.round(message.likeCount);\n    }\n    if (message.totalLikeCount !== 0) {\n      obj.totalLikeCount = Math.round(message.totalLikeCount);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastLikeMessage>, I>>(base?: I): WebcastLikeMessage {\n    return WebcastLikeMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastLikeMessage>, I>>(object: I): WebcastLikeMessage {\n    const message = createBaseWebcastLikeMessage();\n    message.event = (object.event !== undefined && object.event !== null)\n      ? WebcastMessageEvent.fromPartial(object.event)\n      : undefined;\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    message.likeCount = object.likeCount ?? 0;\n    message.totalLikeCount = object.totalLikeCount ?? 0;\n    return message;\n  },\n};\n\nfunction createBaseWebcastQuestionNewMessage(): WebcastQuestionNewMessage {\n  return { questionDetails: undefined };\n}\n\nexport const WebcastQuestionNewMessage: MessageFns<WebcastQuestionNewMessage> = {\n  encode(message: WebcastQuestionNewMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.questionDetails !== undefined) {\n      QuestionDetails.encode(message.questionDetails, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastQuestionNewMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastQuestionNewMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.questionDetails = QuestionDetails.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastQuestionNewMessage {\n    return {\n      questionDetails: isSet(object.questionDetails) ? QuestionDetails.fromJSON(object.questionDetails) : undefined,\n    };\n  },\n\n  toJSON(message: WebcastQuestionNewMessage): unknown {\n    const obj: any = {};\n    if (message.questionDetails !== undefined) {\n      obj.questionDetails = QuestionDetails.toJSON(message.questionDetails);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastQuestionNewMessage>, I>>(base?: I): WebcastQuestionNewMessage {\n    return WebcastQuestionNewMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastQuestionNewMessage>, I>>(object: I): WebcastQuestionNewMessage {\n    const message = createBaseWebcastQuestionNewMessage();\n    message.questionDetails = (object.questionDetails !== undefined && object.questionDetails !== null)\n      ? QuestionDetails.fromPartial(object.questionDetails)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseQuestionDetails(): QuestionDetails {\n  return { questionText: \"\", user: undefined };\n}\n\nexport const QuestionDetails: MessageFns<QuestionDetails> = {\n  encode(message: QuestionDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.questionText !== \"\") {\n      writer.uint32(18).string(message.questionText);\n    }\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(42).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): QuestionDetails {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseQuestionDetails();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.questionText = reader.string();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): QuestionDetails {\n    return {\n      questionText: isSet(object.questionText) ? globalThis.String(object.questionText) : \"\",\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n    };\n  },\n\n  toJSON(message: QuestionDetails): unknown {\n    const obj: any = {};\n    if (message.questionText !== \"\") {\n      obj.questionText = message.questionText;\n    }\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<QuestionDetails>, I>>(base?: I): QuestionDetails {\n    return QuestionDetails.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<QuestionDetails>, I>>(object: I): QuestionDetails {\n    const message = createBaseQuestionDetails();\n    message.questionText = object.questionText ?? \"\";\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastMessageEvent(): WebcastMessageEvent {\n  return { msgId: \"0\", createTime: \"0\", eventDetails: undefined };\n}\n\nexport const WebcastMessageEvent: MessageFns<WebcastMessageEvent> = {\n  encode(message: WebcastMessageEvent, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.msgId !== \"0\") {\n      writer.uint32(16).uint64(message.msgId);\n    }\n    if (message.createTime !== \"0\") {\n      writer.uint32(32).uint64(message.createTime);\n    }\n    if (message.eventDetails !== undefined) {\n      WebcastMessageEventDetails.encode(message.eventDetails, writer.uint32(66).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastMessageEvent {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastMessageEvent();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.msgId = reader.uint64().toString();\n          continue;\n        }\n        case 4: {\n          if (tag !== 32) {\n            break;\n          }\n\n          message.createTime = reader.uint64().toString();\n          continue;\n        }\n        case 8: {\n          if (tag !== 66) {\n            break;\n          }\n\n          message.eventDetails = WebcastMessageEventDetails.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastMessageEvent {\n    return {\n      msgId: isSet(object.msgId) ? globalThis.String(object.msgId) : \"0\",\n      createTime: isSet(object.createTime) ? globalThis.String(object.createTime) : \"0\",\n      eventDetails: isSet(object.eventDetails) ? WebcastMessageEventDetails.fromJSON(object.eventDetails) : undefined,\n    };\n  },\n\n  toJSON(message: WebcastMessageEvent): unknown {\n    const obj: any = {};\n    if (message.msgId !== \"0\") {\n      obj.msgId = message.msgId;\n    }\n    if (message.createTime !== \"0\") {\n      obj.createTime = message.createTime;\n    }\n    if (message.eventDetails !== undefined) {\n      obj.eventDetails = WebcastMessageEventDetails.toJSON(message.eventDetails);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastMessageEvent>, I>>(base?: I): WebcastMessageEvent {\n    return WebcastMessageEvent.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastMessageEvent>, I>>(object: I): WebcastMessageEvent {\n    const message = createBaseWebcastMessageEvent();\n    message.msgId = object.msgId ?? \"0\";\n    message.createTime = object.createTime ?? \"0\";\n    message.eventDetails = (object.eventDetails !== undefined && object.eventDetails !== null)\n      ? WebcastMessageEventDetails.fromPartial(object.eventDetails)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastMessageEventDetails(): WebcastMessageEventDetails {\n  return { displayType: \"\", label: \"\" };\n}\n\nexport const WebcastMessageEventDetails: MessageFns<WebcastMessageEventDetails> = {\n  encode(message: WebcastMessageEventDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.displayType !== \"\") {\n      writer.uint32(10).string(message.displayType);\n    }\n    if (message.label !== \"\") {\n      writer.uint32(18).string(message.label);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastMessageEventDetails {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastMessageEventDetails();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.displayType = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.label = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastMessageEventDetails {\n    return {\n      displayType: isSet(object.displayType) ? globalThis.String(object.displayType) : \"\",\n      label: isSet(object.label) ? globalThis.String(object.label) : \"\",\n    };\n  },\n\n  toJSON(message: WebcastMessageEventDetails): unknown {\n    const obj: any = {};\n    if (message.displayType !== \"\") {\n      obj.displayType = message.displayType;\n    }\n    if (message.label !== \"\") {\n      obj.label = message.label;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastMessageEventDetails>, I>>(base?: I): WebcastMessageEventDetails {\n    return WebcastMessageEventDetails.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastMessageEventDetails>, I>>(object: I): WebcastMessageEventDetails {\n    const message = createBaseWebcastMessageEventDetails();\n    message.displayType = object.displayType ?? \"\";\n    message.label = object.label ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseWebcastLiveIntroMessage(): WebcastLiveIntroMessage {\n  return { id: \"0\", description: \"\", user: undefined };\n}\n\nexport const WebcastLiveIntroMessage: MessageFns<WebcastLiveIntroMessage> = {\n  encode(message: WebcastLiveIntroMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.id !== \"0\") {\n      writer.uint32(16).uint64(message.id);\n    }\n    if (message.description !== \"\") {\n      writer.uint32(34).string(message.description);\n    }\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(42).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastLiveIntroMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastLiveIntroMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.id = reader.uint64().toString();\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.description = reader.string();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastLiveIntroMessage {\n    return {\n      id: isSet(object.id) ? globalThis.String(object.id) : \"0\",\n      description: isSet(object.description) ? globalThis.String(object.description) : \"\",\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n    };\n  },\n\n  toJSON(message: WebcastLiveIntroMessage): unknown {\n    const obj: any = {};\n    if (message.id !== \"0\") {\n      obj.id = message.id;\n    }\n    if (message.description !== \"\") {\n      obj.description = message.description;\n    }\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastLiveIntroMessage>, I>>(base?: I): WebcastLiveIntroMessage {\n    return WebcastLiveIntroMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastLiveIntroMessage>, I>>(object: I): WebcastLiveIntroMessage {\n    const message = createBaseWebcastLiveIntroMessage();\n    message.id = object.id ?? \"0\";\n    message.description = object.description ?? \"\";\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    return message;\n  },\n};\n\nfunction createBaseSystemMessage(): SystemMessage {\n  return { description: \"\" };\n}\n\nexport const SystemMessage: MessageFns<SystemMessage> = {\n  encode(message: SystemMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.description !== \"\") {\n      writer.uint32(18).string(message.description);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): SystemMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseSystemMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.description = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): SystemMessage {\n    return { description: isSet(object.description) ? globalThis.String(object.description) : \"\" };\n  },\n\n  toJSON(message: SystemMessage): unknown {\n    const obj: any = {};\n    if (message.description !== \"\") {\n      obj.description = message.description;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<SystemMessage>, I>>(base?: I): SystemMessage {\n    return SystemMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<SystemMessage>, I>>(object: I): SystemMessage {\n    const message = createBaseSystemMessage();\n    message.description = object.description ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseWebcastInRoomBannerMessage(): WebcastInRoomBannerMessage {\n  return { data: \"\" };\n}\n\nexport const WebcastInRoomBannerMessage: MessageFns<WebcastInRoomBannerMessage> = {\n  encode(message: WebcastInRoomBannerMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.data !== \"\") {\n      writer.uint32(18).string(message.data);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastInRoomBannerMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastInRoomBannerMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.data = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastInRoomBannerMessage {\n    return { data: isSet(object.data) ? globalThis.String(object.data) : \"\" };\n  },\n\n  toJSON(message: WebcastInRoomBannerMessage): unknown {\n    const obj: any = {};\n    if (message.data !== \"\") {\n      obj.data = message.data;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastInRoomBannerMessage>, I>>(base?: I): WebcastInRoomBannerMessage {\n    return WebcastInRoomBannerMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastInRoomBannerMessage>, I>>(object: I): WebcastInRoomBannerMessage {\n    const message = createBaseWebcastInRoomBannerMessage();\n    message.data = object.data ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseRankItem(): RankItem {\n  return { colour: \"\", id: \"0\" };\n}\n\nexport const RankItem: MessageFns<RankItem> = {\n  encode(message: RankItem, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.colour !== \"\") {\n      writer.uint32(10).string(message.colour);\n    }\n    if (message.id !== \"0\") {\n      writer.uint32(32).uint64(message.id);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): RankItem {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseRankItem();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.colour = reader.string();\n          continue;\n        }\n        case 4: {\n          if (tag !== 32) {\n            break;\n          }\n\n          message.id = reader.uint64().toString();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): RankItem {\n    return {\n      colour: isSet(object.colour) ? globalThis.String(object.colour) : \"\",\n      id: isSet(object.id) ? globalThis.String(object.id) : \"0\",\n    };\n  },\n\n  toJSON(message: RankItem): unknown {\n    const obj: any = {};\n    if (message.colour !== \"\") {\n      obj.colour = message.colour;\n    }\n    if (message.id !== \"0\") {\n      obj.id = message.id;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<RankItem>, I>>(base?: I): RankItem {\n    return RankItem.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<RankItem>, I>>(object: I): RankItem {\n    const message = createBaseRankItem();\n    message.colour = object.colour ?? \"\";\n    message.id = object.id ?? \"0\";\n    return message;\n  },\n};\n\nfunction createBaseWeeklyRanking(): WeeklyRanking {\n  return { type: \"\", label: \"\", rank: undefined };\n}\n\nexport const WeeklyRanking: MessageFns<WeeklyRanking> = {\n  encode(message: WeeklyRanking, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.type !== \"\") {\n      writer.uint32(10).string(message.type);\n    }\n    if (message.label !== \"\") {\n      writer.uint32(18).string(message.label);\n    }\n    if (message.rank !== undefined) {\n      RankItem.encode(message.rank, writer.uint32(26).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WeeklyRanking {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWeeklyRanking();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.type = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.label = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.rank = RankItem.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WeeklyRanking {\n    return {\n      type: isSet(object.type) ? globalThis.String(object.type) : \"\",\n      label: isSet(object.label) ? globalThis.String(object.label) : \"\",\n      rank: isSet(object.rank) ? RankItem.fromJSON(object.rank) : undefined,\n    };\n  },\n\n  toJSON(message: WeeklyRanking): unknown {\n    const obj: any = {};\n    if (message.type !== \"\") {\n      obj.type = message.type;\n    }\n    if (message.label !== \"\") {\n      obj.label = message.label;\n    }\n    if (message.rank !== undefined) {\n      obj.rank = RankItem.toJSON(message.rank);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WeeklyRanking>, I>>(base?: I): WeeklyRanking {\n    return WeeklyRanking.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WeeklyRanking>, I>>(object: I): WeeklyRanking {\n    const message = createBaseWeeklyRanking();\n    message.type = object.type ?? \"\";\n    message.label = object.label ?? \"\";\n    message.rank = (object.rank !== undefined && object.rank !== null) ? RankItem.fromPartial(object.rank) : undefined;\n    return message;\n  },\n};\n\nfunction createBaseRankContainer(): RankContainer {\n  return { rankings: undefined };\n}\n\nexport const RankContainer: MessageFns<RankContainer> = {\n  encode(message: RankContainer, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.rankings !== undefined) {\n      WeeklyRanking.encode(message.rankings, writer.uint32(34).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): RankContainer {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseRankContainer();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.rankings = WeeklyRanking.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): RankContainer {\n    return { rankings: isSet(object.rankings) ? WeeklyRanking.fromJSON(object.rankings) : undefined };\n  },\n\n  toJSON(message: RankContainer): unknown {\n    const obj: any = {};\n    if (message.rankings !== undefined) {\n      obj.rankings = WeeklyRanking.toJSON(message.rankings);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<RankContainer>, I>>(base?: I): RankContainer {\n    return RankContainer.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<RankContainer>, I>>(object: I): RankContainer {\n    const message = createBaseRankContainer();\n    message.rankings = (object.rankings !== undefined && object.rankings !== null)\n      ? WeeklyRanking.fromPartial(object.rankings)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseWebcastHourlyRankMessage(): WebcastHourlyRankMessage {\n  return { data: undefined };\n}\n\nexport const WebcastHourlyRankMessage: MessageFns<WebcastHourlyRankMessage> = {\n  encode(message: WebcastHourlyRankMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.data !== undefined) {\n      RankContainer.encode(message.data, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastHourlyRankMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastHourlyRankMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.data = RankContainer.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastHourlyRankMessage {\n    return { data: isSet(object.data) ? RankContainer.fromJSON(object.data) : undefined };\n  },\n\n  toJSON(message: WebcastHourlyRankMessage): unknown {\n    const obj: any = {};\n    if (message.data !== undefined) {\n      obj.data = RankContainer.toJSON(message.data);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastHourlyRankMessage>, I>>(base?: I): WebcastHourlyRankMessage {\n    return WebcastHourlyRankMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastHourlyRankMessage>, I>>(object: I): WebcastHourlyRankMessage {\n    const message = createBaseWebcastHourlyRankMessage();\n    message.data = (object.data !== undefined && object.data !== null)\n      ? RankContainer.fromPartial(object.data)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseEmoteDetails(): EmoteDetails {\n  return { emoteId: \"\", image: undefined };\n}\n\nexport const EmoteDetails: MessageFns<EmoteDetails> = {\n  encode(message: EmoteDetails, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.emoteId !== \"\") {\n      writer.uint32(10).string(message.emoteId);\n    }\n    if (message.image !== undefined) {\n      EmoteImage.encode(message.image, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): EmoteDetails {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseEmoteDetails();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.emoteId = reader.string();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.image = EmoteImage.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): EmoteDetails {\n    return {\n      emoteId: isSet(object.emoteId) ? globalThis.String(object.emoteId) : \"\",\n      image: isSet(object.image) ? EmoteImage.fromJSON(object.image) : undefined,\n    };\n  },\n\n  toJSON(message: EmoteDetails): unknown {\n    const obj: any = {};\n    if (message.emoteId !== \"\") {\n      obj.emoteId = message.emoteId;\n    }\n    if (message.image !== undefined) {\n      obj.image = EmoteImage.toJSON(message.image);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<EmoteDetails>, I>>(base?: I): EmoteDetails {\n    return EmoteDetails.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<EmoteDetails>, I>>(object: I): EmoteDetails {\n    const message = createBaseEmoteDetails();\n    message.emoteId = object.emoteId ?? \"\";\n    message.image = (object.image !== undefined && object.image !== null)\n      ? EmoteImage.fromPartial(object.image)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseEmoteImage(): EmoteImage {\n  return { imageUrl: \"\" };\n}\n\nexport const EmoteImage: MessageFns<EmoteImage> = {\n  encode(message: EmoteImage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.imageUrl !== \"\") {\n      writer.uint32(10).string(message.imageUrl);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): EmoteImage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseEmoteImage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.imageUrl = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): EmoteImage {\n    return { imageUrl: isSet(object.imageUrl) ? globalThis.String(object.imageUrl) : \"\" };\n  },\n\n  toJSON(message: EmoteImage): unknown {\n    const obj: any = {};\n    if (message.imageUrl !== \"\") {\n      obj.imageUrl = message.imageUrl;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<EmoteImage>, I>>(base?: I): EmoteImage {\n    return EmoteImage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<EmoteImage>, I>>(object: I): EmoteImage {\n    const message = createBaseEmoteImage();\n    message.imageUrl = object.imageUrl ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseWebcastEnvelopeMessage(): WebcastEnvelopeMessage {\n  return { treasureBoxData: undefined, treasureBoxUser: undefined };\n}\n\nexport const WebcastEnvelopeMessage: MessageFns<WebcastEnvelopeMessage> = {\n  encode(message: WebcastEnvelopeMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.treasureBoxData !== undefined) {\n      TreasureBoxData.encode(message.treasureBoxData, writer.uint32(18).fork()).join();\n    }\n    if (message.treasureBoxUser !== undefined) {\n      TreasureBoxUser.encode(message.treasureBoxUser, writer.uint32(10).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastEnvelopeMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastEnvelopeMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.treasureBoxData = TreasureBoxData.decode(reader, reader.uint32());\n          continue;\n        }\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.treasureBoxUser = TreasureBoxUser.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastEnvelopeMessage {\n    return {\n      treasureBoxData: isSet(object.treasureBoxData) ? TreasureBoxData.fromJSON(object.treasureBoxData) : undefined,\n      treasureBoxUser: isSet(object.treasureBoxUser) ? TreasureBoxUser.fromJSON(object.treasureBoxUser) : undefined,\n    };\n  },\n\n  toJSON(message: WebcastEnvelopeMessage): unknown {\n    const obj: any = {};\n    if (message.treasureBoxData !== undefined) {\n      obj.treasureBoxData = TreasureBoxData.toJSON(message.treasureBoxData);\n    }\n    if (message.treasureBoxUser !== undefined) {\n      obj.treasureBoxUser = TreasureBoxUser.toJSON(message.treasureBoxUser);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastEnvelopeMessage>, I>>(base?: I): WebcastEnvelopeMessage {\n    return WebcastEnvelopeMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastEnvelopeMessage>, I>>(object: I): WebcastEnvelopeMessage {\n    const message = createBaseWebcastEnvelopeMessage();\n    message.treasureBoxData = (object.treasureBoxData !== undefined && object.treasureBoxData !== null)\n      ? TreasureBoxData.fromPartial(object.treasureBoxData)\n      : undefined;\n    message.treasureBoxUser = (object.treasureBoxUser !== undefined && object.treasureBoxUser !== null)\n      ? TreasureBoxUser.fromPartial(object.treasureBoxUser)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseTreasureBoxUser(): TreasureBoxUser {\n  return { user2: undefined };\n}\n\nexport const TreasureBoxUser: MessageFns<TreasureBoxUser> = {\n  encode(message: TreasureBoxUser, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.user2 !== undefined) {\n      TreasureBoxUser2.encode(message.user2, writer.uint32(66).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TreasureBoxUser {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTreasureBoxUser();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 8: {\n          if (tag !== 66) {\n            break;\n          }\n\n          message.user2 = TreasureBoxUser2.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): TreasureBoxUser {\n    return { user2: isSet(object.user2) ? TreasureBoxUser2.fromJSON(object.user2) : undefined };\n  },\n\n  toJSON(message: TreasureBoxUser): unknown {\n    const obj: any = {};\n    if (message.user2 !== undefined) {\n      obj.user2 = TreasureBoxUser2.toJSON(message.user2);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<TreasureBoxUser>, I>>(base?: I): TreasureBoxUser {\n    return TreasureBoxUser.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TreasureBoxUser>, I>>(object: I): TreasureBoxUser {\n    const message = createBaseTreasureBoxUser();\n    message.user2 = (object.user2 !== undefined && object.user2 !== null)\n      ? TreasureBoxUser2.fromPartial(object.user2)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseTreasureBoxUser2(): TreasureBoxUser2 {\n  return { user3: [] };\n}\n\nexport const TreasureBoxUser2: MessageFns<TreasureBoxUser2> = {\n  encode(message: TreasureBoxUser2, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    for (const v of message.user3) {\n      TreasureBoxUser3.encode(v!, writer.uint32(34).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TreasureBoxUser2 {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTreasureBoxUser2();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.user3.push(TreasureBoxUser3.decode(reader, reader.uint32()));\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): TreasureBoxUser2 {\n    return {\n      user3: globalThis.Array.isArray(object?.user3) ? object.user3.map((e: any) => TreasureBoxUser3.fromJSON(e)) : [],\n    };\n  },\n\n  toJSON(message: TreasureBoxUser2): unknown {\n    const obj: any = {};\n    if (message.user3?.length) {\n      obj.user3 = message.user3.map((e) => TreasureBoxUser3.toJSON(e));\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<TreasureBoxUser2>, I>>(base?: I): TreasureBoxUser2 {\n    return TreasureBoxUser2.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TreasureBoxUser2>, I>>(object: I): TreasureBoxUser2 {\n    const message = createBaseTreasureBoxUser2();\n    message.user3 = object.user3?.map((e) => TreasureBoxUser3.fromPartial(e)) || [];\n    return message;\n  },\n};\n\nfunction createBaseTreasureBoxUser3(): TreasureBoxUser3 {\n  return { user4: undefined };\n}\n\nexport const TreasureBoxUser3: MessageFns<TreasureBoxUser3> = {\n  encode(message: TreasureBoxUser3, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.user4 !== undefined) {\n      TreasureBoxUser4.encode(message.user4, writer.uint32(170).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TreasureBoxUser3 {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTreasureBoxUser3();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 21: {\n          if (tag !== 170) {\n            break;\n          }\n\n          message.user4 = TreasureBoxUser4.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): TreasureBoxUser3 {\n    return { user4: isSet(object.user4) ? TreasureBoxUser4.fromJSON(object.user4) : undefined };\n  },\n\n  toJSON(message: TreasureBoxUser3): unknown {\n    const obj: any = {};\n    if (message.user4 !== undefined) {\n      obj.user4 = TreasureBoxUser4.toJSON(message.user4);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<TreasureBoxUser3>, I>>(base?: I): TreasureBoxUser3 {\n    return TreasureBoxUser3.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TreasureBoxUser3>, I>>(object: I): TreasureBoxUser3 {\n    const message = createBaseTreasureBoxUser3();\n    message.user4 = (object.user4 !== undefined && object.user4 !== null)\n      ? TreasureBoxUser4.fromPartial(object.user4)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseTreasureBoxUser4(): TreasureBoxUser4 {\n  return { user: undefined };\n}\n\nexport const TreasureBoxUser4: MessageFns<TreasureBoxUser4> = {\n  encode(message: TreasureBoxUser4, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(10).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TreasureBoxUser4 {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTreasureBoxUser4();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): TreasureBoxUser4 {\n    return { user: isSet(object.user) ? User.fromJSON(object.user) : undefined };\n  },\n\n  toJSON(message: TreasureBoxUser4): unknown {\n    const obj: any = {};\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<TreasureBoxUser4>, I>>(base?: I): TreasureBoxUser4 {\n    return TreasureBoxUser4.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TreasureBoxUser4>, I>>(object: I): TreasureBoxUser4 {\n    const message = createBaseTreasureBoxUser4();\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    return message;\n  },\n};\n\nfunction createBaseTreasureBoxData(): TreasureBoxData {\n  return { coins: 0, canOpen: 0, timestamp: \"0\" };\n}\n\nexport const TreasureBoxData: MessageFns<TreasureBoxData> = {\n  encode(message: TreasureBoxData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.coins !== 0) {\n      writer.uint32(40).uint32(message.coins);\n    }\n    if (message.canOpen !== 0) {\n      writer.uint32(48).uint32(message.canOpen);\n    }\n    if (message.timestamp !== \"0\") {\n      writer.uint32(56).uint64(message.timestamp);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): TreasureBoxData {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseTreasureBoxData();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 5: {\n          if (tag !== 40) {\n            break;\n          }\n\n          message.coins = reader.uint32();\n          continue;\n        }\n        case 6: {\n          if (tag !== 48) {\n            break;\n          }\n\n          message.canOpen = reader.uint32();\n          continue;\n        }\n        case 7: {\n          if (tag !== 56) {\n            break;\n          }\n\n          message.timestamp = reader.uint64().toString();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): TreasureBoxData {\n    return {\n      coins: isSet(object.coins) ? globalThis.Number(object.coins) : 0,\n      canOpen: isSet(object.canOpen) ? globalThis.Number(object.canOpen) : 0,\n      timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : \"0\",\n    };\n  },\n\n  toJSON(message: TreasureBoxData): unknown {\n    const obj: any = {};\n    if (message.coins !== 0) {\n      obj.coins = Math.round(message.coins);\n    }\n    if (message.canOpen !== 0) {\n      obj.canOpen = Math.round(message.canOpen);\n    }\n    if (message.timestamp !== \"0\") {\n      obj.timestamp = message.timestamp;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<TreasureBoxData>, I>>(base?: I): TreasureBoxData {\n    return TreasureBoxData.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<TreasureBoxData>, I>>(object: I): TreasureBoxData {\n    const message = createBaseTreasureBoxData();\n    message.coins = object.coins ?? 0;\n    message.canOpen = object.canOpen ?? 0;\n    message.timestamp = object.timestamp ?? \"0\";\n    return message;\n  },\n};\n\nfunction createBaseWebcastSubNotifyMessage(): WebcastSubNotifyMessage {\n  return {\n    event: undefined,\n    user: undefined,\n    exhibitionType: 0,\n    subMonth: 0,\n    subscribeType: 0,\n    oldSubscribeStatus: 0,\n    subscribingStatus: 0,\n  };\n}\n\nexport const WebcastSubNotifyMessage: MessageFns<WebcastSubNotifyMessage> = {\n  encode(message: WebcastSubNotifyMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.event !== undefined) {\n      WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();\n    }\n    if (message.user !== undefined) {\n      User.encode(message.user, writer.uint32(18).fork()).join();\n    }\n    if (message.exhibitionType !== 0) {\n      writer.uint32(24).int32(message.exhibitionType);\n    }\n    if (message.subMonth !== 0) {\n      writer.uint32(32).int32(message.subMonth);\n    }\n    if (message.subscribeType !== 0) {\n      writer.uint32(40).int32(message.subscribeType);\n    }\n    if (message.oldSubscribeStatus !== 0) {\n      writer.uint32(48).int32(message.oldSubscribeStatus);\n    }\n    if (message.subscribingStatus !== 0) {\n      writer.uint32(64).int32(message.subscribingStatus);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastSubNotifyMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastSubNotifyMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.event = WebcastMessageEvent.decode(reader, reader.uint32());\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.user = User.decode(reader, reader.uint32());\n          continue;\n        }\n        case 3: {\n          if (tag !== 24) {\n            break;\n          }\n\n          message.exhibitionType = reader.int32();\n          continue;\n        }\n        case 4: {\n          if (tag !== 32) {\n            break;\n          }\n\n          message.subMonth = reader.int32();\n          continue;\n        }\n        case 5: {\n          if (tag !== 40) {\n            break;\n          }\n\n          message.subscribeType = reader.int32();\n          continue;\n        }\n        case 6: {\n          if (tag !== 48) {\n            break;\n          }\n\n          message.oldSubscribeStatus = reader.int32();\n          continue;\n        }\n        case 8: {\n          if (tag !== 64) {\n            break;\n          }\n\n          message.subscribingStatus = reader.int32();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastSubNotifyMessage {\n    return {\n      event: isSet(object.event) ? WebcastMessageEvent.fromJSON(object.event) : undefined,\n      user: isSet(object.user) ? User.fromJSON(object.user) : undefined,\n      exhibitionType: isSet(object.exhibitionType) ? globalThis.Number(object.exhibitionType) : 0,\n      subMonth: isSet(object.subMonth) ? globalThis.Number(object.subMonth) : 0,\n      subscribeType: isSet(object.subscribeType) ? globalThis.Number(object.subscribeType) : 0,\n      oldSubscribeStatus: isSet(object.oldSubscribeStatus) ? globalThis.Number(object.oldSubscribeStatus) : 0,\n      subscribingStatus: isSet(object.subscribingStatus) ? globalThis.Number(object.subscribingStatus) : 0,\n    };\n  },\n\n  toJSON(message: WebcastSubNotifyMessage): unknown {\n    const obj: any = {};\n    if (message.event !== undefined) {\n      obj.event = WebcastMessageEvent.toJSON(message.event);\n    }\n    if (message.user !== undefined) {\n      obj.user = User.toJSON(message.user);\n    }\n    if (message.exhibitionType !== 0) {\n      obj.exhibitionType = Math.round(message.exhibitionType);\n    }\n    if (message.subMonth !== 0) {\n      obj.subMonth = Math.round(message.subMonth);\n    }\n    if (message.subscribeType !== 0) {\n      obj.subscribeType = Math.round(message.subscribeType);\n    }\n    if (message.oldSubscribeStatus !== 0) {\n      obj.oldSubscribeStatus = Math.round(message.oldSubscribeStatus);\n    }\n    if (message.subscribingStatus !== 0) {\n      obj.subscribingStatus = Math.round(message.subscribingStatus);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastSubNotifyMessage>, I>>(base?: I): WebcastSubNotifyMessage {\n    return WebcastSubNotifyMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastSubNotifyMessage>, I>>(object: I): WebcastSubNotifyMessage {\n    const message = createBaseWebcastSubNotifyMessage();\n    message.event = (object.event !== undefined && object.event !== null)\n      ? WebcastMessageEvent.fromPartial(object.event)\n      : undefined;\n    message.user = (object.user !== undefined && object.user !== null) ? User.fromPartial(object.user) : undefined;\n    message.exhibitionType = object.exhibitionType ?? 0;\n    message.subMonth = object.subMonth ?? 0;\n    message.subscribeType = object.subscribeType ?? 0;\n    message.oldSubscribeStatus = object.oldSubscribeStatus ?? 0;\n    message.subscribingStatus = object.subscribingStatus ?? 0;\n    return message;\n  },\n};\n\nfunction createBaseUser(): User {\n  return {\n    userId: \"0\",\n    nickname: \"\",\n    profilePicture: undefined,\n    uniqueId: \"\",\n    secUid: \"\",\n    badges: [],\n    createTime: \"0\",\n    bioDescription: \"\",\n    followInfo: undefined,\n  };\n}\n\nexport const User: MessageFns<User> = {\n  encode(message: User, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.userId !== \"0\") {\n      writer.uint32(8).uint64(message.userId);\n    }\n    if (message.nickname !== \"\") {\n      writer.uint32(26).string(message.nickname);\n    }\n    if (message.profilePicture !== undefined) {\n      ProfilePicture.encode(message.profilePicture, writer.uint32(74).fork()).join();\n    }\n    if (message.uniqueId !== \"\") {\n      writer.uint32(306).string(message.uniqueId);\n    }\n    if (message.secUid !== \"\") {\n      writer.uint32(370).string(message.secUid);\n    }\n    for (const v of message.badges) {\n      UserBadgesAttributes.encode(v!, writer.uint32(514).fork()).join();\n    }\n    if (message.createTime !== \"0\") {\n      writer.uint32(128).uint64(message.createTime);\n    }\n    if (message.bioDescription !== \"\") {\n      writer.uint32(42).string(message.bioDescription);\n    }\n    if (message.followInfo !== undefined) {\n      FollowInfo.encode(message.followInfo, writer.uint32(178).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): User {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseUser();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.userId = reader.uint64().toString();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.nickname = reader.string();\n          continue;\n        }\n        case 9: {\n          if (tag !== 74) {\n            break;\n          }\n\n          message.profilePicture = ProfilePicture.decode(reader, reader.uint32());\n          continue;\n        }\n        case 38: {\n          if (tag !== 306) {\n            break;\n          }\n\n          message.uniqueId = reader.string();\n          continue;\n        }\n        case 46: {\n          if (tag !== 370) {\n            break;\n          }\n\n          message.secUid = reader.string();\n          continue;\n        }\n        case 64: {\n          if (tag !== 514) {\n            break;\n          }\n\n          message.badges.push(UserBadgesAttributes.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 16: {\n          if (tag !== 128) {\n            break;\n          }\n\n          message.createTime = reader.uint64().toString();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.bioDescription = reader.string();\n          continue;\n        }\n        case 22: {\n          if (tag !== 178) {\n            break;\n          }\n\n          message.followInfo = FollowInfo.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): User {\n    return {\n      userId: isSet(object.userId) ? globalThis.String(object.userId) : \"0\",\n      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : \"\",\n      profilePicture: isSet(object.profilePicture) ? ProfilePicture.fromJSON(object.profilePicture) : undefined,\n      uniqueId: isSet(object.uniqueId) ? globalThis.String(object.uniqueId) : \"\",\n      secUid: isSet(object.secUid) ? globalThis.String(object.secUid) : \"\",\n      badges: globalThis.Array.isArray(object?.badges)\n        ? object.badges.map((e: any) => UserBadgesAttributes.fromJSON(e))\n        : [],\n      createTime: isSet(object.createTime) ? globalThis.String(object.createTime) : \"0\",\n      bioDescription: isSet(object.bioDescription) ? globalThis.String(object.bioDescription) : \"\",\n      followInfo: isSet(object.followInfo) ? FollowInfo.fromJSON(object.followInfo) : undefined,\n    };\n  },\n\n  toJSON(message: User): unknown {\n    const obj: any = {};\n    if (message.userId !== \"0\") {\n      obj.userId = message.userId;\n    }\n    if (message.nickname !== \"\") {\n      obj.nickname = message.nickname;\n    }\n    if (message.profilePicture !== undefined) {\n      obj.profilePicture = ProfilePicture.toJSON(message.profilePicture);\n    }\n    if (message.uniqueId !== \"\") {\n      obj.uniqueId = message.uniqueId;\n    }\n    if (message.secUid !== \"\") {\n      obj.secUid = message.secUid;\n    }\n    if (message.badges?.length) {\n      obj.badges = message.badges.map((e) => UserBadgesAttributes.toJSON(e));\n    }\n    if (message.createTime !== \"0\") {\n      obj.createTime = message.createTime;\n    }\n    if (message.bioDescription !== \"\") {\n      obj.bioDescription = message.bioDescription;\n    }\n    if (message.followInfo !== undefined) {\n      obj.followInfo = FollowInfo.toJSON(message.followInfo);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<User>, I>>(base?: I): User {\n    return User.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<User>, I>>(object: I): User {\n    const message = createBaseUser();\n    message.userId = object.userId ?? \"0\";\n    message.nickname = object.nickname ?? \"\";\n    message.profilePicture = (object.profilePicture !== undefined && object.profilePicture !== null)\n      ? ProfilePicture.fromPartial(object.profilePicture)\n      : undefined;\n    message.uniqueId = object.uniqueId ?? \"\";\n    message.secUid = object.secUid ?? \"\";\n    message.badges = object.badges?.map((e) => UserBadgesAttributes.fromPartial(e)) || [];\n    message.createTime = object.createTime ?? \"0\";\n    message.bioDescription = object.bioDescription ?? \"\";\n    message.followInfo = (object.followInfo !== undefined && object.followInfo !== null)\n      ? FollowInfo.fromPartial(object.followInfo)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseFollowInfo(): FollowInfo {\n  return { followingCount: 0, followerCount: 0, followStatus: 0, pushStatus: 0 };\n}\n\nexport const FollowInfo: MessageFns<FollowInfo> = {\n  encode(message: FollowInfo, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.followingCount !== 0) {\n      writer.uint32(8).int32(message.followingCount);\n    }\n    if (message.followerCount !== 0) {\n      writer.uint32(16).int32(message.followerCount);\n    }\n    if (message.followStatus !== 0) {\n      writer.uint32(24).int32(message.followStatus);\n    }\n    if (message.pushStatus !== 0) {\n      writer.uint32(32).int32(message.pushStatus);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): FollowInfo {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseFollowInfo();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.followingCount = reader.int32();\n          continue;\n        }\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.followerCount = reader.int32();\n          continue;\n        }\n        case 3: {\n          if (tag !== 24) {\n            break;\n          }\n\n          message.followStatus = reader.int32();\n          continue;\n        }\n        case 4: {\n          if (tag !== 32) {\n            break;\n          }\n\n          message.pushStatus = reader.int32();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): FollowInfo {\n    return {\n      followingCount: isSet(object.followingCount) ? globalThis.Number(object.followingCount) : 0,\n      followerCount: isSet(object.followerCount) ? globalThis.Number(object.followerCount) : 0,\n      followStatus: isSet(object.followStatus) ? globalThis.Number(object.followStatus) : 0,\n      pushStatus: isSet(object.pushStatus) ? globalThis.Number(object.pushStatus) : 0,\n    };\n  },\n\n  toJSON(message: FollowInfo): unknown {\n    const obj: any = {};\n    if (message.followingCount !== 0) {\n      obj.followingCount = Math.round(message.followingCount);\n    }\n    if (message.followerCount !== 0) {\n      obj.followerCount = Math.round(message.followerCount);\n    }\n    if (message.followStatus !== 0) {\n      obj.followStatus = Math.round(message.followStatus);\n    }\n    if (message.pushStatus !== 0) {\n      obj.pushStatus = Math.round(message.pushStatus);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<FollowInfo>, I>>(base?: I): FollowInfo {\n    return FollowInfo.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<FollowInfo>, I>>(object: I): FollowInfo {\n    const message = createBaseFollowInfo();\n    message.followingCount = object.followingCount ?? 0;\n    message.followerCount = object.followerCount ?? 0;\n    message.followStatus = object.followStatus ?? 0;\n    message.pushStatus = object.pushStatus ?? 0;\n    return message;\n  },\n};\n\nfunction createBaseLinkUser(): LinkUser {\n  return { userId: \"0\", nickname: \"\", profilePicture: undefined, uniqueId: \"\" };\n}\n\nexport const LinkUser: MessageFns<LinkUser> = {\n  encode(message: LinkUser, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.userId !== \"0\") {\n      writer.uint32(8).uint64(message.userId);\n    }\n    if (message.nickname !== \"\") {\n      writer.uint32(18).string(message.nickname);\n    }\n    if (message.profilePicture !== undefined) {\n      ProfilePicture.encode(message.profilePicture, writer.uint32(26).fork()).join();\n    }\n    if (message.uniqueId !== \"\") {\n      writer.uint32(34).string(message.uniqueId);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): LinkUser {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseLinkUser();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.userId = reader.uint64().toString();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.nickname = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.profilePicture = ProfilePicture.decode(reader, reader.uint32());\n          continue;\n        }\n        case 4: {\n          if (tag !== 34) {\n            break;\n          }\n\n          message.uniqueId = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): LinkUser {\n    return {\n      userId: isSet(object.userId) ? globalThis.String(object.userId) : \"0\",\n      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : \"\",\n      profilePicture: isSet(object.profilePicture) ? ProfilePicture.fromJSON(object.profilePicture) : undefined,\n      uniqueId: isSet(object.uniqueId) ? globalThis.String(object.uniqueId) : \"\",\n    };\n  },\n\n  toJSON(message: LinkUser): unknown {\n    const obj: any = {};\n    if (message.userId !== \"0\") {\n      obj.userId = message.userId;\n    }\n    if (message.nickname !== \"\") {\n      obj.nickname = message.nickname;\n    }\n    if (message.profilePicture !== undefined) {\n      obj.profilePicture = ProfilePicture.toJSON(message.profilePicture);\n    }\n    if (message.uniqueId !== \"\") {\n      obj.uniqueId = message.uniqueId;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<LinkUser>, I>>(base?: I): LinkUser {\n    return LinkUser.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<LinkUser>, I>>(object: I): LinkUser {\n    const message = createBaseLinkUser();\n    message.userId = object.userId ?? \"0\";\n    message.nickname = object.nickname ?? \"\";\n    message.profilePicture = (object.profilePicture !== undefined && object.profilePicture !== null)\n      ? ProfilePicture.fromPartial(object.profilePicture)\n      : undefined;\n    message.uniqueId = object.uniqueId ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseProfilePicture(): ProfilePicture {\n  return { urls: [] };\n}\n\nexport const ProfilePicture: MessageFns<ProfilePicture> = {\n  encode(message: ProfilePicture, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    for (const v of message.urls) {\n      writer.uint32(10).string(v!);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): ProfilePicture {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseProfilePicture();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.urls.push(reader.string());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): ProfilePicture {\n    return { urls: globalThis.Array.isArray(object?.urls) ? object.urls.map((e: any) => globalThis.String(e)) : [] };\n  },\n\n  toJSON(message: ProfilePicture): unknown {\n    const obj: any = {};\n    if (message.urls?.length) {\n      obj.urls = message.urls;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<ProfilePicture>, I>>(base?: I): ProfilePicture {\n    return ProfilePicture.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<ProfilePicture>, I>>(object: I): ProfilePicture {\n    const message = createBaseProfilePicture();\n    message.urls = object.urls?.map((e) => e) || [];\n    return message;\n  },\n};\n\nfunction createBaseUserBadgesAttributes(): UserBadgesAttributes {\n  return { badgeSceneType: 0, imageBadges: [], badges: [], privilegeLogExtra: undefined };\n}\n\nexport const UserBadgesAttributes: MessageFns<UserBadgesAttributes> = {\n  encode(message: UserBadgesAttributes, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.badgeSceneType !== 0) {\n      writer.uint32(24).int32(message.badgeSceneType);\n    }\n    for (const v of message.imageBadges) {\n      UserImageBadge.encode(v!, writer.uint32(162).fork()).join();\n    }\n    for (const v of message.badges) {\n      UserBadge.encode(v!, writer.uint32(170).fork()).join();\n    }\n    if (message.privilegeLogExtra !== undefined) {\n      PrivilegeLogExtra.encode(message.privilegeLogExtra, writer.uint32(98).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): UserBadgesAttributes {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseUserBadgesAttributes();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 3: {\n          if (tag !== 24) {\n            break;\n          }\n\n          message.badgeSceneType = reader.int32();\n          continue;\n        }\n        case 20: {\n          if (tag !== 162) {\n            break;\n          }\n\n          message.imageBadges.push(UserImageBadge.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 21: {\n          if (tag !== 170) {\n            break;\n          }\n\n          message.badges.push(UserBadge.decode(reader, reader.uint32()));\n          continue;\n        }\n        case 12: {\n          if (tag !== 98) {\n            break;\n          }\n\n          message.privilegeLogExtra = PrivilegeLogExtra.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): UserBadgesAttributes {\n    return {\n      badgeSceneType: isSet(object.badgeSceneType) ? globalThis.Number(object.badgeSceneType) : 0,\n      imageBadges: globalThis.Array.isArray(object?.imageBadges)\n        ? object.imageBadges.map((e: any) => UserImageBadge.fromJSON(e))\n        : [],\n      badges: globalThis.Array.isArray(object?.badges) ? object.badges.map((e: any) => UserBadge.fromJSON(e)) : [],\n      privilegeLogExtra: isSet(object.privilegeLogExtra)\n        ? PrivilegeLogExtra.fromJSON(object.privilegeLogExtra)\n        : undefined,\n    };\n  },\n\n  toJSON(message: UserBadgesAttributes): unknown {\n    const obj: any = {};\n    if (message.badgeSceneType !== 0) {\n      obj.badgeSceneType = Math.round(message.badgeSceneType);\n    }\n    if (message.imageBadges?.length) {\n      obj.imageBadges = message.imageBadges.map((e) => UserImageBadge.toJSON(e));\n    }\n    if (message.badges?.length) {\n      obj.badges = message.badges.map((e) => UserBadge.toJSON(e));\n    }\n    if (message.privilegeLogExtra !== undefined) {\n      obj.privilegeLogExtra = PrivilegeLogExtra.toJSON(message.privilegeLogExtra);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<UserBadgesAttributes>, I>>(base?: I): UserBadgesAttributes {\n    return UserBadgesAttributes.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<UserBadgesAttributes>, I>>(object: I): UserBadgesAttributes {\n    const message = createBaseUserBadgesAttributes();\n    message.badgeSceneType = object.badgeSceneType ?? 0;\n    message.imageBadges = object.imageBadges?.map((e) => UserImageBadge.fromPartial(e)) || [];\n    message.badges = object.badges?.map((e) => UserBadge.fromPartial(e)) || [];\n    message.privilegeLogExtra = (object.privilegeLogExtra !== undefined && object.privilegeLogExtra !== null)\n      ? PrivilegeLogExtra.fromPartial(object.privilegeLogExtra)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBasePrivilegeLogExtra(): PrivilegeLogExtra {\n  return { privilegeId: \"\", level: \"\" };\n}\n\nexport const PrivilegeLogExtra: MessageFns<PrivilegeLogExtra> = {\n  encode(message: PrivilegeLogExtra, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.privilegeId !== \"\") {\n      writer.uint32(18).string(message.privilegeId);\n    }\n    if (message.level !== \"\") {\n      writer.uint32(42).string(message.level);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): PrivilegeLogExtra {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBasePrivilegeLogExtra();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.privilegeId = reader.string();\n          continue;\n        }\n        case 5: {\n          if (tag !== 42) {\n            break;\n          }\n\n          message.level = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): PrivilegeLogExtra {\n    return {\n      privilegeId: isSet(object.privilegeId) ? globalThis.String(object.privilegeId) : \"\",\n      level: isSet(object.level) ? globalThis.String(object.level) : \"\",\n    };\n  },\n\n  toJSON(message: PrivilegeLogExtra): unknown {\n    const obj: any = {};\n    if (message.privilegeId !== \"\") {\n      obj.privilegeId = message.privilegeId;\n    }\n    if (message.level !== \"\") {\n      obj.level = message.level;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<PrivilegeLogExtra>, I>>(base?: I): PrivilegeLogExtra {\n    return PrivilegeLogExtra.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<PrivilegeLogExtra>, I>>(object: I): PrivilegeLogExtra {\n    const message = createBasePrivilegeLogExtra();\n    message.privilegeId = object.privilegeId ?? \"\";\n    message.level = object.level ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseUserBadge(): UserBadge {\n  return { type: \"\", name: \"\" };\n}\n\nexport const UserBadge: MessageFns<UserBadge> = {\n  encode(message: UserBadge, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.type !== \"\") {\n      writer.uint32(18).string(message.type);\n    }\n    if (message.name !== \"\") {\n      writer.uint32(26).string(message.name);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): UserBadge {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseUserBadge();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.type = reader.string();\n          continue;\n        }\n        case 3: {\n          if (tag !== 26) {\n            break;\n          }\n\n          message.name = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): UserBadge {\n    return {\n      type: isSet(object.type) ? globalThis.String(object.type) : \"\",\n      name: isSet(object.name) ? globalThis.String(object.name) : \"\",\n    };\n  },\n\n  toJSON(message: UserBadge): unknown {\n    const obj: any = {};\n    if (message.type !== \"\") {\n      obj.type = message.type;\n    }\n    if (message.name !== \"\") {\n      obj.name = message.name;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<UserBadge>, I>>(base?: I): UserBadge {\n    return UserBadge.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<UserBadge>, I>>(object: I): UserBadge {\n    const message = createBaseUserBadge();\n    message.type = object.type ?? \"\";\n    message.name = object.name ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseUserImageBadge(): UserImageBadge {\n  return { displayType: 0, image: undefined };\n}\n\nexport const UserImageBadge: MessageFns<UserImageBadge> = {\n  encode(message: UserImageBadge, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.displayType !== 0) {\n      writer.uint32(8).int32(message.displayType);\n    }\n    if (message.image !== undefined) {\n      UserImageBadgeImage.encode(message.image, writer.uint32(18).fork()).join();\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): UserImageBadge {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseUserImageBadge();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 8) {\n            break;\n          }\n\n          message.displayType = reader.int32();\n          continue;\n        }\n        case 2: {\n          if (tag !== 18) {\n            break;\n          }\n\n          message.image = UserImageBadgeImage.decode(reader, reader.uint32());\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): UserImageBadge {\n    return {\n      displayType: isSet(object.displayType) ? globalThis.Number(object.displayType) : 0,\n      image: isSet(object.image) ? UserImageBadgeImage.fromJSON(object.image) : undefined,\n    };\n  },\n\n  toJSON(message: UserImageBadge): unknown {\n    const obj: any = {};\n    if (message.displayType !== 0) {\n      obj.displayType = Math.round(message.displayType);\n    }\n    if (message.image !== undefined) {\n      obj.image = UserImageBadgeImage.toJSON(message.image);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<UserImageBadge>, I>>(base?: I): UserImageBadge {\n    return UserImageBadge.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<UserImageBadge>, I>>(object: I): UserImageBadge {\n    const message = createBaseUserImageBadge();\n    message.displayType = object.displayType ?? 0;\n    message.image = (object.image !== undefined && object.image !== null)\n      ? UserImageBadgeImage.fromPartial(object.image)\n      : undefined;\n    return message;\n  },\n};\n\nfunction createBaseUserImageBadgeImage(): UserImageBadgeImage {\n  return { url: \"\" };\n}\n\nexport const UserImageBadgeImage: MessageFns<UserImageBadgeImage> = {\n  encode(message: UserImageBadgeImage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.url !== \"\") {\n      writer.uint32(10).string(message.url);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): UserImageBadgeImage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseUserImageBadgeImage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 1: {\n          if (tag !== 10) {\n            break;\n          }\n\n          message.url = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): UserImageBadgeImage {\n    return { url: isSet(object.url) ? globalThis.String(object.url) : \"\" };\n  },\n\n  toJSON(message: UserImageBadgeImage): unknown {\n    const obj: any = {};\n    if (message.url !== \"\") {\n      obj.url = message.url;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<UserImageBadgeImage>, I>>(base?: I): UserImageBadgeImage {\n    return UserImageBadgeImage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<UserImageBadgeImage>, I>>(object: I): UserImageBadgeImage {\n    const message = createBaseUserImageBadgeImage();\n    message.url = object.url ?? \"\";\n    return message;\n  },\n};\n\nfunction createBaseWebcastWebsocketMessage(): WebcastWebsocketMessage {\n  return { id: \"0\", type: \"\", binary: new Uint8Array(0) };\n}\n\nexport const WebcastWebsocketMessage: MessageFns<WebcastWebsocketMessage> = {\n  encode(message: WebcastWebsocketMessage, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.id !== \"0\") {\n      writer.uint32(16).uint64(message.id);\n    }\n    if (message.type !== \"\") {\n      writer.uint32(58).string(message.type);\n    }\n    if (message.binary.length !== 0) {\n      writer.uint32(66).bytes(message.binary);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastWebsocketMessage {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastWebsocketMessage();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.id = reader.uint64().toString();\n          continue;\n        }\n        case 7: {\n          if (tag !== 58) {\n            break;\n          }\n\n          message.type = reader.string();\n          continue;\n        }\n        case 8: {\n          if (tag !== 66) {\n            break;\n          }\n\n          message.binary = reader.bytes();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastWebsocketMessage {\n    return {\n      id: isSet(object.id) ? globalThis.String(object.id) : \"0\",\n      type: isSet(object.type) ? globalThis.String(object.type) : \"\",\n      binary: isSet(object.binary) ? bytesFromBase64(object.binary) : new Uint8Array(0),\n    };\n  },\n\n  toJSON(message: WebcastWebsocketMessage): unknown {\n    const obj: any = {};\n    if (message.id !== \"0\") {\n      obj.id = message.id;\n    }\n    if (message.type !== \"\") {\n      obj.type = message.type;\n    }\n    if (message.binary.length !== 0) {\n      obj.binary = base64FromBytes(message.binary);\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastWebsocketMessage>, I>>(base?: I): WebcastWebsocketMessage {\n    return WebcastWebsocketMessage.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastWebsocketMessage>, I>>(object: I): WebcastWebsocketMessage {\n    const message = createBaseWebcastWebsocketMessage();\n    message.id = object.id ?? \"0\";\n    message.type = object.type ?? \"\";\n    message.binary = object.binary ?? new Uint8Array(0);\n    return message;\n  },\n};\n\nfunction createBaseWebcastWebsocketAck(): WebcastWebsocketAck {\n  return { id: \"0\", type: \"\" };\n}\n\nexport const WebcastWebsocketAck: MessageFns<WebcastWebsocketAck> = {\n  encode(message: WebcastWebsocketAck, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {\n    if (message.id !== \"0\") {\n      writer.uint32(16).uint64(message.id);\n    }\n    if (message.type !== \"\") {\n      writer.uint32(58).string(message.type);\n    }\n    return writer;\n  },\n\n  decode(input: BinaryReader | Uint8Array, length?: number): WebcastWebsocketAck {\n    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);\n    let end = length === undefined ? reader.len : reader.pos + length;\n    const message = createBaseWebcastWebsocketAck();\n    while (reader.pos < end) {\n      const tag = reader.uint32();\n      switch (tag >>> 3) {\n        case 2: {\n          if (tag !== 16) {\n            break;\n          }\n\n          message.id = reader.uint64().toString();\n          continue;\n        }\n        case 7: {\n          if (tag !== 58) {\n            break;\n          }\n\n          message.type = reader.string();\n          continue;\n        }\n      }\n      if ((tag & 7) === 4 || tag === 0) {\n        break;\n      }\n      reader.skip(tag & 7);\n    }\n    return message;\n  },\n\n  fromJSON(object: any): WebcastWebsocketAck {\n    return {\n      id: isSet(object.id) ? globalThis.String(object.id) : \"0\",\n      type: isSet(object.type) ? globalThis.String(object.type) : \"\",\n    };\n  },\n\n  toJSON(message: WebcastWebsocketAck): unknown {\n    const obj: any = {};\n    if (message.id !== \"0\") {\n      obj.id = message.id;\n    }\n    if (message.type !== \"\") {\n      obj.type = message.type;\n    }\n    return obj;\n  },\n\n  create<I extends Exact<DeepPartial<WebcastWebsocketAck>, I>>(base?: I): WebcastWebsocketAck {\n    return WebcastWebsocketAck.fromPartial(base ?? ({} as any));\n  },\n  fromPartial<I extends Exact<DeepPartial<WebcastWebsocketAck>, I>>(object: I): WebcastWebsocketAck {\n    const message = createBaseWebcastWebsocketAck();\n    message.id = object.id ?? \"0\";\n    message.type = object.type ?? \"\";\n    return message;\n  },\n};\n\nfunction bytesFromBase64(b64: string): Uint8Array {\n  if ((globalThis as any).Buffer) {\n    return Uint8Array.from(globalThis.Buffer.from(b64, \"base64\"));\n  } else {\n    const bin = globalThis.atob(b64);\n    const arr = new Uint8Array(bin.length);\n    for (let i = 0; i < bin.length; ++i) {\n      arr[i] = bin.charCodeAt(i);\n    }\n    return arr;\n  }\n}\n\nfunction base64FromBytes(arr: Uint8Array): string {\n  if ((globalThis as any).Buffer) {\n    return globalThis.Buffer.from(arr).toString(\"base64\");\n  } else {\n    const bin: string[] = [];\n    arr.forEach((byte) => {\n      bin.push(globalThis.String.fromCharCode(byte));\n    });\n    return globalThis.btoa(bin.join(\"\"));\n  }\n}\n\ntype Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;\n\nexport type DeepPartial<T> = T extends Builtin ? T\n  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>\n  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>\n  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }\n  : Partial<T>;\n\ntype KeysOfUnion<T> = T extends T ? keyof T : never;\nexport type Exact<P, I extends P> = P extends Builtin ? P\n  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };\n\nfunction isSet(value: any): boolean {\n  return value !== null && value !== undefined;\n}\n\nexport interface MessageFns<T> {\n  encode(message: T, writer?: BinaryWriter): BinaryWriter;\n  decode(input: BinaryReader | Uint8Array, length?: number): T;\n  fromJSON(object: any): T;\n  toJSON(message: T): unknown;\n  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;\n  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;\n}\n"]}