{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/types/errors.ts"], "names": [], "mappings": ";;;AAEA,MAAM,YAAa,SAAQ,KAAK;IAC5B,YAAY,OAAe;QACvB,KAAK,CAAC,OAAO,CAAC,CAAC;IACnB,CAAC;CACJ;AAED,MAAa,oBAAqB,SAAQ,KAAK;CAC9C;AADD,oDACC;AAED,MAAa,kBAAmB,SAAQ,KAAK;IACb;IAA5B,YAA4B,MAAe,EAAE,GAAG,IAAW;QACvD,KAAK,EAAE,CAAC;QADgB,WAAM,GAAN,MAAM,CAAS;IAE3C,CAAC;CACJ;AAJD,gDAIC;AAED,MAAa,gBAAiB,SAAQ,KAAK;IACX;IAA5B,YAA4B,MAAe,EAAE,GAAG,IAAW;QACvD,KAAK,EAAE,CAAC;QADgB,WAAM,GAAN,MAAM,CAAS;IAE3C,CAAC;CACJ;AAJD,4CAIC;AAGD,MAAa,oBAAqB,SAAQ,KAAK;IAGvB;IAFpB,YACI,OAAe,EACC,aAAoB,SAAS;QAE7C,KAAK,CAAC,OAAO,CAAC,CAAC;QAFC,eAAU,GAAV,UAAU,CAAmB;QAG7C,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;IACvC,CAAC;CACJ;AARD,oDAQC;AAED,MAAa,kBAAmB,SAAQ,KAAK;CAC5C;AADD,gDACC;AAGD,MAAa,sBAAuB,SAAQ,YAAY;CACvD;AADD,wDACC;AAED,MAAa,qBAAsB,SAAQ,YAAY;CACtD;AADD,sDACC;AAED,MAAa,gBAAiB,SAAQ,YAAY;CACjD;AADD,4CACC;AAED,MAAa,sBAAuB,SAAQ,KAAK;CAChD;AADD,wDACC;AAED,MAAa,eAAgB,SAAQ,KAAK;IACtC,YAAY,OAAe;QACvB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACtC,CAAC;CACJ;AALD,0CAKC;AAED,IAAY,WAQX;AARD,WAAY,WAAW;IACnB,0CAA2B,CAAA;IAC3B,8CAA+B,CAAA;IAC/B,8CAA+B,CAAA;IAC/B,0CAA2B,CAAA;IAC3B,8CAA+B,CAAA;IAC/B,kDAAmC,CAAA;IACnC,oDAAqC,CAAA;AACzC,CAAC,EARW,WAAW,GAAX,mBAAW,KAAX,mBAAW,QAQtB;AAED,MAAa,0CAA2C,SAAQ,KAAK;CACpE;AADD,gGACC;AAED,MAAa,YAAa,SAAQ,eAAe;IACtC,MAAM,CAAc;IACX,KAAK,CAAU;IACf,OAAO,CAAU;IAEjC,YACI,MAAmB,EACnB,KAAc,EACd,OAAgB,EAChB,GAAG,IAA4B;QAE/B,KAAK,CAAC,CAAC,IAAI,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAEM,MAAM,CAAC,uBAAuB,CAAC,OAAe;QACjD,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QACzB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,UAAU,GAAG,qBAAqB,CAAC;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/D,MAAM,UAAU,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAEpD,MAAM,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QAClD,MAAM,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,GAAG,GAAG,UAAU,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC;QAC/G,MAAM,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QACzD,MAAM,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,IAAI,CAAC;QAEnC,OAAO,OAAO,MAAM,OAAO,MAAM,OAAO,IAAI,OAAO,MAAM,IAAI,CAAC;IAClE,CAAC;CACJ;AA/BD,oCA+BC;AAED,MAAa,uBAAwB,SAAQ,YAAY;IACrC,UAAU,CAAS;IACnB,SAAS,CAAU;IAEnC,YAAY,UAA8B,EAAE,SAAiB,EAAE,QAAuB;QAClF,MAAM,UAAU,GAAG,uBAAuB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACzE,MAAM,SAAS,GAAG,uBAAuB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QACvE,MAAM,KAAK,GAAG,uBAAuB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;QACtF,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAE/C,MAAM,YAAY,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;QACpE,MAAM,IAAI,GAAa,CAAC,YAAY,CAAC,CAAC;QAEtC,IAAI,UAAU,EAAE;YACZ,MAAM,SAAS,GAAG,YAAY,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;YACnE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACxB;QAED,KAAK,CAAC,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAEvD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,KAAyB;QACtD,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC/C,CAAC;IAEO,MAAM,CAAC,mBAAmB,CAAC,QAAuB;QACtD,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;QACpE,OAAO,UAAU,GAAG,IAAI,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,kBAAkB,CAAC,QAAuB;QACrD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACpD,OAAO,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;IACtD,CAAC;CAEJ;AAtCD,0DAsCC;AAED,MAAa,2BAA4B,SAAQ,YAAY;IACzD,YAAY,GAAG,IAAc;QACzB,KAAK,CAAC,WAAW,CAAC,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC;IACpE,CAAC;CACJ;AAJD,kEAIC;AAED,MAAa,mBAAoB,SAAQ,YAAY;IACjD,YAAY,UAAkB,EAAE,GAAG,IAAc;QAC7C,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC,CAAC;QAC5D,KAAK,CAAC,WAAW,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC;IACtE,CAAC;CACJ;AALD,kDAKC;AAED,MAAa,qCAAsC,SAAQ,YAAY;IACnE,YAAY,GAAG,IAAc;QACzB,KAAK,CAAC,WAAW,CAAC,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC;IACvE,CAAC;CACJ;AAJD,sFAIC", "sourcesContent": ["import { AxiosResponse } from 'axios';\n\nclass ConnectError extends <PERSON><PERSON>r {\n    constructor(message: string) {\n        super(message);\n    }\n}\n\nexport class InvalidUniqueIdError extends Error {\n}\n\nexport class ExtractRoomIdError extends Error {\n    constructor(public readonly errors: Error[], ...args: any[]) {\n        super();\n    }\n}\n\nexport class FetchIsLiveError extends Error {\n    constructor(public readonly errors: Error[], ...args: any[]) {\n        super();\n    }\n}\n\n\nexport class InvalidResponseError extends Error {\n    constructor(\n        message: string,\n        public readonly requestErr: Error = undefined\n    ) {\n        super(message);\n        this.name = 'InvalidResponseError';\n    }\n}\n\nexport class MissingRoomIdError extends Error {\n}\n\n\nexport class AlreadyConnectingError extends ConnectError {\n}\n\nexport class AlreadyConnectedError extends ConnectError {\n}\n\nexport class UserOfflineError extends ConnectError {\n}\n\nexport class InvalidSchemaNameError extends Error {\n}\n\nexport class TikTokLiveError extends Error {\n    constructor(message: string) {\n        super(message);\n        this.name = this.constructor.name;\n    }\n}\n\nexport enum ErrorReason {\n    RATE_LIMIT = 'Rate Limited',\n    CONNECT_ERROR = 'Connect Error',\n    EMPTY_PAYLOAD = 'Empty Payload',\n    SIGN_NOT_200 = 'Sign Error',\n    EMPTY_COOKIES = 'Empty Cookies',\n    PREMIUM_FEATURE = 'Premium Feature',\n    AUTHENTICATED_WS = 'Authenticated WS'\n}\n\nexport class FetchSignedWebSocketIdentityParameterError extends Error {\n}\n\nexport class SignAPIError extends TikTokLiveError {\n    public reason: ErrorReason;\n    public readonly logId?: number;\n    public readonly agentId?: string;\n\n    constructor(\n        reason: ErrorReason,\n        logId?: number,\n        agentId?: string,\n        ...args: (string | undefined)[]\n    ) {\n        super([`[${reason}]`, ...args].join(' '));\n        this.reason = reason;\n        this.logId = logId;\n        this.agentId = agentId;\n    }\n\n    public static formatSignServerMessage(message: string): string {\n        message = message.trim();\n        const msgLen = message.length;\n        const headerText = 'SIGN SERVER MESSAGE';\n        const headerLen = Math.floor((msgLen - headerText.length) / 2);\n        const paddingLen = (msgLen - headerText.length) % 2;\n\n        const footer = '+' + '-'.repeat(msgLen + 2) + '+';\n        const header = '+' + '-'.repeat(headerLen) + ' ' + headerText + ' ' + '-'.repeat(headerLen + paddingLen) + '+';\n        const prefix = '|' + ' '.repeat(header.length - 2) + '|';\n        const body = '| ' + message + ' |';\n\n        return `\\n\\t${prefix}\\n\\t${header}\\n\\t${body}\\n\\t${footer}\\n`;\n    }\n}\n\nexport class SignatureRateLimitError extends SignAPIError {\n    public readonly retryAfter: number;\n    public readonly resetTime?: number;\n\n    constructor(apiMessage: string | undefined, formatStr: string, response: AxiosResponse) {\n        const retryAfter = SignatureRateLimitError.calculateRetryAfter(response);\n        const resetTime = SignatureRateLimitError.calculateResetTime(response);\n        const logId = SignatureRateLimitError.parseHeaderNumber(response.headers['X-Log-ID']);\n        const agentId = response.headers['X-Agent-ID'];\n\n        const formattedMsg = formatStr.replace('%s', retryAfter.toString());\n        const args: string[] = [formattedMsg];\n\n        if (apiMessage) {\n            const serverMsg = SignAPIError.formatSignServerMessage(apiMessage);\n            args.push(serverMsg);\n        }\n\n        super(ErrorReason.RATE_LIMIT, logId, agentId, ...args);\n\n        this.retryAfter = retryAfter;\n        this.resetTime = resetTime;\n    }\n\n    private static parseHeaderNumber(value: string | undefined): number | undefined {\n        return value ? parseInt(value) : undefined;\n    }\n\n    private static calculateRetryAfter(response: AxiosResponse): number {\n        const retryAfter = parseInt(response.headers['retry-after'] || '0');\n        return retryAfter * 1000;\n    }\n\n    private static calculateResetTime(response: AxiosResponse): number | undefined {\n        const value = response.headers['x-ratelimit-reset'];\n        return value ? parseInt(value) * 1000 : undefined;\n    }\n\n}\n\nexport class SignatureMissingTokensError extends SignAPIError {\n    constructor(...args: string[]) {\n        super(ErrorReason.EMPTY_PAYLOAD, undefined, undefined, ...args);\n    }\n}\n\nexport class PremiumFeatureError extends SignAPIError {\n    constructor(apiMessage: string, ...args: string[]) {\n        args.push(SignAPIError.formatSignServerMessage(apiMessage));\n        super(ErrorReason.PREMIUM_FEATURE, undefined, undefined, ...args);\n    }\n}\n\nexport class AuthenticatedWebSocketConnectionError extends SignAPIError {\n    constructor(...args: string[]) {\n        super(ErrorReason.AUTHENTICATED_WS, undefined, undefined, ...args);\n    }\n}\n\n"]}