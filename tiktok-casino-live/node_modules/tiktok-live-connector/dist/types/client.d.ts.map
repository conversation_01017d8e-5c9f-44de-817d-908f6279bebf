{"version": 3, "file": "client.d.ts", "sourceRoot": "", "sources": ["../../src/types/client.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,YAAY,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AACvF,OAAO,EAAE,kBAAkB,EAAE,MAAM,OAAO,CAAC;AAC3C,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAElC,MAAM,MAAM,2BAA2B,GAAG;IACtC,kBAAkB,EAAE,OAAO,CAAC;IAC5B,sBAAsB,EAAE,OAAO,CAAC;IAChC,sBAAsB,EAAE,OAAO,CAAC;IAChC,oBAAoB,EAAE,OAAO,CAAC;IAC9B,wBAAwB,EAAE,MAAM,CAAC;IACjC,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;IAC1B,cAAc,EAAE,OAAO,CAAC;IACxB,iBAAiB,EAAE,MAAM,EAAE,CAAC;IAC5B,mBAAmB,EAAE,OAAO,CAAC;IAC7B,sBAAsB,EAAE,OAAO,CAAC;IAEhC,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACxC,gBAAgB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzC,gBAAgB,EAAE,kBAAkB,CAAC;IAErC,eAAe,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACxC,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACvC,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC;IAGrC,uBAAuB,CAAC,EAAE,CAAC,KAAK,EAAE,0BAA0B,KAAK,OAAO,CAAC,eAAe,CAAC,CAAA;CAC5F,CAAA;AAGD,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG;IAAE,MAAM,EAAE,MAAM,CAAA;CAAE,CAAA;AAC/D,MAAM,MAAM,YAAY,GAAG,GAAG,CAAC;AAE/B,MAAM,MAAM,0BAA0B,GAAG;IACrC,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC7B,SAAS,CAAC,EAAE,MAAM,CAAC;CACtB,CAAA;AAGD,MAAM,MAAM,uBAAuB,GAAG;IAClC,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACtC,YAAY,EAAE,kBAAkB,CAAC;IACjC,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACrC,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,UAAU,CAAC,EAAE,MAAM,CAAC;CACvB,CAAA;AAED,MAAM,WAAW,mCAAoC,SAAQ,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAC5E,QAAQ,CAAC,EAAE,MAAM,CAAC;CACrB;AAGD,MAAM,MAAM,8BAA8B,GAAG,uBAAuB,GAAG;IACnE,eAAe,CAAC,EAAE,GAAG,CAAC;CACzB,CAAA;AAGD,MAAM,WAAW,cAAc;IAC3B,eAAe,EAAE,MAAM,CAAC;IACxB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,kBAAkB,EAAE,MAAM,CAAC;IAG3B,2BAA2B,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACpD,0BAA0B,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACnD,2BAA2B,EAAE,kBAAkB,CAAC;IAChD,yCAAyC,EAAE,MAAM,CAAC;IAClD,2BAA2B,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG;QAClD,YAAY,EAAE,MAAM,CAAC;KACxB,CAAC;IAGF,wBAAwB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjD,yBAAyB,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG;QAChD,YAAY,EAAE,MAAM,CAAC;KACxB,CAAC;CAEL;AAED,MAAM,WAAW,yBAAyB;IACtC,gBAAgB,EAAE,MAAM,EAAE,CAAC;CAC9B;AAED,KAAK,kBAAkB,CAAC,CAAC,IAAI,CAAC,SAAS,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAGvE,MAAM,MAAM,cAAc,GAAG;KACxB,CAAC,IAAI,MAAM,OAAO,YAAY,IAAI,kBAAkB,CAAC,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,KAAK,GAAG,KAAK,GAAG,CAAC,GACvG,kBAAkB,CAAC,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;CAC7C,CAAC;AAGF,MAAM,MAAM,mBAAmB,GAAG;KAC7B,CAAC,IAAI,MAAM,cAAc,IAAI,CAAC,SAAS,UAAU,MAAM,EAAE,GAAG,CAAC,GAAG,KAAK,GAAG,cAAc,CAAC,CAAC,CAAC;CAC7F,CAAC;AAEF,OAAO,QAAQ,uBAAuB,CAAC;IACnC,UAAiB,OAAO;QACpB,WAAW,CAAC,EAAE,mBAAmB,CAAC,MAAM,mBAAmB,CAAC,CAAC;KAChE;CACJ;AAGD,MAAM,MAAM,cAAc,GACtB,MAAM,GACJ,iBAAiB,GACjB,iBAAiB,GACjB,uBAAuB,GACvB,SAAS,GACT,eAAe,GACf,cAAc,CAAC;AAGrB,MAAM,MAAM,8BAA8B,GAAG,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,EAAE,SAAS,CAAC,GAAG;IAC5F,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChC,WAAW,EAAE,OAAO,CAAC;CACxB,CAAC"}