"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v5.27.1
// source: tiktok-schema.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilePicture = exports.LinkUser = exports.FollowInfo = exports.User = exports.WebcastSubNotifyMessage = exports.TreasureBoxData = exports.TreasureBoxUser4 = exports.TreasureBoxUser3 = exports.TreasureBoxUser2 = exports.TreasureBoxUser = exports.WebcastEnvelopeMessage = exports.EmoteImage = exports.EmoteDetails = exports.WebcastHourlyRankMessage = exports.RankContainer = exports.WeeklyRanking = exports.RankItem = exports.WebcastInRoomBannerMessage = exports.SystemMessage = exports.WebcastLiveIntroMessage = exports.WebcastMessageEventDetails = exports.WebcastMessageEvent = exports.QuestionDetails = exports.WebcastQuestionNewMessage = exports.WebcastLikeMessage = exports.WebcastSocialMessage = exports.WebcastLinkMicArmiesGroup = exports.WebcastLinkMicArmiesItems = exports.WebcastLinkMicArmies = exports.WebcastLinkMicBattleGroup = exports.WebcastLinkMicBattleItems = exports.WebcastLinkMicBattle = exports.WebcastGiftMessageGiftImage = exports.WebcastGiftMessageGiftExtra = exports.WebcastGiftMessageGiftDetails = exports.WebcastGiftMessage = exports.WebcastMemberMessage = exports.WebcastSubEmote = exports.WebcastEmoteChatMessage = exports.WebcastChatMessage = exports.TopUser = exports.WebcastRoomUserSeqMessage = exports.WebcastControlMessage = exports.WebsocketParam = exports.Message = exports.WebcastResponse = exports.controlActionToJSON = exports.controlActionFromJSON = exports.ControlAction = exports.protobufPackage = void 0;
exports.WebcastWebsocketAck = exports.WebcastWebsocketMessage = exports.UserImageBadgeImage = exports.UserImageBadge = exports.UserBadge = exports.PrivilegeLogExtra = exports.UserBadgesAttributes = void 0;
/* eslint-disable */
const wire_1 = require("@bufbuild/protobuf/wire");
exports.protobufPackage = "TikTok";
var ControlAction;
(function (ControlAction) {
    ControlAction[ControlAction["CONTROL_ACTION_FALLBACK_UNKNOWN"] = 0] = "CONTROL_ACTION_FALLBACK_UNKNOWN";
    ControlAction[ControlAction["CONTROL_ACTION_STREAM_PAUSED"] = 1] = "CONTROL_ACTION_STREAM_PAUSED";
    ControlAction[ControlAction["CONTROL_ACTION_STREAM_UNPAUSED"] = 2] = "CONTROL_ACTION_STREAM_UNPAUSED";
    ControlAction[ControlAction["CONTROL_ACTION_STREAM_ENDED"] = 3] = "CONTROL_ACTION_STREAM_ENDED";
    ControlAction[ControlAction["CONTROL_ACTION_STREAM_SUSPENDED"] = 4] = "CONTROL_ACTION_STREAM_SUSPENDED";
    ControlAction[ControlAction["UNRECOGNIZED"] = -1] = "UNRECOGNIZED";
})(ControlAction = exports.ControlAction || (exports.ControlAction = {}));
function controlActionFromJSON(object) {
    switch (object) {
        case 0:
        case "CONTROL_ACTION_FALLBACK_UNKNOWN":
            return ControlAction.CONTROL_ACTION_FALLBACK_UNKNOWN;
        case 1:
        case "CONTROL_ACTION_STREAM_PAUSED":
            return ControlAction.CONTROL_ACTION_STREAM_PAUSED;
        case 2:
        case "CONTROL_ACTION_STREAM_UNPAUSED":
            return ControlAction.CONTROL_ACTION_STREAM_UNPAUSED;
        case 3:
        case "CONTROL_ACTION_STREAM_ENDED":
            return ControlAction.CONTROL_ACTION_STREAM_ENDED;
        case 4:
        case "CONTROL_ACTION_STREAM_SUSPENDED":
            return ControlAction.CONTROL_ACTION_STREAM_SUSPENDED;
        case -1:
        case "UNRECOGNIZED":
        default:
            return ControlAction.UNRECOGNIZED;
    }
}
exports.controlActionFromJSON = controlActionFromJSON;
function controlActionToJSON(object) {
    switch (object) {
        case ControlAction.CONTROL_ACTION_FALLBACK_UNKNOWN:
            return "CONTROL_ACTION_FALLBACK_UNKNOWN";
        case ControlAction.CONTROL_ACTION_STREAM_PAUSED:
            return "CONTROL_ACTION_STREAM_PAUSED";
        case ControlAction.CONTROL_ACTION_STREAM_UNPAUSED:
            return "CONTROL_ACTION_STREAM_UNPAUSED";
        case ControlAction.CONTROL_ACTION_STREAM_ENDED:
            return "CONTROL_ACTION_STREAM_ENDED";
        case ControlAction.CONTROL_ACTION_STREAM_SUSPENDED:
            return "CONTROL_ACTION_STREAM_SUSPENDED";
        case ControlAction.UNRECOGNIZED:
        default:
            return "UNRECOGNIZED";
    }
}
exports.controlActionToJSON = controlActionToJSON;
function createBaseWebcastResponse() {
    return {
        messages: [],
        cursor: "",
        fetchInterval: 0,
        serverTimestamp: "0",
        internalExt: "",
        fetchType: 0,
        wsParams: [],
        heartbeatDuration: 0,
        needAck: false,
        wsUrl: "",
    };
}
exports.WebcastResponse = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        for (const v of message.messages) {
            exports.Message.encode(v, writer.uint32(10).fork()).join();
        }
        if (message.cursor !== "") {
            writer.uint32(18).string(message.cursor);
        }
        if (message.fetchInterval !== 0) {
            writer.uint32(24).int32(message.fetchInterval);
        }
        if (message.serverTimestamp !== "0") {
            writer.uint32(32).int64(message.serverTimestamp);
        }
        if (message.internalExt !== "") {
            writer.uint32(42).string(message.internalExt);
        }
        if (message.fetchType !== 0) {
            writer.uint32(48).int32(message.fetchType);
        }
        for (const v of message.wsParams) {
            exports.WebsocketParam.encode(v, writer.uint32(58).fork()).join();
        }
        if (message.heartbeatDuration !== 0) {
            writer.uint32(64).int32(message.heartbeatDuration);
        }
        if (message.needAck !== false) {
            writer.uint32(72).bool(message.needAck);
        }
        if (message.wsUrl !== "") {
            writer.uint32(82).string(message.wsUrl);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastResponse();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.messages.push(exports.Message.decode(reader, reader.uint32()));
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.cursor = reader.string();
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.fetchInterval = reader.int32();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.serverTimestamp = reader.int64().toString();
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.internalExt = reader.string();
                    continue;
                }
                case 6: {
                    if (tag !== 48) {
                        break;
                    }
                    message.fetchType = reader.int32();
                    continue;
                }
                case 7: {
                    if (tag !== 58) {
                        break;
                    }
                    message.wsParams.push(exports.WebsocketParam.decode(reader, reader.uint32()));
                    continue;
                }
                case 8: {
                    if (tag !== 64) {
                        break;
                    }
                    message.heartbeatDuration = reader.int32();
                    continue;
                }
                case 9: {
                    if (tag !== 72) {
                        break;
                    }
                    message.needAck = reader.bool();
                    continue;
                }
                case 10: {
                    if (tag !== 82) {
                        break;
                    }
                    message.wsUrl = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            messages: globalThis.Array.isArray(object?.messages) ? object.messages.map((e) => exports.Message.fromJSON(e)) : [],
            cursor: isSet(object.cursor) ? globalThis.String(object.cursor) : "",
            fetchInterval: isSet(object.fetchInterval) ? globalThis.Number(object.fetchInterval) : 0,
            serverTimestamp: isSet(object.serverTimestamp) ? globalThis.String(object.serverTimestamp) : "0",
            internalExt: isSet(object.internalExt) ? globalThis.String(object.internalExt) : "",
            fetchType: isSet(object.fetchType) ? globalThis.Number(object.fetchType) : 0,
            wsParams: globalThis.Array.isArray(object?.wsParams)
                ? object.wsParams.map((e) => exports.WebsocketParam.fromJSON(e))
                : [],
            heartbeatDuration: isSet(object.heartbeatDuration) ? globalThis.Number(object.heartbeatDuration) : 0,
            needAck: isSet(object.needAck) ? globalThis.Boolean(object.needAck) : false,
            wsUrl: isSet(object.wsUrl) ? globalThis.String(object.wsUrl) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.messages?.length) {
            obj.messages = message.messages.map((e) => exports.Message.toJSON(e));
        }
        if (message.cursor !== "") {
            obj.cursor = message.cursor;
        }
        if (message.fetchInterval !== 0) {
            obj.fetchInterval = Math.round(message.fetchInterval);
        }
        if (message.serverTimestamp !== "0") {
            obj.serverTimestamp = message.serverTimestamp;
        }
        if (message.internalExt !== "") {
            obj.internalExt = message.internalExt;
        }
        if (message.fetchType !== 0) {
            obj.fetchType = Math.round(message.fetchType);
        }
        if (message.wsParams?.length) {
            obj.wsParams = message.wsParams.map((e) => exports.WebsocketParam.toJSON(e));
        }
        if (message.heartbeatDuration !== 0) {
            obj.heartbeatDuration = Math.round(message.heartbeatDuration);
        }
        if (message.needAck !== false) {
            obj.needAck = message.needAck;
        }
        if (message.wsUrl !== "") {
            obj.wsUrl = message.wsUrl;
        }
        return obj;
    },
    create(base) {
        return exports.WebcastResponse.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastResponse();
        message.messages = object.messages?.map((e) => exports.Message.fromPartial(e)) || [];
        message.cursor = object.cursor ?? "";
        message.fetchInterval = object.fetchInterval ?? 0;
        message.serverTimestamp = object.serverTimestamp ?? "0";
        message.internalExt = object.internalExt ?? "";
        message.fetchType = object.fetchType ?? 0;
        message.wsParams = object.wsParams?.map((e) => exports.WebsocketParam.fromPartial(e)) || [];
        message.heartbeatDuration = object.heartbeatDuration ?? 0;
        message.needAck = object.needAck ?? false;
        message.wsUrl = object.wsUrl ?? "";
        return message;
    },
};
function createBaseMessage() {
    return { type: "", binary: new Uint8Array(0) };
}
exports.Message = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.type !== "") {
            writer.uint32(10).string(message.type);
        }
        if (message.binary.length !== 0) {
            writer.uint32(18).bytes(message.binary);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.type = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.binary = reader.bytes();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? globalThis.String(object.type) : "",
            binary: isSet(object.binary) ? bytesFromBase64(object.binary) : new Uint8Array(0),
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.type !== "") {
            obj.type = message.type;
        }
        if (message.binary.length !== 0) {
            obj.binary = base64FromBytes(message.binary);
        }
        return obj;
    },
    create(base) {
        return exports.Message.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseMessage();
        message.type = object.type ?? "";
        message.binary = object.binary ?? new Uint8Array(0);
        return message;
    },
};
function createBaseWebsocketParam() {
    return { name: "", value: "" };
}
exports.WebsocketParam = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.name !== "") {
            writer.uint32(10).string(message.name);
        }
        if (message.value !== "") {
            writer.uint32(18).string(message.value);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebsocketParam();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.name = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.value = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            name: isSet(object.name) ? globalThis.String(object.name) : "",
            value: isSet(object.value) ? globalThis.String(object.value) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.name !== "") {
            obj.name = message.name;
        }
        if (message.value !== "") {
            obj.value = message.value;
        }
        return obj;
    },
    create(base) {
        return exports.WebsocketParam.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebsocketParam();
        message.name = object.name ?? "";
        message.value = object.value ?? "";
        return message;
    },
};
function createBaseWebcastControlMessage() {
    return { action: 0 };
}
exports.WebcastControlMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.action !== 0) {
            writer.uint32(16).int32(message.action);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastControlMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.action = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { action: isSet(object.action) ? controlActionFromJSON(object.action) : 0 };
    },
    toJSON(message) {
        const obj = {};
        if (message.action !== 0) {
            obj.action = controlActionToJSON(message.action);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastControlMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastControlMessage();
        message.action = object.action ?? 0;
        return message;
    },
};
function createBaseWebcastRoomUserSeqMessage() {
    return { topViewers: [], viewerCount: 0 };
}
exports.WebcastRoomUserSeqMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        for (const v of message.topViewers) {
            exports.TopUser.encode(v, writer.uint32(18).fork()).join();
        }
        if (message.viewerCount !== 0) {
            writer.uint32(24).int32(message.viewerCount);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastRoomUserSeqMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.topViewers.push(exports.TopUser.decode(reader, reader.uint32()));
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.viewerCount = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            topViewers: globalThis.Array.isArray(object?.topViewers)
                ? object.topViewers.map((e) => exports.TopUser.fromJSON(e))
                : [],
            viewerCount: isSet(object.viewerCount) ? globalThis.Number(object.viewerCount) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.topViewers?.length) {
            obj.topViewers = message.topViewers.map((e) => exports.TopUser.toJSON(e));
        }
        if (message.viewerCount !== 0) {
            obj.viewerCount = Math.round(message.viewerCount);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastRoomUserSeqMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastRoomUserSeqMessage();
        message.topViewers = object.topViewers?.map((e) => exports.TopUser.fromPartial(e)) || [];
        message.viewerCount = object.viewerCount ?? 0;
        return message;
    },
};
function createBaseTopUser() {
    return { coinCount: "0", user: undefined };
}
exports.TopUser = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.coinCount !== "0") {
            writer.uint32(8).uint64(message.coinCount);
        }
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTopUser();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.coinCount = reader.uint64().toString();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            coinCount: isSet(object.coinCount) ? globalThis.String(object.coinCount) : "0",
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.coinCount !== "0") {
            obj.coinCount = message.coinCount;
        }
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        return obj;
    },
    create(base) {
        return exports.TopUser.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseTopUser();
        message.coinCount = object.coinCount ?? "0";
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        return message;
    },
};
function createBaseWebcastChatMessage() {
    return { event: undefined, user: undefined, comment: "", emotes: [] };
}
exports.WebcastChatMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.event !== undefined) {
            exports.WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();
        }
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(18).fork()).join();
        }
        if (message.comment !== "") {
            writer.uint32(26).string(message.comment);
        }
        for (const v of message.emotes) {
            exports.WebcastSubEmote.encode(v, writer.uint32(106).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastChatMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.event = exports.WebcastMessageEvent.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.comment = reader.string();
                    continue;
                }
                case 13: {
                    if (tag !== 106) {
                        break;
                    }
                    message.emotes.push(exports.WebcastSubEmote.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            event: isSet(object.event) ? exports.WebcastMessageEvent.fromJSON(object.event) : undefined,
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
            comment: isSet(object.comment) ? globalThis.String(object.comment) : "",
            emotes: globalThis.Array.isArray(object?.emotes)
                ? object.emotes.map((e) => exports.WebcastSubEmote.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.event !== undefined) {
            obj.event = exports.WebcastMessageEvent.toJSON(message.event);
        }
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        if (message.comment !== "") {
            obj.comment = message.comment;
        }
        if (message.emotes?.length) {
            obj.emotes = message.emotes.map((e) => exports.WebcastSubEmote.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return exports.WebcastChatMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastChatMessage();
        message.event = (object.event !== undefined && object.event !== null)
            ? exports.WebcastMessageEvent.fromPartial(object.event)
            : undefined;
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        message.comment = object.comment ?? "";
        message.emotes = object.emotes?.map((e) => exports.WebcastSubEmote.fromPartial(e)) || [];
        return message;
    },
};
function createBaseWebcastEmoteChatMessage() {
    return { user: undefined, emote: undefined };
}
exports.WebcastEmoteChatMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(18).fork()).join();
        }
        if (message.emote !== undefined) {
            exports.EmoteDetails.encode(message.emote, writer.uint32(26).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastEmoteChatMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.emote = exports.EmoteDetails.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
            emote: isSet(object.emote) ? exports.EmoteDetails.fromJSON(object.emote) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        if (message.emote !== undefined) {
            obj.emote = exports.EmoteDetails.toJSON(message.emote);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastEmoteChatMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastEmoteChatMessage();
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        message.emote = (object.emote !== undefined && object.emote !== null)
            ? exports.EmoteDetails.fromPartial(object.emote)
            : undefined;
        return message;
    },
};
function createBaseWebcastSubEmote() {
    return { placeInComment: 0, emote: undefined };
}
exports.WebcastSubEmote = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.placeInComment !== 0) {
            writer.uint32(8).int32(message.placeInComment);
        }
        if (message.emote !== undefined) {
            exports.EmoteDetails.encode(message.emote, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastSubEmote();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.placeInComment = reader.int32();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.emote = exports.EmoteDetails.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            placeInComment: isSet(object.placeInComment) ? globalThis.Number(object.placeInComment) : 0,
            emote: isSet(object.emote) ? exports.EmoteDetails.fromJSON(object.emote) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.placeInComment !== 0) {
            obj.placeInComment = Math.round(message.placeInComment);
        }
        if (message.emote !== undefined) {
            obj.emote = exports.EmoteDetails.toJSON(message.emote);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastSubEmote.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastSubEmote();
        message.placeInComment = object.placeInComment ?? 0;
        message.emote = (object.emote !== undefined && object.emote !== null)
            ? exports.EmoteDetails.fromPartial(object.emote)
            : undefined;
        return message;
    },
};
function createBaseWebcastMemberMessage() {
    return { event: undefined, user: undefined, actionId: 0 };
}
exports.WebcastMemberMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.event !== undefined) {
            exports.WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();
        }
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(18).fork()).join();
        }
        if (message.actionId !== 0) {
            writer.uint32(80).int32(message.actionId);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastMemberMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.event = exports.WebcastMessageEvent.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
                case 10: {
                    if (tag !== 80) {
                        break;
                    }
                    message.actionId = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            event: isSet(object.event) ? exports.WebcastMessageEvent.fromJSON(object.event) : undefined,
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
            actionId: isSet(object.actionId) ? globalThis.Number(object.actionId) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.event !== undefined) {
            obj.event = exports.WebcastMessageEvent.toJSON(message.event);
        }
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        if (message.actionId !== 0) {
            obj.actionId = Math.round(message.actionId);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastMemberMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastMemberMessage();
        message.event = (object.event !== undefined && object.event !== null)
            ? exports.WebcastMessageEvent.fromPartial(object.event)
            : undefined;
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        message.actionId = object.actionId ?? 0;
        return message;
    },
};
function createBaseWebcastGiftMessage() {
    return {
        event: undefined,
        giftId: 0,
        repeatCount: 0,
        user: undefined,
        repeatEnd: 0,
        groupId: "0",
        giftDetails: undefined,
        monitorExtra: "",
        giftExtra: undefined,
    };
}
exports.WebcastGiftMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.event !== undefined) {
            exports.WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();
        }
        if (message.giftId !== 0) {
            writer.uint32(16).int32(message.giftId);
        }
        if (message.repeatCount !== 0) {
            writer.uint32(40).int32(message.repeatCount);
        }
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(58).fork()).join();
        }
        if (message.repeatEnd !== 0) {
            writer.uint32(72).int32(message.repeatEnd);
        }
        if (message.groupId !== "0") {
            writer.uint32(88).uint64(message.groupId);
        }
        if (message.giftDetails !== undefined) {
            exports.WebcastGiftMessageGiftDetails.encode(message.giftDetails, writer.uint32(122).fork()).join();
        }
        if (message.monitorExtra !== "") {
            writer.uint32(178).string(message.monitorExtra);
        }
        if (message.giftExtra !== undefined) {
            exports.WebcastGiftMessageGiftExtra.encode(message.giftExtra, writer.uint32(186).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastGiftMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.event = exports.WebcastMessageEvent.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.giftId = reader.int32();
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.repeatCount = reader.int32();
                    continue;
                }
                case 7: {
                    if (tag !== 58) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
                case 9: {
                    if (tag !== 72) {
                        break;
                    }
                    message.repeatEnd = reader.int32();
                    continue;
                }
                case 11: {
                    if (tag !== 88) {
                        break;
                    }
                    message.groupId = reader.uint64().toString();
                    continue;
                }
                case 15: {
                    if (tag !== 122) {
                        break;
                    }
                    message.giftDetails = exports.WebcastGiftMessageGiftDetails.decode(reader, reader.uint32());
                    continue;
                }
                case 22: {
                    if (tag !== 178) {
                        break;
                    }
                    message.monitorExtra = reader.string();
                    continue;
                }
                case 23: {
                    if (tag !== 186) {
                        break;
                    }
                    message.giftExtra = exports.WebcastGiftMessageGiftExtra.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            event: isSet(object.event) ? exports.WebcastMessageEvent.fromJSON(object.event) : undefined,
            giftId: isSet(object.giftId) ? globalThis.Number(object.giftId) : 0,
            repeatCount: isSet(object.repeatCount) ? globalThis.Number(object.repeatCount) : 0,
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
            repeatEnd: isSet(object.repeatEnd) ? globalThis.Number(object.repeatEnd) : 0,
            groupId: isSet(object.groupId) ? globalThis.String(object.groupId) : "0",
            giftDetails: isSet(object.giftDetails) ? exports.WebcastGiftMessageGiftDetails.fromJSON(object.giftDetails) : undefined,
            monitorExtra: isSet(object.monitorExtra) ? globalThis.String(object.monitorExtra) : "",
            giftExtra: isSet(object.giftExtra) ? exports.WebcastGiftMessageGiftExtra.fromJSON(object.giftExtra) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.event !== undefined) {
            obj.event = exports.WebcastMessageEvent.toJSON(message.event);
        }
        if (message.giftId !== 0) {
            obj.giftId = Math.round(message.giftId);
        }
        if (message.repeatCount !== 0) {
            obj.repeatCount = Math.round(message.repeatCount);
        }
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        if (message.repeatEnd !== 0) {
            obj.repeatEnd = Math.round(message.repeatEnd);
        }
        if (message.groupId !== "0") {
            obj.groupId = message.groupId;
        }
        if (message.giftDetails !== undefined) {
            obj.giftDetails = exports.WebcastGiftMessageGiftDetails.toJSON(message.giftDetails);
        }
        if (message.monitorExtra !== "") {
            obj.monitorExtra = message.monitorExtra;
        }
        if (message.giftExtra !== undefined) {
            obj.giftExtra = exports.WebcastGiftMessageGiftExtra.toJSON(message.giftExtra);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastGiftMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastGiftMessage();
        message.event = (object.event !== undefined && object.event !== null)
            ? exports.WebcastMessageEvent.fromPartial(object.event)
            : undefined;
        message.giftId = object.giftId ?? 0;
        message.repeatCount = object.repeatCount ?? 0;
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        message.repeatEnd = object.repeatEnd ?? 0;
        message.groupId = object.groupId ?? "0";
        message.giftDetails = (object.giftDetails !== undefined && object.giftDetails !== null)
            ? exports.WebcastGiftMessageGiftDetails.fromPartial(object.giftDetails)
            : undefined;
        message.monitorExtra = object.monitorExtra ?? "";
        message.giftExtra = (object.giftExtra !== undefined && object.giftExtra !== null)
            ? exports.WebcastGiftMessageGiftExtra.fromPartial(object.giftExtra)
            : undefined;
        return message;
    },
};
function createBaseWebcastGiftMessageGiftDetails() {
    return { giftImage: undefined, giftName: "", describe: "", giftType: 0, diamondCount: 0 };
}
exports.WebcastGiftMessageGiftDetails = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.giftImage !== undefined) {
            exports.WebcastGiftMessageGiftImage.encode(message.giftImage, writer.uint32(10).fork()).join();
        }
        if (message.giftName !== "") {
            writer.uint32(130).string(message.giftName);
        }
        if (message.describe !== "") {
            writer.uint32(18).string(message.describe);
        }
        if (message.giftType !== 0) {
            writer.uint32(88).int32(message.giftType);
        }
        if (message.diamondCount !== 0) {
            writer.uint32(96).int32(message.diamondCount);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastGiftMessageGiftDetails();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.giftImage = exports.WebcastGiftMessageGiftImage.decode(reader, reader.uint32());
                    continue;
                }
                case 16: {
                    if (tag !== 130) {
                        break;
                    }
                    message.giftName = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.describe = reader.string();
                    continue;
                }
                case 11: {
                    if (tag !== 88) {
                        break;
                    }
                    message.giftType = reader.int32();
                    continue;
                }
                case 12: {
                    if (tag !== 96) {
                        break;
                    }
                    message.diamondCount = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            giftImage: isSet(object.giftImage) ? exports.WebcastGiftMessageGiftImage.fromJSON(object.giftImage) : undefined,
            giftName: isSet(object.giftName) ? globalThis.String(object.giftName) : "",
            describe: isSet(object.describe) ? globalThis.String(object.describe) : "",
            giftType: isSet(object.giftType) ? globalThis.Number(object.giftType) : 0,
            diamondCount: isSet(object.diamondCount) ? globalThis.Number(object.diamondCount) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.giftImage !== undefined) {
            obj.giftImage = exports.WebcastGiftMessageGiftImage.toJSON(message.giftImage);
        }
        if (message.giftName !== "") {
            obj.giftName = message.giftName;
        }
        if (message.describe !== "") {
            obj.describe = message.describe;
        }
        if (message.giftType !== 0) {
            obj.giftType = Math.round(message.giftType);
        }
        if (message.diamondCount !== 0) {
            obj.diamondCount = Math.round(message.diamondCount);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastGiftMessageGiftDetails.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastGiftMessageGiftDetails();
        message.giftImage = (object.giftImage !== undefined && object.giftImage !== null)
            ? exports.WebcastGiftMessageGiftImage.fromPartial(object.giftImage)
            : undefined;
        message.giftName = object.giftName ?? "";
        message.describe = object.describe ?? "";
        message.giftType = object.giftType ?? 0;
        message.diamondCount = object.diamondCount ?? 0;
        return message;
    },
};
function createBaseWebcastGiftMessageGiftExtra() {
    return { timestamp: "0", receiverUserId: "0" };
}
exports.WebcastGiftMessageGiftExtra = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.timestamp !== "0") {
            writer.uint32(48).uint64(message.timestamp);
        }
        if (message.receiverUserId !== "0") {
            writer.uint32(64).uint64(message.receiverUserId);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastGiftMessageGiftExtra();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 6: {
                    if (tag !== 48) {
                        break;
                    }
                    message.timestamp = reader.uint64().toString();
                    continue;
                }
                case 8: {
                    if (tag !== 64) {
                        break;
                    }
                    message.receiverUserId = reader.uint64().toString();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : "0",
            receiverUserId: isSet(object.receiverUserId) ? globalThis.String(object.receiverUserId) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.timestamp !== "0") {
            obj.timestamp = message.timestamp;
        }
        if (message.receiverUserId !== "0") {
            obj.receiverUserId = message.receiverUserId;
        }
        return obj;
    },
    create(base) {
        return exports.WebcastGiftMessageGiftExtra.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastGiftMessageGiftExtra();
        message.timestamp = object.timestamp ?? "0";
        message.receiverUserId = object.receiverUserId ?? "0";
        return message;
    },
};
function createBaseWebcastGiftMessageGiftImage() {
    return { giftPictureUrl: "" };
}
exports.WebcastGiftMessageGiftImage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.giftPictureUrl !== "") {
            writer.uint32(10).string(message.giftPictureUrl);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastGiftMessageGiftImage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.giftPictureUrl = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { giftPictureUrl: isSet(object.giftPictureUrl) ? globalThis.String(object.giftPictureUrl) : "" };
    },
    toJSON(message) {
        const obj = {};
        if (message.giftPictureUrl !== "") {
            obj.giftPictureUrl = message.giftPictureUrl;
        }
        return obj;
    },
    create(base) {
        return exports.WebcastGiftMessageGiftImage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastGiftMessageGiftImage();
        message.giftPictureUrl = object.giftPictureUrl ?? "";
        return message;
    },
};
function createBaseWebcastLinkMicBattle() {
    return { battleUsers: [] };
}
exports.WebcastLinkMicBattle = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        for (const v of message.battleUsers) {
            exports.WebcastLinkMicBattleItems.encode(v, writer.uint32(82).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastLinkMicBattle();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 10: {
                    if (tag !== 82) {
                        break;
                    }
                    message.battleUsers.push(exports.WebcastLinkMicBattleItems.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            battleUsers: globalThis.Array.isArray(object?.battleUsers)
                ? object.battleUsers.map((e) => exports.WebcastLinkMicBattleItems.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.battleUsers?.length) {
            obj.battleUsers = message.battleUsers.map((e) => exports.WebcastLinkMicBattleItems.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return exports.WebcastLinkMicBattle.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastLinkMicBattle();
        message.battleUsers = object.battleUsers?.map((e) => exports.WebcastLinkMicBattleItems.fromPartial(e)) || [];
        return message;
    },
};
function createBaseWebcastLinkMicBattleItems() {
    return { battleGroup: undefined };
}
exports.WebcastLinkMicBattleItems = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.battleGroup !== undefined) {
            exports.WebcastLinkMicBattleGroup.encode(message.battleGroup, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastLinkMicBattleItems();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.battleGroup = exports.WebcastLinkMicBattleGroup.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            battleGroup: isSet(object.battleGroup) ? exports.WebcastLinkMicBattleGroup.fromJSON(object.battleGroup) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.battleGroup !== undefined) {
            obj.battleGroup = exports.WebcastLinkMicBattleGroup.toJSON(message.battleGroup);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastLinkMicBattleItems.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastLinkMicBattleItems();
        message.battleGroup = (object.battleGroup !== undefined && object.battleGroup !== null)
            ? exports.WebcastLinkMicBattleGroup.fromPartial(object.battleGroup)
            : undefined;
        return message;
    },
};
function createBaseWebcastLinkMicBattleGroup() {
    return { user: undefined };
}
exports.WebcastLinkMicBattleGroup = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.user !== undefined) {
            exports.LinkUser.encode(message.user, writer.uint32(10).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastLinkMicBattleGroup();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.user = exports.LinkUser.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { user: isSet(object.user) ? exports.LinkUser.fromJSON(object.user) : undefined };
    },
    toJSON(message) {
        const obj = {};
        if (message.user !== undefined) {
            obj.user = exports.LinkUser.toJSON(message.user);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastLinkMicBattleGroup.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastLinkMicBattleGroup();
        message.user = (object.user !== undefined && object.user !== null) ? exports.LinkUser.fromPartial(object.user) : undefined;
        return message;
    },
};
function createBaseWebcastLinkMicArmies() {
    return { battleItems: [], battleStatus: 0 };
}
exports.WebcastLinkMicArmies = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        for (const v of message.battleItems) {
            exports.WebcastLinkMicArmiesItems.encode(v, writer.uint32(26).fork()).join();
        }
        if (message.battleStatus !== 0) {
            writer.uint32(56).int32(message.battleStatus);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastLinkMicArmies();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.battleItems.push(exports.WebcastLinkMicArmiesItems.decode(reader, reader.uint32()));
                    continue;
                }
                case 7: {
                    if (tag !== 56) {
                        break;
                    }
                    message.battleStatus = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            battleItems: globalThis.Array.isArray(object?.battleItems)
                ? object.battleItems.map((e) => exports.WebcastLinkMicArmiesItems.fromJSON(e))
                : [],
            battleStatus: isSet(object.battleStatus) ? globalThis.Number(object.battleStatus) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.battleItems?.length) {
            obj.battleItems = message.battleItems.map((e) => exports.WebcastLinkMicArmiesItems.toJSON(e));
        }
        if (message.battleStatus !== 0) {
            obj.battleStatus = Math.round(message.battleStatus);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastLinkMicArmies.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastLinkMicArmies();
        message.battleItems = object.battleItems?.map((e) => exports.WebcastLinkMicArmiesItems.fromPartial(e)) || [];
        message.battleStatus = object.battleStatus ?? 0;
        return message;
    },
};
function createBaseWebcastLinkMicArmiesItems() {
    return { hostUserId: "0", battleGroups: [] };
}
exports.WebcastLinkMicArmiesItems = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.hostUserId !== "0") {
            writer.uint32(8).uint64(message.hostUserId);
        }
        for (const v of message.battleGroups) {
            exports.WebcastLinkMicArmiesGroup.encode(v, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastLinkMicArmiesItems();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.hostUserId = reader.uint64().toString();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.battleGroups.push(exports.WebcastLinkMicArmiesGroup.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            hostUserId: isSet(object.hostUserId) ? globalThis.String(object.hostUserId) : "0",
            battleGroups: globalThis.Array.isArray(object?.battleGroups)
                ? object.battleGroups.map((e) => exports.WebcastLinkMicArmiesGroup.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.hostUserId !== "0") {
            obj.hostUserId = message.hostUserId;
        }
        if (message.battleGroups?.length) {
            obj.battleGroups = message.battleGroups.map((e) => exports.WebcastLinkMicArmiesGroup.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return exports.WebcastLinkMicArmiesItems.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastLinkMicArmiesItems();
        message.hostUserId = object.hostUserId ?? "0";
        message.battleGroups = object.battleGroups?.map((e) => exports.WebcastLinkMicArmiesGroup.fromPartial(e)) || [];
        return message;
    },
};
function createBaseWebcastLinkMicArmiesGroup() {
    return { users: [], points: 0 };
}
exports.WebcastLinkMicArmiesGroup = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        for (const v of message.users) {
            exports.User.encode(v, writer.uint32(10).fork()).join();
        }
        if (message.points !== 0) {
            writer.uint32(16).int32(message.points);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastLinkMicArmiesGroup();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.users.push(exports.User.decode(reader, reader.uint32()));
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.points = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            users: globalThis.Array.isArray(object?.users) ? object.users.map((e) => exports.User.fromJSON(e)) : [],
            points: isSet(object.points) ? globalThis.Number(object.points) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.users?.length) {
            obj.users = message.users.map((e) => exports.User.toJSON(e));
        }
        if (message.points !== 0) {
            obj.points = Math.round(message.points);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastLinkMicArmiesGroup.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastLinkMicArmiesGroup();
        message.users = object.users?.map((e) => exports.User.fromPartial(e)) || [];
        message.points = object.points ?? 0;
        return message;
    },
};
function createBaseWebcastSocialMessage() {
    return { event: undefined, user: undefined };
}
exports.WebcastSocialMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.event !== undefined) {
            exports.WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();
        }
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastSocialMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.event = exports.WebcastMessageEvent.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            event: isSet(object.event) ? exports.WebcastMessageEvent.fromJSON(object.event) : undefined,
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.event !== undefined) {
            obj.event = exports.WebcastMessageEvent.toJSON(message.event);
        }
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastSocialMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastSocialMessage();
        message.event = (object.event !== undefined && object.event !== null)
            ? exports.WebcastMessageEvent.fromPartial(object.event)
            : undefined;
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        return message;
    },
};
function createBaseWebcastLikeMessage() {
    return { event: undefined, user: undefined, likeCount: 0, totalLikeCount: 0 };
}
exports.WebcastLikeMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.event !== undefined) {
            exports.WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();
        }
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(42).fork()).join();
        }
        if (message.likeCount !== 0) {
            writer.uint32(16).int32(message.likeCount);
        }
        if (message.totalLikeCount !== 0) {
            writer.uint32(24).int32(message.totalLikeCount);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastLikeMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.event = exports.WebcastMessageEvent.decode(reader, reader.uint32());
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.likeCount = reader.int32();
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.totalLikeCount = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            event: isSet(object.event) ? exports.WebcastMessageEvent.fromJSON(object.event) : undefined,
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
            likeCount: isSet(object.likeCount) ? globalThis.Number(object.likeCount) : 0,
            totalLikeCount: isSet(object.totalLikeCount) ? globalThis.Number(object.totalLikeCount) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.event !== undefined) {
            obj.event = exports.WebcastMessageEvent.toJSON(message.event);
        }
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        if (message.likeCount !== 0) {
            obj.likeCount = Math.round(message.likeCount);
        }
        if (message.totalLikeCount !== 0) {
            obj.totalLikeCount = Math.round(message.totalLikeCount);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastLikeMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastLikeMessage();
        message.event = (object.event !== undefined && object.event !== null)
            ? exports.WebcastMessageEvent.fromPartial(object.event)
            : undefined;
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        message.likeCount = object.likeCount ?? 0;
        message.totalLikeCount = object.totalLikeCount ?? 0;
        return message;
    },
};
function createBaseWebcastQuestionNewMessage() {
    return { questionDetails: undefined };
}
exports.WebcastQuestionNewMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.questionDetails !== undefined) {
            exports.QuestionDetails.encode(message.questionDetails, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastQuestionNewMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.questionDetails = exports.QuestionDetails.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            questionDetails: isSet(object.questionDetails) ? exports.QuestionDetails.fromJSON(object.questionDetails) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.questionDetails !== undefined) {
            obj.questionDetails = exports.QuestionDetails.toJSON(message.questionDetails);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastQuestionNewMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastQuestionNewMessage();
        message.questionDetails = (object.questionDetails !== undefined && object.questionDetails !== null)
            ? exports.QuestionDetails.fromPartial(object.questionDetails)
            : undefined;
        return message;
    },
};
function createBaseQuestionDetails() {
    return { questionText: "", user: undefined };
}
exports.QuestionDetails = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.questionText !== "") {
            writer.uint32(18).string(message.questionText);
        }
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(42).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseQuestionDetails();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.questionText = reader.string();
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            questionText: isSet(object.questionText) ? globalThis.String(object.questionText) : "",
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.questionText !== "") {
            obj.questionText = message.questionText;
        }
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        return obj;
    },
    create(base) {
        return exports.QuestionDetails.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseQuestionDetails();
        message.questionText = object.questionText ?? "";
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        return message;
    },
};
function createBaseWebcastMessageEvent() {
    return { msgId: "0", createTime: "0", eventDetails: undefined };
}
exports.WebcastMessageEvent = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.msgId !== "0") {
            writer.uint32(16).uint64(message.msgId);
        }
        if (message.createTime !== "0") {
            writer.uint32(32).uint64(message.createTime);
        }
        if (message.eventDetails !== undefined) {
            exports.WebcastMessageEventDetails.encode(message.eventDetails, writer.uint32(66).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastMessageEvent();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.msgId = reader.uint64().toString();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.createTime = reader.uint64().toString();
                    continue;
                }
                case 8: {
                    if (tag !== 66) {
                        break;
                    }
                    message.eventDetails = exports.WebcastMessageEventDetails.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            msgId: isSet(object.msgId) ? globalThis.String(object.msgId) : "0",
            createTime: isSet(object.createTime) ? globalThis.String(object.createTime) : "0",
            eventDetails: isSet(object.eventDetails) ? exports.WebcastMessageEventDetails.fromJSON(object.eventDetails) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.msgId !== "0") {
            obj.msgId = message.msgId;
        }
        if (message.createTime !== "0") {
            obj.createTime = message.createTime;
        }
        if (message.eventDetails !== undefined) {
            obj.eventDetails = exports.WebcastMessageEventDetails.toJSON(message.eventDetails);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastMessageEvent.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastMessageEvent();
        message.msgId = object.msgId ?? "0";
        message.createTime = object.createTime ?? "0";
        message.eventDetails = (object.eventDetails !== undefined && object.eventDetails !== null)
            ? exports.WebcastMessageEventDetails.fromPartial(object.eventDetails)
            : undefined;
        return message;
    },
};
function createBaseWebcastMessageEventDetails() {
    return { displayType: "", label: "" };
}
exports.WebcastMessageEventDetails = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.displayType !== "") {
            writer.uint32(10).string(message.displayType);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastMessageEventDetails();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.displayType = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.label = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            displayType: isSet(object.displayType) ? globalThis.String(object.displayType) : "",
            label: isSet(object.label) ? globalThis.String(object.label) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.displayType !== "") {
            obj.displayType = message.displayType;
        }
        if (message.label !== "") {
            obj.label = message.label;
        }
        return obj;
    },
    create(base) {
        return exports.WebcastMessageEventDetails.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastMessageEventDetails();
        message.displayType = object.displayType ?? "";
        message.label = object.label ?? "";
        return message;
    },
};
function createBaseWebcastLiveIntroMessage() {
    return { id: "0", description: "", user: undefined };
}
exports.WebcastLiveIntroMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.id !== "0") {
            writer.uint32(16).uint64(message.id);
        }
        if (message.description !== "") {
            writer.uint32(34).string(message.description);
        }
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(42).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastLiveIntroMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.id = reader.uint64().toString();
                    continue;
                }
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.description = reader.string();
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? globalThis.String(object.id) : "0",
            description: isSet(object.description) ? globalThis.String(object.description) : "",
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.id !== "0") {
            obj.id = message.id;
        }
        if (message.description !== "") {
            obj.description = message.description;
        }
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastLiveIntroMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastLiveIntroMessage();
        message.id = object.id ?? "0";
        message.description = object.description ?? "";
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        return message;
    },
};
function createBaseSystemMessage() {
    return { description: "" };
}
exports.SystemMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.description !== "") {
            writer.uint32(18).string(message.description);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseSystemMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.description = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { description: isSet(object.description) ? globalThis.String(object.description) : "" };
    },
    toJSON(message) {
        const obj = {};
        if (message.description !== "") {
            obj.description = message.description;
        }
        return obj;
    },
    create(base) {
        return exports.SystemMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseSystemMessage();
        message.description = object.description ?? "";
        return message;
    },
};
function createBaseWebcastInRoomBannerMessage() {
    return { data: "" };
}
exports.WebcastInRoomBannerMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.data !== "") {
            writer.uint32(18).string(message.data);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastInRoomBannerMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.data = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { data: isSet(object.data) ? globalThis.String(object.data) : "" };
    },
    toJSON(message) {
        const obj = {};
        if (message.data !== "") {
            obj.data = message.data;
        }
        return obj;
    },
    create(base) {
        return exports.WebcastInRoomBannerMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastInRoomBannerMessage();
        message.data = object.data ?? "";
        return message;
    },
};
function createBaseRankItem() {
    return { colour: "", id: "0" };
}
exports.RankItem = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.colour !== "") {
            writer.uint32(10).string(message.colour);
        }
        if (message.id !== "0") {
            writer.uint32(32).uint64(message.id);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRankItem();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.colour = reader.string();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.id = reader.uint64().toString();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            colour: isSet(object.colour) ? globalThis.String(object.colour) : "",
            id: isSet(object.id) ? globalThis.String(object.id) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.colour !== "") {
            obj.colour = message.colour;
        }
        if (message.id !== "0") {
            obj.id = message.id;
        }
        return obj;
    },
    create(base) {
        return exports.RankItem.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRankItem();
        message.colour = object.colour ?? "";
        message.id = object.id ?? "0";
        return message;
    },
};
function createBaseWeeklyRanking() {
    return { type: "", label: "", rank: undefined };
}
exports.WeeklyRanking = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.type !== "") {
            writer.uint32(10).string(message.type);
        }
        if (message.label !== "") {
            writer.uint32(18).string(message.label);
        }
        if (message.rank !== undefined) {
            exports.RankItem.encode(message.rank, writer.uint32(26).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWeeklyRanking();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.type = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.label = reader.string();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.rank = exports.RankItem.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? globalThis.String(object.type) : "",
            label: isSet(object.label) ? globalThis.String(object.label) : "",
            rank: isSet(object.rank) ? exports.RankItem.fromJSON(object.rank) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.type !== "") {
            obj.type = message.type;
        }
        if (message.label !== "") {
            obj.label = message.label;
        }
        if (message.rank !== undefined) {
            obj.rank = exports.RankItem.toJSON(message.rank);
        }
        return obj;
    },
    create(base) {
        return exports.WeeklyRanking.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWeeklyRanking();
        message.type = object.type ?? "";
        message.label = object.label ?? "";
        message.rank = (object.rank !== undefined && object.rank !== null) ? exports.RankItem.fromPartial(object.rank) : undefined;
        return message;
    },
};
function createBaseRankContainer() {
    return { rankings: undefined };
}
exports.RankContainer = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.rankings !== undefined) {
            exports.WeeklyRanking.encode(message.rankings, writer.uint32(34).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseRankContainer();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.rankings = exports.WeeklyRanking.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { rankings: isSet(object.rankings) ? exports.WeeklyRanking.fromJSON(object.rankings) : undefined };
    },
    toJSON(message) {
        const obj = {};
        if (message.rankings !== undefined) {
            obj.rankings = exports.WeeklyRanking.toJSON(message.rankings);
        }
        return obj;
    },
    create(base) {
        return exports.RankContainer.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseRankContainer();
        message.rankings = (object.rankings !== undefined && object.rankings !== null)
            ? exports.WeeklyRanking.fromPartial(object.rankings)
            : undefined;
        return message;
    },
};
function createBaseWebcastHourlyRankMessage() {
    return { data: undefined };
}
exports.WebcastHourlyRankMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.data !== undefined) {
            exports.RankContainer.encode(message.data, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastHourlyRankMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.data = exports.RankContainer.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { data: isSet(object.data) ? exports.RankContainer.fromJSON(object.data) : undefined };
    },
    toJSON(message) {
        const obj = {};
        if (message.data !== undefined) {
            obj.data = exports.RankContainer.toJSON(message.data);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastHourlyRankMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastHourlyRankMessage();
        message.data = (object.data !== undefined && object.data !== null)
            ? exports.RankContainer.fromPartial(object.data)
            : undefined;
        return message;
    },
};
function createBaseEmoteDetails() {
    return { emoteId: "", image: undefined };
}
exports.EmoteDetails = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.emoteId !== "") {
            writer.uint32(10).string(message.emoteId);
        }
        if (message.image !== undefined) {
            exports.EmoteImage.encode(message.image, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEmoteDetails();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.emoteId = reader.string();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.image = exports.EmoteImage.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            emoteId: isSet(object.emoteId) ? globalThis.String(object.emoteId) : "",
            image: isSet(object.image) ? exports.EmoteImage.fromJSON(object.image) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.emoteId !== "") {
            obj.emoteId = message.emoteId;
        }
        if (message.image !== undefined) {
            obj.image = exports.EmoteImage.toJSON(message.image);
        }
        return obj;
    },
    create(base) {
        return exports.EmoteDetails.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEmoteDetails();
        message.emoteId = object.emoteId ?? "";
        message.image = (object.image !== undefined && object.image !== null)
            ? exports.EmoteImage.fromPartial(object.image)
            : undefined;
        return message;
    },
};
function createBaseEmoteImage() {
    return { imageUrl: "" };
}
exports.EmoteImage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.imageUrl !== "") {
            writer.uint32(10).string(message.imageUrl);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseEmoteImage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.imageUrl = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { imageUrl: isSet(object.imageUrl) ? globalThis.String(object.imageUrl) : "" };
    },
    toJSON(message) {
        const obj = {};
        if (message.imageUrl !== "") {
            obj.imageUrl = message.imageUrl;
        }
        return obj;
    },
    create(base) {
        return exports.EmoteImage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseEmoteImage();
        message.imageUrl = object.imageUrl ?? "";
        return message;
    },
};
function createBaseWebcastEnvelopeMessage() {
    return { treasureBoxData: undefined, treasureBoxUser: undefined };
}
exports.WebcastEnvelopeMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.treasureBoxData !== undefined) {
            exports.TreasureBoxData.encode(message.treasureBoxData, writer.uint32(18).fork()).join();
        }
        if (message.treasureBoxUser !== undefined) {
            exports.TreasureBoxUser.encode(message.treasureBoxUser, writer.uint32(10).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastEnvelopeMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.treasureBoxData = exports.TreasureBoxData.decode(reader, reader.uint32());
                    continue;
                }
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.treasureBoxUser = exports.TreasureBoxUser.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            treasureBoxData: isSet(object.treasureBoxData) ? exports.TreasureBoxData.fromJSON(object.treasureBoxData) : undefined,
            treasureBoxUser: isSet(object.treasureBoxUser) ? exports.TreasureBoxUser.fromJSON(object.treasureBoxUser) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.treasureBoxData !== undefined) {
            obj.treasureBoxData = exports.TreasureBoxData.toJSON(message.treasureBoxData);
        }
        if (message.treasureBoxUser !== undefined) {
            obj.treasureBoxUser = exports.TreasureBoxUser.toJSON(message.treasureBoxUser);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastEnvelopeMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastEnvelopeMessage();
        message.treasureBoxData = (object.treasureBoxData !== undefined && object.treasureBoxData !== null)
            ? exports.TreasureBoxData.fromPartial(object.treasureBoxData)
            : undefined;
        message.treasureBoxUser = (object.treasureBoxUser !== undefined && object.treasureBoxUser !== null)
            ? exports.TreasureBoxUser.fromPartial(object.treasureBoxUser)
            : undefined;
        return message;
    },
};
function createBaseTreasureBoxUser() {
    return { user2: undefined };
}
exports.TreasureBoxUser = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.user2 !== undefined) {
            exports.TreasureBoxUser2.encode(message.user2, writer.uint32(66).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTreasureBoxUser();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 8: {
                    if (tag !== 66) {
                        break;
                    }
                    message.user2 = exports.TreasureBoxUser2.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { user2: isSet(object.user2) ? exports.TreasureBoxUser2.fromJSON(object.user2) : undefined };
    },
    toJSON(message) {
        const obj = {};
        if (message.user2 !== undefined) {
            obj.user2 = exports.TreasureBoxUser2.toJSON(message.user2);
        }
        return obj;
    },
    create(base) {
        return exports.TreasureBoxUser.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseTreasureBoxUser();
        message.user2 = (object.user2 !== undefined && object.user2 !== null)
            ? exports.TreasureBoxUser2.fromPartial(object.user2)
            : undefined;
        return message;
    },
};
function createBaseTreasureBoxUser2() {
    return { user3: [] };
}
exports.TreasureBoxUser2 = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        for (const v of message.user3) {
            exports.TreasureBoxUser3.encode(v, writer.uint32(34).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTreasureBoxUser2();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.user3.push(exports.TreasureBoxUser3.decode(reader, reader.uint32()));
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            user3: globalThis.Array.isArray(object?.user3) ? object.user3.map((e) => exports.TreasureBoxUser3.fromJSON(e)) : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.user3?.length) {
            obj.user3 = message.user3.map((e) => exports.TreasureBoxUser3.toJSON(e));
        }
        return obj;
    },
    create(base) {
        return exports.TreasureBoxUser2.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseTreasureBoxUser2();
        message.user3 = object.user3?.map((e) => exports.TreasureBoxUser3.fromPartial(e)) || [];
        return message;
    },
};
function createBaseTreasureBoxUser3() {
    return { user4: undefined };
}
exports.TreasureBoxUser3 = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.user4 !== undefined) {
            exports.TreasureBoxUser4.encode(message.user4, writer.uint32(170).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTreasureBoxUser3();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 21: {
                    if (tag !== 170) {
                        break;
                    }
                    message.user4 = exports.TreasureBoxUser4.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { user4: isSet(object.user4) ? exports.TreasureBoxUser4.fromJSON(object.user4) : undefined };
    },
    toJSON(message) {
        const obj = {};
        if (message.user4 !== undefined) {
            obj.user4 = exports.TreasureBoxUser4.toJSON(message.user4);
        }
        return obj;
    },
    create(base) {
        return exports.TreasureBoxUser3.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseTreasureBoxUser3();
        message.user4 = (object.user4 !== undefined && object.user4 !== null)
            ? exports.TreasureBoxUser4.fromPartial(object.user4)
            : undefined;
        return message;
    },
};
function createBaseTreasureBoxUser4() {
    return { user: undefined };
}
exports.TreasureBoxUser4 = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(10).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTreasureBoxUser4();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined };
    },
    toJSON(message) {
        const obj = {};
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        return obj;
    },
    create(base) {
        return exports.TreasureBoxUser4.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseTreasureBoxUser4();
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        return message;
    },
};
function createBaseTreasureBoxData() {
    return { coins: 0, canOpen: 0, timestamp: "0" };
}
exports.TreasureBoxData = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.coins !== 0) {
            writer.uint32(40).uint32(message.coins);
        }
        if (message.canOpen !== 0) {
            writer.uint32(48).uint32(message.canOpen);
        }
        if (message.timestamp !== "0") {
            writer.uint32(56).uint64(message.timestamp);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseTreasureBoxData();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.coins = reader.uint32();
                    continue;
                }
                case 6: {
                    if (tag !== 48) {
                        break;
                    }
                    message.canOpen = reader.uint32();
                    continue;
                }
                case 7: {
                    if (tag !== 56) {
                        break;
                    }
                    message.timestamp = reader.uint64().toString();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            coins: isSet(object.coins) ? globalThis.Number(object.coins) : 0,
            canOpen: isSet(object.canOpen) ? globalThis.Number(object.canOpen) : 0,
            timestamp: isSet(object.timestamp) ? globalThis.String(object.timestamp) : "0",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.coins !== 0) {
            obj.coins = Math.round(message.coins);
        }
        if (message.canOpen !== 0) {
            obj.canOpen = Math.round(message.canOpen);
        }
        if (message.timestamp !== "0") {
            obj.timestamp = message.timestamp;
        }
        return obj;
    },
    create(base) {
        return exports.TreasureBoxData.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseTreasureBoxData();
        message.coins = object.coins ?? 0;
        message.canOpen = object.canOpen ?? 0;
        message.timestamp = object.timestamp ?? "0";
        return message;
    },
};
function createBaseWebcastSubNotifyMessage() {
    return {
        event: undefined,
        user: undefined,
        exhibitionType: 0,
        subMonth: 0,
        subscribeType: 0,
        oldSubscribeStatus: 0,
        subscribingStatus: 0,
    };
}
exports.WebcastSubNotifyMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.event !== undefined) {
            exports.WebcastMessageEvent.encode(message.event, writer.uint32(10).fork()).join();
        }
        if (message.user !== undefined) {
            exports.User.encode(message.user, writer.uint32(18).fork()).join();
        }
        if (message.exhibitionType !== 0) {
            writer.uint32(24).int32(message.exhibitionType);
        }
        if (message.subMonth !== 0) {
            writer.uint32(32).int32(message.subMonth);
        }
        if (message.subscribeType !== 0) {
            writer.uint32(40).int32(message.subscribeType);
        }
        if (message.oldSubscribeStatus !== 0) {
            writer.uint32(48).int32(message.oldSubscribeStatus);
        }
        if (message.subscribingStatus !== 0) {
            writer.uint32(64).int32(message.subscribingStatus);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastSubNotifyMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.event = exports.WebcastMessageEvent.decode(reader, reader.uint32());
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.user = exports.User.decode(reader, reader.uint32());
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.exhibitionType = reader.int32();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.subMonth = reader.int32();
                    continue;
                }
                case 5: {
                    if (tag !== 40) {
                        break;
                    }
                    message.subscribeType = reader.int32();
                    continue;
                }
                case 6: {
                    if (tag !== 48) {
                        break;
                    }
                    message.oldSubscribeStatus = reader.int32();
                    continue;
                }
                case 8: {
                    if (tag !== 64) {
                        break;
                    }
                    message.subscribingStatus = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            event: isSet(object.event) ? exports.WebcastMessageEvent.fromJSON(object.event) : undefined,
            user: isSet(object.user) ? exports.User.fromJSON(object.user) : undefined,
            exhibitionType: isSet(object.exhibitionType) ? globalThis.Number(object.exhibitionType) : 0,
            subMonth: isSet(object.subMonth) ? globalThis.Number(object.subMonth) : 0,
            subscribeType: isSet(object.subscribeType) ? globalThis.Number(object.subscribeType) : 0,
            oldSubscribeStatus: isSet(object.oldSubscribeStatus) ? globalThis.Number(object.oldSubscribeStatus) : 0,
            subscribingStatus: isSet(object.subscribingStatus) ? globalThis.Number(object.subscribingStatus) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.event !== undefined) {
            obj.event = exports.WebcastMessageEvent.toJSON(message.event);
        }
        if (message.user !== undefined) {
            obj.user = exports.User.toJSON(message.user);
        }
        if (message.exhibitionType !== 0) {
            obj.exhibitionType = Math.round(message.exhibitionType);
        }
        if (message.subMonth !== 0) {
            obj.subMonth = Math.round(message.subMonth);
        }
        if (message.subscribeType !== 0) {
            obj.subscribeType = Math.round(message.subscribeType);
        }
        if (message.oldSubscribeStatus !== 0) {
            obj.oldSubscribeStatus = Math.round(message.oldSubscribeStatus);
        }
        if (message.subscribingStatus !== 0) {
            obj.subscribingStatus = Math.round(message.subscribingStatus);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastSubNotifyMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastSubNotifyMessage();
        message.event = (object.event !== undefined && object.event !== null)
            ? exports.WebcastMessageEvent.fromPartial(object.event)
            : undefined;
        message.user = (object.user !== undefined && object.user !== null) ? exports.User.fromPartial(object.user) : undefined;
        message.exhibitionType = object.exhibitionType ?? 0;
        message.subMonth = object.subMonth ?? 0;
        message.subscribeType = object.subscribeType ?? 0;
        message.oldSubscribeStatus = object.oldSubscribeStatus ?? 0;
        message.subscribingStatus = object.subscribingStatus ?? 0;
        return message;
    },
};
function createBaseUser() {
    return {
        userId: "0",
        nickname: "",
        profilePicture: undefined,
        uniqueId: "",
        secUid: "",
        badges: [],
        createTime: "0",
        bioDescription: "",
        followInfo: undefined,
    };
}
exports.User = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.userId !== "0") {
            writer.uint32(8).uint64(message.userId);
        }
        if (message.nickname !== "") {
            writer.uint32(26).string(message.nickname);
        }
        if (message.profilePicture !== undefined) {
            exports.ProfilePicture.encode(message.profilePicture, writer.uint32(74).fork()).join();
        }
        if (message.uniqueId !== "") {
            writer.uint32(306).string(message.uniqueId);
        }
        if (message.secUid !== "") {
            writer.uint32(370).string(message.secUid);
        }
        for (const v of message.badges) {
            exports.UserBadgesAttributes.encode(v, writer.uint32(514).fork()).join();
        }
        if (message.createTime !== "0") {
            writer.uint32(128).uint64(message.createTime);
        }
        if (message.bioDescription !== "") {
            writer.uint32(42).string(message.bioDescription);
        }
        if (message.followInfo !== undefined) {
            exports.FollowInfo.encode(message.followInfo, writer.uint32(178).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseUser();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.userId = reader.uint64().toString();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.nickname = reader.string();
                    continue;
                }
                case 9: {
                    if (tag !== 74) {
                        break;
                    }
                    message.profilePicture = exports.ProfilePicture.decode(reader, reader.uint32());
                    continue;
                }
                case 38: {
                    if (tag !== 306) {
                        break;
                    }
                    message.uniqueId = reader.string();
                    continue;
                }
                case 46: {
                    if (tag !== 370) {
                        break;
                    }
                    message.secUid = reader.string();
                    continue;
                }
                case 64: {
                    if (tag !== 514) {
                        break;
                    }
                    message.badges.push(exports.UserBadgesAttributes.decode(reader, reader.uint32()));
                    continue;
                }
                case 16: {
                    if (tag !== 128) {
                        break;
                    }
                    message.createTime = reader.uint64().toString();
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.bioDescription = reader.string();
                    continue;
                }
                case 22: {
                    if (tag !== 178) {
                        break;
                    }
                    message.followInfo = exports.FollowInfo.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            userId: isSet(object.userId) ? globalThis.String(object.userId) : "0",
            nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : "",
            profilePicture: isSet(object.profilePicture) ? exports.ProfilePicture.fromJSON(object.profilePicture) : undefined,
            uniqueId: isSet(object.uniqueId) ? globalThis.String(object.uniqueId) : "",
            secUid: isSet(object.secUid) ? globalThis.String(object.secUid) : "",
            badges: globalThis.Array.isArray(object?.badges)
                ? object.badges.map((e) => exports.UserBadgesAttributes.fromJSON(e))
                : [],
            createTime: isSet(object.createTime) ? globalThis.String(object.createTime) : "0",
            bioDescription: isSet(object.bioDescription) ? globalThis.String(object.bioDescription) : "",
            followInfo: isSet(object.followInfo) ? exports.FollowInfo.fromJSON(object.followInfo) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.userId !== "0") {
            obj.userId = message.userId;
        }
        if (message.nickname !== "") {
            obj.nickname = message.nickname;
        }
        if (message.profilePicture !== undefined) {
            obj.profilePicture = exports.ProfilePicture.toJSON(message.profilePicture);
        }
        if (message.uniqueId !== "") {
            obj.uniqueId = message.uniqueId;
        }
        if (message.secUid !== "") {
            obj.secUid = message.secUid;
        }
        if (message.badges?.length) {
            obj.badges = message.badges.map((e) => exports.UserBadgesAttributes.toJSON(e));
        }
        if (message.createTime !== "0") {
            obj.createTime = message.createTime;
        }
        if (message.bioDescription !== "") {
            obj.bioDescription = message.bioDescription;
        }
        if (message.followInfo !== undefined) {
            obj.followInfo = exports.FollowInfo.toJSON(message.followInfo);
        }
        return obj;
    },
    create(base) {
        return exports.User.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseUser();
        message.userId = object.userId ?? "0";
        message.nickname = object.nickname ?? "";
        message.profilePicture = (object.profilePicture !== undefined && object.profilePicture !== null)
            ? exports.ProfilePicture.fromPartial(object.profilePicture)
            : undefined;
        message.uniqueId = object.uniqueId ?? "";
        message.secUid = object.secUid ?? "";
        message.badges = object.badges?.map((e) => exports.UserBadgesAttributes.fromPartial(e)) || [];
        message.createTime = object.createTime ?? "0";
        message.bioDescription = object.bioDescription ?? "";
        message.followInfo = (object.followInfo !== undefined && object.followInfo !== null)
            ? exports.FollowInfo.fromPartial(object.followInfo)
            : undefined;
        return message;
    },
};
function createBaseFollowInfo() {
    return { followingCount: 0, followerCount: 0, followStatus: 0, pushStatus: 0 };
}
exports.FollowInfo = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.followingCount !== 0) {
            writer.uint32(8).int32(message.followingCount);
        }
        if (message.followerCount !== 0) {
            writer.uint32(16).int32(message.followerCount);
        }
        if (message.followStatus !== 0) {
            writer.uint32(24).int32(message.followStatus);
        }
        if (message.pushStatus !== 0) {
            writer.uint32(32).int32(message.pushStatus);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseFollowInfo();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.followingCount = reader.int32();
                    continue;
                }
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.followerCount = reader.int32();
                    continue;
                }
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.followStatus = reader.int32();
                    continue;
                }
                case 4: {
                    if (tag !== 32) {
                        break;
                    }
                    message.pushStatus = reader.int32();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            followingCount: isSet(object.followingCount) ? globalThis.Number(object.followingCount) : 0,
            followerCount: isSet(object.followerCount) ? globalThis.Number(object.followerCount) : 0,
            followStatus: isSet(object.followStatus) ? globalThis.Number(object.followStatus) : 0,
            pushStatus: isSet(object.pushStatus) ? globalThis.Number(object.pushStatus) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.followingCount !== 0) {
            obj.followingCount = Math.round(message.followingCount);
        }
        if (message.followerCount !== 0) {
            obj.followerCount = Math.round(message.followerCount);
        }
        if (message.followStatus !== 0) {
            obj.followStatus = Math.round(message.followStatus);
        }
        if (message.pushStatus !== 0) {
            obj.pushStatus = Math.round(message.pushStatus);
        }
        return obj;
    },
    create(base) {
        return exports.FollowInfo.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseFollowInfo();
        message.followingCount = object.followingCount ?? 0;
        message.followerCount = object.followerCount ?? 0;
        message.followStatus = object.followStatus ?? 0;
        message.pushStatus = object.pushStatus ?? 0;
        return message;
    },
};
function createBaseLinkUser() {
    return { userId: "0", nickname: "", profilePicture: undefined, uniqueId: "" };
}
exports.LinkUser = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.userId !== "0") {
            writer.uint32(8).uint64(message.userId);
        }
        if (message.nickname !== "") {
            writer.uint32(18).string(message.nickname);
        }
        if (message.profilePicture !== undefined) {
            exports.ProfilePicture.encode(message.profilePicture, writer.uint32(26).fork()).join();
        }
        if (message.uniqueId !== "") {
            writer.uint32(34).string(message.uniqueId);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseLinkUser();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.userId = reader.uint64().toString();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.nickname = reader.string();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.profilePicture = exports.ProfilePicture.decode(reader, reader.uint32());
                    continue;
                }
                case 4: {
                    if (tag !== 34) {
                        break;
                    }
                    message.uniqueId = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            userId: isSet(object.userId) ? globalThis.String(object.userId) : "0",
            nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : "",
            profilePicture: isSet(object.profilePicture) ? exports.ProfilePicture.fromJSON(object.profilePicture) : undefined,
            uniqueId: isSet(object.uniqueId) ? globalThis.String(object.uniqueId) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.userId !== "0") {
            obj.userId = message.userId;
        }
        if (message.nickname !== "") {
            obj.nickname = message.nickname;
        }
        if (message.profilePicture !== undefined) {
            obj.profilePicture = exports.ProfilePicture.toJSON(message.profilePicture);
        }
        if (message.uniqueId !== "") {
            obj.uniqueId = message.uniqueId;
        }
        return obj;
    },
    create(base) {
        return exports.LinkUser.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseLinkUser();
        message.userId = object.userId ?? "0";
        message.nickname = object.nickname ?? "";
        message.profilePicture = (object.profilePicture !== undefined && object.profilePicture !== null)
            ? exports.ProfilePicture.fromPartial(object.profilePicture)
            : undefined;
        message.uniqueId = object.uniqueId ?? "";
        return message;
    },
};
function createBaseProfilePicture() {
    return { urls: [] };
}
exports.ProfilePicture = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        for (const v of message.urls) {
            writer.uint32(10).string(v);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseProfilePicture();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.urls.push(reader.string());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { urls: globalThis.Array.isArray(object?.urls) ? object.urls.map((e) => globalThis.String(e)) : [] };
    },
    toJSON(message) {
        const obj = {};
        if (message.urls?.length) {
            obj.urls = message.urls;
        }
        return obj;
    },
    create(base) {
        return exports.ProfilePicture.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseProfilePicture();
        message.urls = object.urls?.map((e) => e) || [];
        return message;
    },
};
function createBaseUserBadgesAttributes() {
    return { badgeSceneType: 0, imageBadges: [], badges: [], privilegeLogExtra: undefined };
}
exports.UserBadgesAttributes = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.badgeSceneType !== 0) {
            writer.uint32(24).int32(message.badgeSceneType);
        }
        for (const v of message.imageBadges) {
            exports.UserImageBadge.encode(v, writer.uint32(162).fork()).join();
        }
        for (const v of message.badges) {
            exports.UserBadge.encode(v, writer.uint32(170).fork()).join();
        }
        if (message.privilegeLogExtra !== undefined) {
            exports.PrivilegeLogExtra.encode(message.privilegeLogExtra, writer.uint32(98).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseUserBadgesAttributes();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 3: {
                    if (tag !== 24) {
                        break;
                    }
                    message.badgeSceneType = reader.int32();
                    continue;
                }
                case 20: {
                    if (tag !== 162) {
                        break;
                    }
                    message.imageBadges.push(exports.UserImageBadge.decode(reader, reader.uint32()));
                    continue;
                }
                case 21: {
                    if (tag !== 170) {
                        break;
                    }
                    message.badges.push(exports.UserBadge.decode(reader, reader.uint32()));
                    continue;
                }
                case 12: {
                    if (tag !== 98) {
                        break;
                    }
                    message.privilegeLogExtra = exports.PrivilegeLogExtra.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            badgeSceneType: isSet(object.badgeSceneType) ? globalThis.Number(object.badgeSceneType) : 0,
            imageBadges: globalThis.Array.isArray(object?.imageBadges)
                ? object.imageBadges.map((e) => exports.UserImageBadge.fromJSON(e))
                : [],
            badges: globalThis.Array.isArray(object?.badges) ? object.badges.map((e) => exports.UserBadge.fromJSON(e)) : [],
            privilegeLogExtra: isSet(object.privilegeLogExtra)
                ? exports.PrivilegeLogExtra.fromJSON(object.privilegeLogExtra)
                : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.badgeSceneType !== 0) {
            obj.badgeSceneType = Math.round(message.badgeSceneType);
        }
        if (message.imageBadges?.length) {
            obj.imageBadges = message.imageBadges.map((e) => exports.UserImageBadge.toJSON(e));
        }
        if (message.badges?.length) {
            obj.badges = message.badges.map((e) => exports.UserBadge.toJSON(e));
        }
        if (message.privilegeLogExtra !== undefined) {
            obj.privilegeLogExtra = exports.PrivilegeLogExtra.toJSON(message.privilegeLogExtra);
        }
        return obj;
    },
    create(base) {
        return exports.UserBadgesAttributes.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseUserBadgesAttributes();
        message.badgeSceneType = object.badgeSceneType ?? 0;
        message.imageBadges = object.imageBadges?.map((e) => exports.UserImageBadge.fromPartial(e)) || [];
        message.badges = object.badges?.map((e) => exports.UserBadge.fromPartial(e)) || [];
        message.privilegeLogExtra = (object.privilegeLogExtra !== undefined && object.privilegeLogExtra !== null)
            ? exports.PrivilegeLogExtra.fromPartial(object.privilegeLogExtra)
            : undefined;
        return message;
    },
};
function createBasePrivilegeLogExtra() {
    return { privilegeId: "", level: "" };
}
exports.PrivilegeLogExtra = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.privilegeId !== "") {
            writer.uint32(18).string(message.privilegeId);
        }
        if (message.level !== "") {
            writer.uint32(42).string(message.level);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBasePrivilegeLogExtra();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.privilegeId = reader.string();
                    continue;
                }
                case 5: {
                    if (tag !== 42) {
                        break;
                    }
                    message.level = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            privilegeId: isSet(object.privilegeId) ? globalThis.String(object.privilegeId) : "",
            level: isSet(object.level) ? globalThis.String(object.level) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.privilegeId !== "") {
            obj.privilegeId = message.privilegeId;
        }
        if (message.level !== "") {
            obj.level = message.level;
        }
        return obj;
    },
    create(base) {
        return exports.PrivilegeLogExtra.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBasePrivilegeLogExtra();
        message.privilegeId = object.privilegeId ?? "";
        message.level = object.level ?? "";
        return message;
    },
};
function createBaseUserBadge() {
    return { type: "", name: "" };
}
exports.UserBadge = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.type !== "") {
            writer.uint32(18).string(message.type);
        }
        if (message.name !== "") {
            writer.uint32(26).string(message.name);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseUserBadge();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.type = reader.string();
                    continue;
                }
                case 3: {
                    if (tag !== 26) {
                        break;
                    }
                    message.name = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            type: isSet(object.type) ? globalThis.String(object.type) : "",
            name: isSet(object.name) ? globalThis.String(object.name) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.type !== "") {
            obj.type = message.type;
        }
        if (message.name !== "") {
            obj.name = message.name;
        }
        return obj;
    },
    create(base) {
        return exports.UserBadge.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseUserBadge();
        message.type = object.type ?? "";
        message.name = object.name ?? "";
        return message;
    },
};
function createBaseUserImageBadge() {
    return { displayType: 0, image: undefined };
}
exports.UserImageBadge = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.displayType !== 0) {
            writer.uint32(8).int32(message.displayType);
        }
        if (message.image !== undefined) {
            exports.UserImageBadgeImage.encode(message.image, writer.uint32(18).fork()).join();
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseUserImageBadge();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 8) {
                        break;
                    }
                    message.displayType = reader.int32();
                    continue;
                }
                case 2: {
                    if (tag !== 18) {
                        break;
                    }
                    message.image = exports.UserImageBadgeImage.decode(reader, reader.uint32());
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            displayType: isSet(object.displayType) ? globalThis.Number(object.displayType) : 0,
            image: isSet(object.image) ? exports.UserImageBadgeImage.fromJSON(object.image) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.displayType !== 0) {
            obj.displayType = Math.round(message.displayType);
        }
        if (message.image !== undefined) {
            obj.image = exports.UserImageBadgeImage.toJSON(message.image);
        }
        return obj;
    },
    create(base) {
        return exports.UserImageBadge.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseUserImageBadge();
        message.displayType = object.displayType ?? 0;
        message.image = (object.image !== undefined && object.image !== null)
            ? exports.UserImageBadgeImage.fromPartial(object.image)
            : undefined;
        return message;
    },
};
function createBaseUserImageBadgeImage() {
    return { url: "" };
}
exports.UserImageBadgeImage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.url !== "") {
            writer.uint32(10).string(message.url);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseUserImageBadgeImage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 1: {
                    if (tag !== 10) {
                        break;
                    }
                    message.url = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return { url: isSet(object.url) ? globalThis.String(object.url) : "" };
    },
    toJSON(message) {
        const obj = {};
        if (message.url !== "") {
            obj.url = message.url;
        }
        return obj;
    },
    create(base) {
        return exports.UserImageBadgeImage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseUserImageBadgeImage();
        message.url = object.url ?? "";
        return message;
    },
};
function createBaseWebcastWebsocketMessage() {
    return { id: "0", type: "", binary: new Uint8Array(0) };
}
exports.WebcastWebsocketMessage = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.id !== "0") {
            writer.uint32(16).uint64(message.id);
        }
        if (message.type !== "") {
            writer.uint32(58).string(message.type);
        }
        if (message.binary.length !== 0) {
            writer.uint32(66).bytes(message.binary);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastWebsocketMessage();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.id = reader.uint64().toString();
                    continue;
                }
                case 7: {
                    if (tag !== 58) {
                        break;
                    }
                    message.type = reader.string();
                    continue;
                }
                case 8: {
                    if (tag !== 66) {
                        break;
                    }
                    message.binary = reader.bytes();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? globalThis.String(object.id) : "0",
            type: isSet(object.type) ? globalThis.String(object.type) : "",
            binary: isSet(object.binary) ? bytesFromBase64(object.binary) : new Uint8Array(0),
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.id !== "0") {
            obj.id = message.id;
        }
        if (message.type !== "") {
            obj.type = message.type;
        }
        if (message.binary.length !== 0) {
            obj.binary = base64FromBytes(message.binary);
        }
        return obj;
    },
    create(base) {
        return exports.WebcastWebsocketMessage.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastWebsocketMessage();
        message.id = object.id ?? "0";
        message.type = object.type ?? "";
        message.binary = object.binary ?? new Uint8Array(0);
        return message;
    },
};
function createBaseWebcastWebsocketAck() {
    return { id: "0", type: "" };
}
exports.WebcastWebsocketAck = {
    encode(message, writer = new wire_1.BinaryWriter()) {
        if (message.id !== "0") {
            writer.uint32(16).uint64(message.id);
        }
        if (message.type !== "") {
            writer.uint32(58).string(message.type);
        }
        return writer;
    },
    decode(input, length) {
        const reader = input instanceof wire_1.BinaryReader ? input : new wire_1.BinaryReader(input);
        let end = length === undefined ? reader.len : reader.pos + length;
        const message = createBaseWebcastWebsocketAck();
        while (reader.pos < end) {
            const tag = reader.uint32();
            switch (tag >>> 3) {
                case 2: {
                    if (tag !== 16) {
                        break;
                    }
                    message.id = reader.uint64().toString();
                    continue;
                }
                case 7: {
                    if (tag !== 58) {
                        break;
                    }
                    message.type = reader.string();
                    continue;
                }
            }
            if ((tag & 7) === 4 || tag === 0) {
                break;
            }
            reader.skip(tag & 7);
        }
        return message;
    },
    fromJSON(object) {
        return {
            id: isSet(object.id) ? globalThis.String(object.id) : "0",
            type: isSet(object.type) ? globalThis.String(object.type) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.id !== "0") {
            obj.id = message.id;
        }
        if (message.type !== "") {
            obj.type = message.type;
        }
        return obj;
    },
    create(base) {
        return exports.WebcastWebsocketAck.fromPartial(base ?? {});
    },
    fromPartial(object) {
        const message = createBaseWebcastWebsocketAck();
        message.id = object.id ?? "0";
        message.type = object.type ?? "";
        return message;
    },
};
function bytesFromBase64(b64) {
    if (globalThis.Buffer) {
        return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
    }
    else {
        const bin = globalThis.atob(b64);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; ++i) {
            arr[i] = bin.charCodeAt(i);
        }
        return arr;
    }
}
function base64FromBytes(arr) {
    if (globalThis.Buffer) {
        return globalThis.Buffer.from(arr).toString("base64");
    }
    else {
        const bin = [];
        arr.forEach((byte) => {
            bin.push(globalThis.String.fromCharCode(byte));
        });
        return globalThis.btoa(bin.join(""));
    }
}
function isSet(value) {
    return value !== null && value !== undefined;
}
//# sourceMappingURL=tiktok-schema.js.map