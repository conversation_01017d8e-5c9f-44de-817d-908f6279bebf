{"name": "tiktok-live-connector", "version": "2.0.3", "description": "Node.js library to receive live stream chat events like comments and gifts from TikTok LIVE.", "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=10.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/zerodytrash/TikTok-Live-Connector.git"}, "funding": ["https://buymeacoffee.com/isaackogan", "https://buymeacoffee.com/zerody"], "keywords": ["tiktok", "tiktok live", "tiktok.com", "live", "stream", "livestream", "chat", "connector", "api", "webcast", "tracker", "scraper", "websocket", "bot", "broadcast", "reader"], "author": "zerodytrash", "license": "MIT", "bugs": {"url": "https://github.com/zerodytrash/TikTok-Live-Connector/issues"}, "homepage": "https://github.com/zerodytrash/TikTok-Live-Connector#readme", "dependencies": {"@bufbuild/protobuf": "^2.2.5", "@eulerstream/euler-api-sdk": "^0.0.7", "axios": "^1.9.0", "callable-instance": "^2.0.0", "protobufjs": "^6.11.2", "websocket": "^1.0.35", "typed-emitter": "^2.1.0", "ts-proto": "^2.6.1"}, "devDependencies": {"@babel/cli": "^7.17.0", "@babel/core": "^7.17.2", "@babel/preset-env": "^7.16.11", "@types/node": "^22.15.3", "@types/websocket": "^1.0.10", "@types/ws": "^8.18.1", "cpx": "^1.5.0", "dotenv": "^16.5.0", "prettier": "2.5.1", "tsc-alias": "^1.8.15", "typescript": "^4.8.2"}, "scripts": {"build": "tsx update-version.ts && npm run build:copy-package && tsc && tsc-alias", "build:copy-package": "cpx package.json dist && cpx LICENSE dist"}}