/**
 * Copyright (C) 2016-2020 <PERSON>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
export declare function isUndefined(val: unknown): val is undefined;
export declare function isNull(val: unknown): val is null;
export declare function isObject(val: unknown): val is Record<string, unknown>;
export declare function isArray(val: unknown): val is unknown[];
export declare function isFunction(val: unknown): val is Function;
export declare function isSet(val: unknown): val is Set<unknown>;
export declare function isMap(val: unknown): val is Map<unknown, unknown>;
/**
 * Returns a string representation of the specified value, as given by the
 * value's toString() method (if it has one) or the global String() function
 * (if it does not).
 *
 * @param value The value to convert to a string.
 *
 * @returns A string representation of the specified value.
 */
export declare function stringify(value: any): string;
