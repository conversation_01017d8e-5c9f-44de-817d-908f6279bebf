{"name": "case-anything", "version": "2.1.13", "description": "camelCase, kebab-case, PascalCase... a simple integration with nano package size. (SMALL footprint!)", "type": "module", "sideEffects": false, "types": "./dist/index.d.ts", "module": "./dist/index.js", "main": "./dist/index.js", "exports": {".": {"require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "files": ["dist"], "engines": {"node": ">=12.13"}, "scripts": {"lint": "tsc --noEmit && eslint ./src --ext .ts", "test": "vitest run", "build": "rollup -c ./rollup.config.js", "release": "npm run lint && del dist && npm run build && np"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "del-cli": "^5.0.0", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.7.0", "prettier": "^2.8.8", "rollup": "^3.23.0", "rollup-plugin-dts": "^5.3.0", "rollup-plugin-esbuild": "^5.0.0", "typescript": "^4.9.5", "vitest": "^0.31.0"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/case-anything.git"}, "keywords": ["change-case", "change-casing", "case-change", "casing-change", "camel-case", "pascal-case", "kebab-case", "snake-case", "ada-case", "constant-case", "train-case", "cobol-case", "path-case", "dot-case", "camel", "pascal", "kebab", "snake", "constant", "path", "format-string"], "author": "<PERSON>", "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/case-anything/issues"}, "homepage": "https://github.com/mesqueeb/case-anything#readme", "np": {"yarn": false, "branch": "production"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}}