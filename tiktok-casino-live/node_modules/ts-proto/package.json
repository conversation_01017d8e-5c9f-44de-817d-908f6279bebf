{"name": "ts-proto", "version": "2.7.5", "description": "", "main": "build/src/plugin.js", "repository": "github:stephenh/ts-proto", "bin": {"protoc-gen-ts_proto": "./protoc-gen-ts_proto"}, "types": "build/src/index.d.ts", "scripts": {"build": "yarn tsc", "build:test": "yarn proto2ts && yarn proto2pbjs", "prepare": "yarn build", "proto2ts": "docker compose run --rm protoc update-code.sh", "proto2pbjs": "docker compose run --rm protoc pbjs.sh", "test": "yarn jest -c jest.config.js", "tsc:check": "./tsc-check.sh tsconfig.json tests/tsconfig.json integration/tsconfig.json integration/tsconfig.proto.json protos/tsconfig.json", "format": "prettier --write {src,tests}/**/*.ts integration/*.ts", "format:check": "prettier --list-different {src,tests}/**/*.ts", "setup:docker": "docker compose build", "watch": "tsx integration/watch.ts"}, "files": ["build"], "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@grpc/grpc-js": "^1.8.17", "@grpc/proto-loader": "^0.7.8", "@improbable-eng/grpc-web": "^0.14.1", "@improbable-eng/grpc-web-node-http-transport": "^0.14.1", "@nestjs/common": "^9.4.3", "@nestjs/core": "^9.4.3", "@nestjs/microservices": "^9.4.3", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^10.1.6", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.1", "@tsconfig/strictest": "^2.0.1", "@types/jest": "^29.5.3", "@types/node": "^16.18.38", "@types/object-hash": "^3.0.2", "chokidar": "^3.5.3", "conventional-changelog-conventionalcommits": "^8.0.0", "dataloader": "^2.2.2", "jest": "^29.6.1", "jest-ts-webcompat-resolver": "^1.0.0", "mongodb": "^5.7.0", "nano-date": "^4.1.0", "nice-grpc": "^2.1.4", "object-hash": "^3.0.0", "prettier": "^3.5.3", "protobufjs": "^7.2.4", "protobufjs-cli": "^1.1.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "semantic-release": "^24.0.0", "ts-jest": "^29.3.4", "tsx": "^4.19.4", "typescript": "^5.8.3", "uglify-js": "^3.17.4"}, "dependencies": {"@bufbuild/protobuf": "^2.0.0", "case-anything": "^2.1.13", "ts-poet": "^6.12.0", "ts-proto-descriptors": "2.0.0"}, "packageManager": "yarn@4.4.0"}