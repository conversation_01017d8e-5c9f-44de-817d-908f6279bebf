{"name": "callable-instance", "version": "2.0.0", "description": "Instances of classes which are directly callable as functions.", "repository": "CGamesPlay/node-callable-instance", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha -c -R progress"}, "keywords": ["instance", "function", "object", "class", "callable"], "bugs": {"url": "https://github.com/CGamesPlay/node-callable-instance/issues"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"mocha": "^8.0.1"}, "files": ["index.js", "index.d.ts", "LICENSE", "README.md"]}