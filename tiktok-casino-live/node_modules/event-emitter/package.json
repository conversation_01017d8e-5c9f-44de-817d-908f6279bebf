{"name": "event-emitter", "version": "0.3.5", "description": "Environment agnostic event emitter", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["event", "events", "trigger", "observer", "listener", "emitter", "pubsub"], "repository": {"type": "git", "url": "git://github.com/medikoo/event-emitter.git"}, "dependencies": {"es5-ext": "~0.10.14", "d": "1"}, "devDependencies": {"tad": "~0.2.7", "xlint": "~0.2.2", "xlint-jslint-medikoo": "~0.1.4"}, "scripts": {"lint": "node node_modules/xlint/bin/xlint --linter=node_modules/xlint-jslint-medikoo/index.js --no-cache --no-stream", "lint-console": "node node_modules/xlint/bin/xlint --linter=node_modules/xlint-jslint-medikoo/index.js --watch", "test": "node ./node_modules/tad/bin/tad"}, "license": "MIT"}