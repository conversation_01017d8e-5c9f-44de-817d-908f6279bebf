#!/bin/bash

echo "🎰🎬 TikTok Live Casino - Pornire Completă"
echo "=========================================="

# Verifică dacă toate componentele sunt disponibile
echo "🔍 Verificare componente..."

if [ ! -f "server.js" ]; then
    echo "❌ server.js nu a fost găsit!"
    exit 1
fi

if [ ! -f "ssl/cert.pem" ] || [ ! -f "ssl/key.pem" ]; then
    echo "❌ Certificate SSL nu au fost găsite!"
    echo "💡 Rulează: ./generate-ssl.sh"
    exit 1
fi

if ! command -v obs &> /dev/null; then
    echo "❌ OBS Studio nu este instalat!"
    exit 1
fi

echo "✅ Toate componentele sunt disponibile!"
echo ""

# Pornește serverul casino în background
echo "🎰 Pornesc TikTok Live Casino..."
node server.js &
CASINO_PID=$!
echo "✅ Casino pornit cu PID: $CASINO_PID"

# Așteaptă ca serverul să pornească
sleep 3

# Testează dacă serverul funcționează
if curl -k -s https://localhost:3443/ > /dev/null; then
    echo "✅ Casino server funcționează la https://localhost:3443"
else
    echo "❌ Casino server nu răspunde!"
    kill $CASINO_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🎬 Pornesc OBS Studio..."

# Pornește OBS Studio cu environment curat
env -i PATH=/usr/bin:/bin:/usr/local/bin DISPLAY=$DISPLAY HOME=$HOME USER=$USER obs &
OBS_PID=$!
echo "✅ OBS Studio pornit cu PID: $OBS_PID"

echo ""
echo "🎯 TOTUL ESTE GATA!"
echo "==================="
echo ""
echo "📱 Casino App: https://localhost:3443"
echo "🎬 OBS Studio: Pornit și gata de configurare"
echo ""
echo "📋 Pașii următori în OBS:"
echo "1. File → Settings → Stream"
echo "2. Service: Custom"
echo "3. Server: rtmp://live.tiktok.com/live/"
echo "4. Stream Key: [Obține de la TikTok]"
echo "5. Adaugă Browser Source:"
echo "   - URL: https://localhost:3443"
echo "   - Width: 1080, Height: 1920"
echo "6. Start Streaming!"
echo ""
echo "🔑 Pentru stream key, rulează:"
echo "   cd TikTokStreamKeyGenerator"
echo "   source venv/bin/activate"
echo "   python TikTokStreamKeyGenerator.py"
echo ""
echo "⚠️  Pentru a opri totul, apasă Ctrl+C"

# Funcție pentru cleanup la exit
cleanup() {
    echo ""
    echo "🛑 Opresc serviciile..."
    kill $CASINO_PID 2>/dev/null
    kill $OBS_PID 2>/dev/null
    echo "✅ Toate serviciile au fost oprite!"
    exit 0
}

# Capturează semnalele pentru cleanup
trap cleanup SIGINT SIGTERM

# Așteaptă până când utilizatorul oprește scriptul
echo "💡 Apasă Ctrl+C pentru a opri toate serviciile..."
wait
