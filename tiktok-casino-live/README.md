# 🎰🎬 TikTok Live Casino + OBS Studio

**Sistem complet pentru streaming casino live pe TikTok cu toate funcționalitățile integrate!**

## ✅ Ce este inclus:
- 🎰 **Casino Live complet** cu 4 jocuri (Rulet<PERSON>, Slots, Blackjack, Zaruri)
- 🎬 **OBS Studio 31.1.0** configurat pentru TikTok
- 🔒 **HTTPS securizat** cu certificate SSL
- 🔑 **TikTok Stream Key Generator**
- 🚀 **Script de pornire automată**
- 📋 **Ghiduri complete de configurare**

## 🚀 Pornire Rapidă (UN SINGUR CLICK!)

### Metoda 1: Pornire Automată (RECOMANDAT)
```bash
./start-tiktok-live.sh
```
**Asta e tot!** Scriptul pornește automat:
- ✅ Casino server la https://localhost:3443
- ✅ OBS Studio configurat pentru TikTok
- ✅ Toate setările optimizate

### Metoda 2: Pornire Manuală

#### 1. Pregătire
```bash
cd tiktok-casino-live
npm install
```

#### 2. Pornire aplicație

**Server HTTPS (recomandat pentru TikTok Live)**
```bash
node server.js
```
Aplicația va rula la `https://localhost:3443`

## 🎬 Configurare OBS Studio pentru TikTok

### Pasul 1: Setări Stream
1. **File** → **Settings** → **Stream**
2. **Service**: Custom
3. **Server**: `rtmp://live.tiktok.com/live/`
4. **Stream Key**: [Vezi secțiunea Stream Key mai jos]

### Pasul 2: Setări Video (Format TikTok Vertical)
1. **Settings** → **Video**
2. **Base Resolution**: 1080x1920
3. **Output Resolution**: 1080x1920
4. **FPS**: 30

### Pasul 3: Adaugă Casino ca Browser Source
1. **Sources** → **+** → **Browser**
2. **URL**: `https://localhost:3443`
3. **Width**: 1080, **Height**: 1920
4. **✅ Shutdown source when not visible**
5. **✅ Refresh browser when scene becomes active**

## 🔑 Obținere Stream Key pentru TikTok

### Metoda 1: TikTok Stream Key Generator (Inclus)
```bash
cd TikTokStreamKeyGenerator
source venv/bin/activate
python TikTokStreamKeyGenerator.py
```

### Metoda 2: TikTok Live Studio
1. Deschide TikTok Live Studio
2. Începe un live
3. Copiază stream key-ul generat

## 🎰 Conectare Casino la TikTok Live
1. **Deschide aplicația** la `https://localhost:3443`
2. **Acceptă certificatul SSL** (este normal să apară avertisment)
3. **Introdu username-ul tău TikTok** în câmpul de conectare
4. **Apasă "Conectează Live Casino"**

## 🎮 Funcționalități

### 💰 Sistem de Credite Automat
- **🎁 Gifts** = Credite (1 diamond = 10 credite)
- **👆 3000 tap-uri** = 50 credite
- **📤 300 share-uri** = 30 credite
- **❤️ Follow** = 200 credite bonus
- **👋 Intrare în live** = 100 credite de start

### 🎲 Jocuri de Casino
Vizitatorii pot juca scriind în chat:

1. **🎯 Ruletă** (10 credite)
   - Scrie: "ruletă" sau "roulette"
   - Câștig: 2.5x

2. **🎰 Slots** (5 credite)
   - Scrie: "slots" sau "păcănele"
   - Câștig: 3.0x

3. **🃏 Blackjack** (15 credite)
   - Scrie: "blackjack" sau "21"
   - Câștig: 2.0x

4. **🎲 Zaruri** (8 credite)
   - Scrie: "zaruri" sau "dice"
   - Câștig: 2.2x

### 🏆 Leaderboard în Timp Real
- Top 5 jucători cu cele mai multe credite
- Actualizare automată la fiecare interacțiune
- Afișare în aplicație și în chat

### 💬 Chat Live Integrat
- Toate mesajele din TikTok Live
- Notificări pentru câștiguri și credite
- Comenzi de joc prin chat

## 🔒 Configurare HTTPS

### De ce HTTPS?
- **TikTok Live API** necesită conexiuni securizate HTTPS
- **Browser-ele moderne** blochează multe funcții pe HTTP
- **Securitate îmbunătățită** pentru streaming live

### Generare Certificate SSL
```bash
./generate-ssl.sh
```
sau manual:
```bash
mkdir -p ssl
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes \
    -subj "/C=RO/ST=Romania/L=Bucharest/O=TikTok Casino/OU=Development/CN=localhost"
```

### Acceptare Certificate în Browser
1. Deschide `https://localhost:3443`
2. Browser-ul va afișa "Conexiune nesigură"
3. Apasă "Avansat" → "Continuă către localhost (nesigur)"
4. Certificatul va fi acceptat pentru sesiunea curentă

⚠️ **Notă**: Acestea sunt certificate self-signed pentru dezvoltare. Pentru producție, folosește certificate SSL valide.

## ⚙️ Configurare Avansată

### House Advantage
- **45% șanse de câștig** pentru jucători
- **55% house advantage** pentru profitabilitate
- Multiplicatori echilibrați pentru fiecare joc

### Personalizare
Poți modifica în `main.js`:
- Costurile jocurilor
- Multiplicatorii de câștig
- Creditele pentru acțiuni
- Mesajele de câștig/pierdere

## 🔧 Depanare

### Probleme de conectare
1. Verifică că username-ul TikTok este corect
2. Asigură-te că live-ul este activ
3. Verifică conexiunea la internet

### Aplicația nu pornește
```bash
npm install
npm run dev
```

### Erori în consolă
- Deschide Developer Tools (F12)
- Verifică tab-ul Console pentru erori
- Raportează erorile pentru suport

## 📱 Compatibilitate
- ✅ Chrome/Edge (recomandat)
- ✅ Firefox
- ✅ Safari
- ✅ Mobile browsers

## 🎯 Utilizare Optimă

### Pentru Streameri
1. Testează aplicația înainte de live
2. Explică vizitatorilor cum funcționează
3. Încurajează interacțiunea prin gifts și tap-uri
4. Monitorizează leaderboard-ul pentru engagement

### Pentru Vizitatori
1. Dă gifts pentru credite instant
2. Tap-ează pentru a acumula credite
3. Share live-ul pentru credite bonus
4. Scrie numele jocului în chat pentru a juca

## 🔒 Securitate
- Aplicația nu stochează date personale
- Conexiunea la TikTok este read-only
- Nu sunt necesare permisiuni speciale

## 📞 Suport
Pentru probleme sau întrebări, verifică:
1. Acest README
2. Consolele browser-ului pentru erori
3. Documentația TikTok Live Connector

---

**🎰 Distracție plăcută cu TikTok Live Casino!**
